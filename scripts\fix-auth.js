/**
 * Authentication Fix Script
 * 
 * This script helps fix authentication issues by:
 * 1. Clearing all stored tokens
 * 2. Refreshing the authentication session
 * 3. Storing the new token in sessionStorage
 * 
 * Run this script in the browser console when experiencing authentication issues.
 */

(async function() {
  console.log('Starting authentication fix...');
  
  // Step 1: Clear all stored tokens
  console.log('Clearing all stored tokens...');
  
  // Clear sessionStorage
  try {
    if (window.sessionStorage) {
      sessionStorage.removeItem('oss_auth_token_cache');
      sessionStorage.removeItem('oss_session');
      console.log('Cleared tokens from sessionStorage');
    }
  } catch (e) {
    console.error('Error clearing sessionStorage:', e);
  }
  
  // Clear localStorage (legacy storage)
  try {
    if (window.localStorage) {
      localStorage.removeItem('oss_auth_token');
      localStorage.removeItem('sb_auth_token');
      console.log('Cleared tokens from localStorage');
    }
  } catch (e) {
    console.error('Error clearing localStorage:', e);
  }
  
  // Clear cookies
  try {
    document.cookie = 'oss_auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = 'sb_auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    console.log('Cleared tokens from cookies');
  } catch (e) {
    console.error('Error clearing cookies:', e);
  }
  
  // Step 2: Refresh the authentication session
  console.log('Refreshing authentication session...');
  
  try {
    // Import the API client
    const apiClient = await import('/lib/api-client.js').then(module => module.default);
    
    if (!apiClient) {
      throw new Error('Failed to import API client');
    }
    
    // Refresh the token
    const token = await apiClient.refreshAuthToken();
    
    if (!token) {
      throw new Error('Failed to refresh token');
    }
    
    console.log('Successfully refreshed authentication token');
    
    // Step 3: Verify the token is stored correctly
    try {
      const cachedToken = sessionStorage.getItem('oss_auth_token_cache');
      if (cachedToken) {
        const tokenData = JSON.parse(cachedToken);
        if (tokenData && tokenData.token) {
          console.log('Token successfully stored in sessionStorage');
          console.log('Token expiry:', new Date(tokenData.expiry).toLocaleString());
          
          // Extract token info
          const tokenParts = tokenData.token.split('.');
          if (tokenParts.length === 3) {
            try {
              const payload = JSON.parse(atob(tokenParts[1]));
              console.log('Token payload:', {
                sub: payload.sub,
                exp: new Date(payload.exp * 1000).toLocaleString(),
                iat: new Date(payload.iat * 1000).toLocaleString()
              });
            } catch (e) {
              console.error('Error parsing token payload:', e);
            }
          }
        } else {
          console.warn('Token data is invalid');
        }
      } else {
        console.warn('No token found in sessionStorage after refresh');
      }
    } catch (e) {
      console.error('Error verifying token storage:', e);
    }
    
    console.log('Authentication fix complete!');
    console.log('Please refresh the page and try accessing the admin panel again.');
  } catch (error) {
    console.error('Authentication fix failed:', error);
    console.log('Please try logging out and logging back in.');
  }
})();

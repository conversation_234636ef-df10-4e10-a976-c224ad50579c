.reportExporter {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.reportExporter h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.exportForm {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

@media (min-width: 576px) {
  .exportForm {
    grid-template-columns: repeat(2, 1fr) auto;
    align-items: end;
  }
}

.formGroup {
  margin-bottom: 0;
}

.formGroup label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.formControl {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.formControl:focus {
  border-color: #4a90e2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.exportButton {
  padding: 10px 20px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  height: 40px;
}

.exportButton:hover {
  background-color: #3a7bc8;
}

.exportButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.reportDescription {
  font-size: 14px;
  color: #666;
  font-style: italic;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

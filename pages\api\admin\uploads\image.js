import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Disable the default body parser to handle form data
export const config = {
  api: {
    bodyParser: false,
  },
};

/**
 * API endpoint for image uploads
 * 
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Authenticate request
  const { authorized, error, user } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Parse form data
    const form = new formidable.IncomingForm({
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
    });

    const { fields, files } = await new Promise((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) reject(err);
        resolve({ fields, files });
      });
    });

    // Check if image file exists
    if (!files.image) {
      return res.status(400).json({ error: 'No image file provided' });
    }

    const file = files.image;
    
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
      return res.status(400).json({ error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' });
    }

    // Read file
    const fileData = fs.readFileSync(file.filepath);
    
    // Generate unique filename
    const fileExt = path.extname(file.originalFilename);
    const fileName = `${uuidv4()}${fileExt}`;
    
    // Upload to Supabase Storage
    const { data, error: uploadError } = await supabaseAdmin
      .storage
      .from('product-images')
      .upload(`uploads/${fileName}`, fileData, {
        contentType: file.mimetype,
        cacheControl: '3600',
        upsert: false
      });
    
    if (uploadError) {
      console.error('Error uploading to Supabase:', uploadError);
      return res.status(500).json({ error: 'Failed to upload image to storage' });
    }
    
    // Get public URL
    const { data: { publicUrl } } = supabaseAdmin
      .storage
      .from('product-images')
      .getPublicUrl(`uploads/${fileName}`);
    
    // Return success response
    return res.status(200).json({
      success: true,
      url: publicUrl,
      fileName: fileName
    });
  } catch (err) {
    console.error('Error handling image upload:', err);
    return res.status(500).json({ error: 'Failed to process image upload' });
  }
}

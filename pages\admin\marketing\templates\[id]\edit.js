import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import AdminLayout from '@/components/admin/AdminLayout'
import TemplateEditor from '@/components/admin/marketing/TemplateEditor'
import styles from '@/styles/admin/marketing/TemplateCreate.module.css'

export default function EditTemplate() {
  const router = useRouter()
  const { id } = router.query
  const [template, setTemplate] = useState(null)
  const [loading, setLoading] = useState(false)
  const [fetchLoading, setFetchLoading] = useState(true)
  const [error, setError] = useState(null)
  const [successMessage, setSuccessMessage] = useState(null)

  // Fetch template data
  useEffect(() => {
    if (!id) return

    const fetchTemplate = async () => {
      setFetchLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/marketing/templates/${id}`)
        
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch template')
        }

        const data = await response.json()
        setTemplate(data.template || null)
      } catch (error) {
        console.error('Error fetching template:', error)
        setError(error.message)
      } finally {
        setFetchLoading(false)
      }
    }

    fetchTemplate()
  }, [id])

  // Handle form submission
  const handleSubmit = async (templateData) => {
    setLoading(true)
    setError(null)
    setSuccessMessage(null)

    try {
      // Update template
      const response = await fetch(`/api/marketing/templates/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(templateData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update template')
      }

      const data = await response.json()
      setSuccessMessage('Template updated successfully')

      // Redirect to template detail page after a short delay
      setTimeout(() => {
        router.push(`/admin/marketing/templates/${id}`)
      }, 1500)
    } catch (error) {
      console.error('Error updating template:', error)
      setError(error.message)
      setLoading(false)
    }
  }

  if (fetchLoading) {
    return (
      <AdminLayout>
        <div className={styles.templateCreate}>
          <div className={styles.loading}>Loading template data...</div>
        </div>
      </AdminLayout>
    )
  }

  if (!template && !fetchLoading) {
    return (
      <AdminLayout>
        <div className={styles.templateCreate}>
          <div className={styles.error}>
            Template not found or you don't have permission to edit it.
          </div>
          <button
            className={styles.backButton}
            onClick={() => router.push('/admin/marketing/templates')}
          >
            Back to Templates
          </button>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className={styles.templateCreate}>
        <div className={styles.header}>
          <h2>Edit Message Template</h2>
          <button
            className={styles.cancelButton}
            onClick={() => router.push(`/admin/marketing/templates/${id}`)}
            disabled={loading}
          >
            Cancel
          </button>
        </div>

        {error && (
          <div className={styles.error}>
            Error: {error}
          </div>
        )}

        {successMessage && (
          <div className={styles.success}>
            {successMessage}
          </div>
        )}

        <TemplateEditor
          initialTemplate={template}
          onSave={handleSubmit}
          onCancel={() => router.push(`/admin/marketing/templates/${id}`)}
        />
      </div>
    </AdminLayout>
  )
}

.promptContainer {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.prompt {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 20px;
  max-width: 320px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.closeButton {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 20px;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.closeButton:hover {
  color: #343a40;
}

.promptIcon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #6a0dad;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  color: white;
}

.promptContent {
  text-align: center;
  margin-bottom: 15px;
}

.promptContent h3 {
  margin: 0 0 8px;
  font-size: 1.1rem;
  color: #333;
}

.promptContent p {
  margin: 0;
  font-size: 0.9rem;
  color: #6c757d;
  line-height: 1.4;
}

.promptActions {
  display: flex;
  gap: 10px;
  width: 100%;
}

.laterButton {
  flex: 1;
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #ced4da;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.laterButton:hover {
  background-color: #e9ecef;
}

.subscribeButton {
  flex: 1;
  background-color: #6a0dad;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.subscribeButton:hover {
  background-color: #5a0b9d;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .promptContainer {
    left: 10px;
    right: 10px;
    bottom: 10px;
  }
  
  .prompt {
    max-width: none;
    width: 100%;
  }
}

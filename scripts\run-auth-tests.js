/**
 * Authentication Test Runner
 *
 * This script runs the API authentication tests defined in the admin-auth-test-script.md document.
 * It uses the utility functions from utils/auth-test-utils.js to make authenticated API requests
 * and validate the responses.
 *
 * Usage:
 * node scripts/run-auth-tests.js --token=<your_auth_token>
 */

// Import required dependencies
import {
  makeAuthenticatedRequest,
  makeXAuthTokenRequest,
  createExpiredToken,
  testAllAuthEndpoints,
  generateTestReport
} from '../utils/auth-test-utils.js';
import fs from 'fs';
import path from 'path';

// Parse command line arguments
const args = process.argv.slice(2);
const tokenArg = args.find(arg => arg.startsWith('--token='));
const mockArg = args.find(arg => arg === '--mock');
const isMockTest = !!mockArg;

// Get token from arguments or file
let token = tokenArg ? tokenArg.split('=')[1] : null;

// If no token provided and mock testing is enabled, use mock token
if (!token && isMockTest) {
  try {
    const mockTokenPath = path.join(process.cwd(), 'test-results', 'mock-auth-token.txt');
    if (fs.existsSync(mockTokenPath)) {
      token = fs.readFileSync(mockTokenPath, 'utf8').trim();
      console.log('Using mock token for testing');
    }
  } catch (error) {
    console.error('Error reading mock token:', error);
  }
}

// Set base URL
const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

// Main function to run tests
async function runTests() {
  if (!token) {
    console.error('Error: No authentication token provided.');
    console.error('Usage: node scripts/run-auth-tests.js --token=<your_auth_token>');
    process.exit(1);
  }

  console.log('Running API Authentication Tests...');
  console.log(`Base URL: ${baseUrl}`);

  // Create test results directory if it doesn't exist
  const resultsDir = path.join(process.cwd(), 'test-results');
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir);
  }

  // Test results object
  const testResults = {
    timestamp: new Date().toISOString(),
    tests: {}
  };

  // API-01: Valid Admin Token Test
  try {
    console.log('\nRunning API-01: Valid Admin Token Test...');
    const result = await makeAuthenticatedRequest(
      `${baseUrl}/api/admin/diagnostics/auth-check`,
      token
    );

    const success = result.status === 200 && result.data && result.data.user;

    testResults.tests['API-01'] = {
      name: 'Valid Admin Token Test',
      status: success ? 'Pass' : 'Fail',
      details: {
        statusCode: result.status,
        response: result.data
      }
    };

    console.log(`API-01 Result: ${success ? 'Pass' : 'Fail'}`);
    console.log(`Status Code: ${result.status}`);
    console.log('Response:', result.data);
  } catch (error) {
    console.error('Error running API-01:', error);
    testResults.tests['API-01'] = {
      name: 'Valid Admin Token Test',
      status: 'Error',
      details: {
        error: error.message
      }
    };
  }

  // API-02: Expired Token Test
  try {
    console.log('\nRunning API-02: Expired Token Test...');
    const expiredToken = createExpiredToken(token);
    const result = await makeAuthenticatedRequest(
      `${baseUrl}/api/admin/diagnostics/auth-check`,
      expiredToken
    );

    const success = result.status === 401;

    testResults.tests['API-02'] = {
      name: 'Expired Token Test',
      status: success ? 'Pass' : 'Fail',
      details: {
        statusCode: result.status,
        response: result.data
      }
    };

    console.log(`API-02 Result: ${success ? 'Pass' : 'Fail'}`);
    console.log(`Status Code: ${result.status}`);
    console.log('Response:', result.data);
  } catch (error) {
    console.error('Error running API-02:', error);
    testResults.tests['API-02'] = {
      name: 'Expired Token Test',
      status: 'Error',
      details: {
        error: error.message
      }
    };
  }

  // API-03: No Token Test
  try {
    console.log('\nRunning API-03: No Token Test...');
    const result = await makeAuthenticatedRequest(
      `${baseUrl}/api/admin/diagnostics/auth-check`,
      ''
    );

    const success = result.status === 401;

    testResults.tests['API-03'] = {
      name: 'No Token Test',
      status: success ? 'Pass' : 'Fail',
      details: {
        statusCode: result.status,
        response: result.data
      }
    };

    console.log(`API-03 Result: ${success ? 'Pass' : 'Fail'}`);
    console.log(`Status Code: ${result.status}`);
    console.log('Response:', result.data);
  } catch (error) {
    console.error('Error running API-03:', error);
    testResults.tests['API-03'] = {
      name: 'No Token Test',
      status: 'Error',
      details: {
        error: error.message
      }
    };
  }

  // API-04: Valid Admin Token for Customer Data
  try {
    console.log('\nRunning API-04: Valid Admin Token for Customer Data...');
    const result = await makeAuthenticatedRequest(
      `${baseUrl}/api/admin/customers`,
      token
    );

    const success = result.status === 200 && Array.isArray(result.data);

    testResults.tests['API-04'] = {
      name: 'Valid Admin Token for Customer Data',
      status: success ? 'Pass' : 'Fail',
      details: {
        statusCode: result.status,
        responseType: Array.isArray(result.data) ? 'Array' : typeof result.data
      }
    };

    console.log(`API-04 Result: ${success ? 'Pass' : 'Fail'}`);
    console.log(`Status Code: ${result.status}`);
    console.log(`Response Type: ${Array.isArray(result.data) ? 'Array' : typeof result.data}`);
  } catch (error) {
    console.error('Error running API-04:', error);
    testResults.tests['API-04'] = {
      name: 'Valid Admin Token for Customer Data',
      status: 'Error',
      details: {
        error: error.message
      }
    };
  }

  // API-06: Valid Admin Token for Settings
  try {
    console.log('\nRunning API-06: Valid Admin Token for Settings...');
    const result = await makeAuthenticatedRequest(
      `${baseUrl}/api/admin/settings`,
      token
    );

    const success = result.status === 200 && result.data;

    testResults.tests['API-06'] = {
      name: 'Valid Admin Token for Settings',
      status: success ? 'Pass' : 'Fail',
      details: {
        statusCode: result.status,
        responseType: typeof result.data
      }
    };

    console.log(`API-06 Result: ${success ? 'Pass' : 'Fail'}`);
    console.log(`Status Code: ${result.status}`);
    console.log(`Response Type: ${typeof result.data}`);
  } catch (error) {
    console.error('Error running API-06:', error);
    testResults.tests['API-06'] = {
      name: 'Valid Admin Token for Settings',
      status: 'Error',
      details: {
        error: error.message
      }
    };
  }

  // X-Auth-Token Test
  try {
    console.log('\nRunning X-Auth-Token Test...');
    const result = await makeXAuthTokenRequest(
      `${baseUrl}/api/admin/diagnostics/auth-check`,
      token
    );

    const success = result.status === 200 && result.data && result.data.user;

    testResults.tests['X-Auth-Token'] = {
      name: 'X-Auth-Token Header Test',
      status: success ? 'Pass' : 'Fail',
      details: {
        statusCode: result.status,
        response: result.data
      }
    };

    console.log(`X-Auth-Token Test Result: ${success ? 'Pass' : 'Fail'}`);
    console.log(`Status Code: ${result.status}`);
    console.log('Response:', result.data);
  } catch (error) {
    console.error('Error running X-Auth-Token Test:', error);
    testResults.tests['X-Auth-Token'] = {
      name: 'X-Auth-Token Header Test',
      status: 'Error',
      details: {
        error: error.message
      }
    };
  }

  // Save test results
  const timestamp = new Date().toISOString().replace(/:/g, '-');
  const resultsPath = path.join(resultsDir, `auth-test-results-${timestamp}.json`);
  fs.writeFileSync(resultsPath, JSON.stringify(testResults, null, 2));

  console.log(`\nTest results saved to ${resultsPath}`);

  // Generate summary
  let summary = '\nTest Summary:\n';
  let passCount = 0;
  let failCount = 0;
  let errorCount = 0;

  for (const [testId, test] of Object.entries(testResults.tests)) {
    summary += `${testId}: ${test.status}\n`;
    if (test.status === 'Pass') passCount++;
    else if (test.status === 'Fail') failCount++;
    else errorCount++;
  }

  summary += `\nTotal: ${Object.keys(testResults.tests).length} tests\n`;
  summary += `Passed: ${passCount} tests\n`;
  summary += `Failed: ${failCount} tests\n`;
  summary += `Errors: ${errorCount} tests\n`;

  console.log(summary);
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
  process.exit(1);
});

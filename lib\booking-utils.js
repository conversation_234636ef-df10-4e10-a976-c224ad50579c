import supabase from '@/lib/supabase';

/**
 * Check for booking conflicts
 * @param {Object} bookingData - Booking data
 * @param {string} bookingData.start_time - Start time
 * @param {string} bookingData.end_time - End time
 * @param {string} bookingData.location - Location
 * @param {string} [existingBookingId] - ID of existing booking (for updates)
 * @returns {Promise<Array>} - Array of conflicting bookings
 */
export const checkBookingConflicts = async (bookingData, existingBookingId = null) => {
  const { start_time, end_time, location } = bookingData;

  // Query Supabase for overlapping bookings
  const { data, error } = await supabase
    .from('bookings')
    .select('id, customer_id, service_id, start_time, end_time, status, location, customers(name, email, phone)')
    .neq('status', 'canceled')
    .eq('location', location)
    .or(`start_time.lt.${end_time},end_time.gt.${start_time}`)

  if (error) throw error;

  // Filter out the current booking if editing
  const conflicts = data.filter(booking => booking.id !== existingBookingId);

  return conflicts;
};

/**
 * Move a booking to a new time
 * @param {string} bookingId - Booking ID
 * @param {Date} newStartTime - New start time
 * @param {Date} newEndTime - New end time
 * @param {string} [newStatus] - Optional new status
 * @returns {Promise<Object>} - Updated booking
 */
export const moveBooking = async (bookingId, newStartTime, newEndTime, newStatus = null) => {
  try {
    // Use the API endpoint for moving bookings
    const response = await fetch('/api/admin/bookings/move', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: bookingId,
        start_time: newStartTime.toISOString(),
        end_time: newEndTime.toISOString(),
        status: newStatus
      }),
    });

    // Handle non-200 responses
    if (!response.ok) {
      const errorData = await response.json();

      // If there are conflicts, provide more detailed error
      if (response.status === 409 && errorData.conflicts) {
        const conflictCount = errorData.conflicts.length;
        const conflictNames = errorData.conflicts
          .map(c => c.customers?.name || 'Unknown')
          .join(', ');

        throw new Error(`Booking conflicts with ${conflictCount} other booking(s): ${conflictNames}`);
      }

      throw new Error(errorData.error || 'Failed to move booking');
    }

    // Parse and return the updated booking
    const { booking } = await response.json();
    return booking;
  } catch (error) {
    console.error('Error in moveBooking:', error);
    throw error;
  }
};

/**
 * Calculate duration between two dates in minutes
 * @param {Date} startTime - Start time
 * @param {Date} endTime - End time
 * @returns {number} - Duration in minutes
 */
export const calculateDuration = (startTime, endTime) => {
  return Math.round((endTime - startTime) / (1000 * 60));
};

/**
 * Calculate end time based on start time and duration
 * @param {Date} startTime - Start time
 * @param {number} durationMinutes - Duration in minutes
 * @returns {Date} - End time
 */
export const calculateEndTime = (startTime, durationMinutes) => {
  const endTime = new Date(startTime);
  endTime.setMinutes(endTime.getMinutes() + durationMinutes);
  return endTime;
};

/**
 * Format date for display
 * @param {Date|string} date - Date to format
 * @param {Object} options - Format options
 * @returns {string} - Formatted date
 */
export const formatDate = (date, options = {}) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  const defaultOptions = {
    weekday: 'short',
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  };

  return dateObj.toLocaleDateString('en-AU', { ...defaultOptions, ...options });
};

/**
 * Format time for display
 * @param {Date|string} date - Date to format
 * @param {Object} options - Format options
 * @returns {string} - Formatted time
 */
export const formatTime = (date, options = {}) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  const defaultOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  };

  return dateObj.toLocaleTimeString('en-AU', { ...defaultOptions, ...options });
};

/**
 * Format date and time for display
 * @param {Date|string} date - Date to format
 * @param {Object} options - Format options
 * @returns {string} - Formatted date and time
 */
export const formatDateTime = (date, options = {}) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  const defaultOptions = {
    weekday: 'short',
    day: 'numeric',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  };

  return dateObj.toLocaleString('en-AU', { ...defaultOptions, ...options });
};

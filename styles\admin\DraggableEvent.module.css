.draggableEvent {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.dragging {
  opacity: 0.5;
}

.over {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.eventTitle {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.eventStatus {
  font-size: 0.75rem;
  margin-top: 2px;
  opacity: 0.9;
}

/* Add a subtle animation for hover effect */
.draggableEvent:hover {
  transform: scale(1.02);
  transition: transform 0.1s ease-in-out;
  z-index: 10;
}

/* Add a subtle animation for active dragging */
.dragging {
  transform: scale(1.05);
  transition: transform 0.1s ease-in-out;
  z-index: 20;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

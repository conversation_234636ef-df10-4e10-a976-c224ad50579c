import { getAdminClient, getCurrentUserFromRequest, getClient } from '@/lib/supabase'
import { sendOneSignalEmail, sendOneSignalPush } from '@/lib/notifications'

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Check authentication
  try {
    const { user, role } = await getCurrentUserFromRequest(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  const { id: campaignId, messageId } = req.query
  const { test_mode = false } = req.body

  try {
    const client = getClient();
    if (!client) {
      return res.status(500).json({ error: 'Database connection failed' });
    }

    // Get campaign message
    const { data: message, error: messageError } = await client
      .from('campaign_messages')
      .select('*')
      .eq('id', messageId)
      .eq('campaign_id', campaignId)
      .single()

    if (messageError) {
      throw new Error(`Error fetching message: ${messageError.message}`)
    }

    if (!message) {
      return res.status(404).json({ error: 'Message not found' })
    }

    // Check if message is already sent
    if (message.status === 'sent' && !test_mode) {
      return res.status(400).json({ error: 'Message has already been sent' })
    }

    // Get campaign
    const { data: campaign, error: campaignError } = await client
      .from('marketing_campaigns')
      .select('*, target_segment:customer_segments (*)')
      .eq('id', campaignId)
      .single()

    if (campaignError) {
      throw new Error(`Error fetching campaign: ${campaignError.message}`)
    }

    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' })
    }

    // Check if campaign is active
    if (campaign.status !== 'active' && !test_mode) {
      return res.status(400).json({ error: `Cannot send messages for a ${campaign.status} campaign` })
    }

    // Get customers in segment
    let customers = []

    if (test_mode) {
      // In test mode, just get a few customers for testing
      const { data: testCustomers, error: testCustomersError } = await client
        .from('customers')
        .select('id, name, email, phone')
        .eq('marketing_consent', true)
        .limit(3)

      if (testCustomersError) {
        throw new Error(`Error fetching test customers: ${testCustomersError.message}`)
      }

      customers = testCustomers || []
    } else {
      // Get segment preview
      const segmentResponse = await fetch(
        `${process.env.NEXT_PUBLIC_SITE_URL}/api/marketing/segments/${campaign.target_segment.id}?action=preview`,
        { method: 'POST' }
      )

      if (!segmentResponse.ok) {
        const errorData = await segmentResponse.json()
        throw new Error(`Error previewing segment: ${errorData.error}`)
      }

      const segmentData = await segmentResponse.json()
      customers = segmentData.customers || []

      // Filter customers by marketing consent
      customers = customers.filter(customer => customer.marketing_consent)
    }

    if (customers.length === 0) {
      return res.status(400).json({
        error: 'No customers with marketing consent found in this segment',
        test_mode: test_mode
      })
    }

    // Send messages based on message type
    let sentCount = 0

    if (message.message_type === 'email') {
      // Send email to each customer
      for (const customer of customers) {
        if (!customer.email) continue

        try {
          // Replace personalization tokens in subject and content
          const personalizedSubject = personalizeText(message.subject, customer)
          const personalizedContent = personalizeText(message.content, customer)

          // Send email via OneSignal
          await sendOneSignalEmail({
            email: customer.email,
            subject: personalizedSubject,
            message: personalizedContent,
            htmlBody: personalizedContent,
            data: {
              type: 'marketing_campaign',
              campaign_id: campaignId,
              message_id: messageId
            }
          })

          sentCount++
        } catch (error) {
          console.error(`Error sending email to ${customer.email}:`, error)
          // Continue with other customers
        }
      }
    } else if (message.message_type === 'sms') {
      // Send SMS to each customer
      for (const customer of customers) {
        if (!customer.phone) continue

        try {
          // Replace personalization tokens in content
          const personalizedContent = personalizeText(message.content, customer)

          // In a real implementation, this would use an SMS service
          // For now, we'll just log it
          console.log(`SMS would be sent to ${customer.phone}:`, personalizedContent)

          sentCount++
        } catch (error) {
          console.error(`Error sending SMS to ${customer.phone}:`, error)
          // Continue with other customers
        }
      }
    } else if (message.message_type === 'push') {
      // Send push notification to each customer
      try {
        // Replace personalization tokens with generic values for push
        const genericSubject = personalizeText(message.subject, { name: 'Customer' })
        const genericContent = personalizeText(message.content, { name: 'Customer' })

        // Send push notification via OneSignal
        await sendOneSignalPush({
          userIds: customers.map(customer => customer.id),
          title: genericSubject,
          message: genericContent,
          data: {
            type: 'marketing_campaign',
            campaign_id: campaignId,
            message_id: messageId
          }
        })

        sentCount = customers.length
      } catch (error) {
        console.error('Error sending push notifications:', error)
      }
    }

    // Update message status if not in test mode
    if (!test_mode) {
      await client
        .from('campaign_messages')
        .update({
          status: 'sent',
          sent_date: new Date(),
          updated_at: new Date()
        })
        .eq('id', messageId)

      // Record metrics
      await client
        .from('campaign_metrics')
        .insert([
          {
            campaign_id: campaignId,
            metric_type: 'sent',
            metric_value: sentCount
          }
        ])
    }

    return res.status(200).json({
      success: true,
      sent: sentCount,
      total_customers: customers.length,
      test_mode: test_mode
    })
  } catch (error) {
    console.error('Error sending campaign message:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Helper function to replace personalization tokens in text
function personalizeText(text, customer) {
  if (!text) return ''

  return text
    .replace(/\{name\}/g, customer.name || 'Customer')
    .replace(/\{first_name\}/g, customer.name ? customer.name.split(' ')[0] : 'Customer')
    .replace(/\{email\}/g, customer.email || '')
    .replace(/\{phone\}/g, customer.phone || '')
    .replace(/\{city\}/g, customer.city || '')
    .replace(/\{state\}/g, customer.state || '')
}

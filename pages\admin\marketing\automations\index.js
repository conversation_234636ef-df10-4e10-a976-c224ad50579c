import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import styles from '@/styles/admin/marketing/AutomationList.module.css'
import { debounce } from 'lodash'

export default function AutomationList() {
  const router = useRouter()
  const [automations, setAutomations] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [search, setSearch] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [filters, setFilters] = useState({
    trigger_type: '',
    message_type: '',
    is_active: ''
  })
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0
  })
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(null)

  // Initialize filters from URL query params
  useEffect(() => {
    if (router.query.trigger_type) {
      setFilters(prev => ({ ...prev, trigger_type: router.query.trigger_type }))
    }
    if (router.query.message_type) {
      setFilters(prev => ({ ...prev, message_type: router.query.message_type }))
    }
    if (router.query.is_active) {
      setFilters(prev => ({ ...prev, is_active: router.query.is_active }))
    }
  }, [router.query])

  // Debounce search input
  const debouncedSetSearch = useCallback(
    debounce((value) => {
      setDebouncedSearch(value)
    }, 500),
    []
  )

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearch(e.target.value)
    debouncedSetSearch(e.target.value)
  }

  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target
    setFilters(prev => ({ ...prev, [name]: value }))
  }

  // Handle sort change
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  // Render sort indicator
  const renderSortIndicator = (field) => {
    if (sortBy !== field) return null
    return sortOrder === 'asc' ? ' ↑' : ' ↓'
  }

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPagination({ ...pagination, page: newPage })
  }

  // Fetch automations
  const fetchAutomations = async () => {
    setLoading(true)
    setError(null)

    try {
      const offset = (pagination.page - 1) * pagination.limit
      const queryParams = new URLSearchParams({
        limit: pagination.limit,
        offset,
        sort_by: sortBy,
        sort_order: sortOrder
      })

      if (debouncedSearch) {
        queryParams.append('search', debouncedSearch)
      }

      if (filters.trigger_type) {
        queryParams.append('trigger_type', filters.trigger_type)
      }

      if (filters.message_type) {
        queryParams.append('message_type', filters.message_type)
      }

      if (filters.is_active) {
        queryParams.append('is_active', filters.is_active)
      }

      const response = await fetch(`/api/marketing/automations?${queryParams.toString()}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch automations')
      }

      const data = await response.json()
      setAutomations(data.automations || [])
      setPagination({ ...pagination, total: data.total || 0 })
    } catch (error) {
      console.error('Error fetching automations:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  // Fetch automations when dependencies change
  useEffect(() => {
    fetchAutomations()
  }, [debouncedSearch, filters, sortBy, sortOrder, pagination.page, pagination.limit])

  // Handle toggle automation status
  const handleToggleStatus = async (automationId, currentStatus) => {
    setStatusUpdateLoading(automationId)

    try {
      const response = await fetch(`/api/marketing/automations/${automationId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          is_active: !currentStatus
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update automation status')
      }

      // Update automation in state
      setAutomations(automations.map(automation =>
        automation.id === automationId
          ? { ...automation, is_active: !currentStatus }
          : automation
      ))
    } catch (error) {
      console.error('Error updating automation status:', error)
      setError(`Failed to update status: ${error.message}`)
    } finally {
      setStatusUpdateLoading(null)
    }
  }

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Get trigger type label
  const getTriggerTypeLabel = (triggerType) => {
    switch (triggerType) {
      case 'event':
        return 'Event-Based'
      case 'schedule':
        return 'Scheduled'
      case 'segment_entry':
        return 'Segment Entry'
      default:
        return triggerType
    }
  }

  // Get event type label
  const getEventTypeLabel = (triggerConfig) => {
    if (!triggerConfig || !triggerConfig.event_type) return '-'

    const eventTypes = {
      new_booking: 'New Booking',
      booking_confirmed: 'Booking Confirmed',
      booking_completed: 'Booking Completed',
      new_customer: 'New Customer',
      product_purchased: 'Product Purchased',
      abandoned_cart: 'Abandoned Cart'
    }

    return eventTypes[triggerConfig.event_type] || triggerConfig.event_type
  }

  // Get schedule frequency label
  const getScheduleFrequencyLabel = (triggerConfig) => {
    if (!triggerConfig || !triggerConfig.frequency) return '-'

    const frequencies = {
      daily: 'Daily',
      weekly: 'Weekly',
      monthly: 'Monthly',
      birthday: 'Birthday'
    }

    return frequencies[triggerConfig.frequency] || triggerConfig.frequency
  }

  // Calculate total pages
  const totalPages = Math.ceil(pagination.total / pagination.limit)

  return (
    <AdminLayout>
      <div className={styles.automationList}>
        <div className={styles.header}>
          <h2>Automated Messages</h2>
          <div className={styles.actions}>
            <Link href="/admin/marketing/automations/new" className={styles.addButton}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
              Create Automation
            </Link>
          </div>
        </div>

        <div className={styles.filters}>
          <div className={styles.searchContainer}>
            <input
              type="text"
              placeholder="Search automations..."
              value={search}
              onChange={handleSearchChange}
              className={styles.searchInput}
            />
          </div>

          <div className={styles.filterControls}>
            <select
              name="trigger_type"
              value={filters.trigger_type}
              onChange={handleFilterChange}
              className={styles.filterSelect}
            >
              <option value="">All Triggers</option>
              <option value="event">Event-Based</option>
              <option value="schedule">Scheduled</option>
              <option value="segment_entry">Segment Entry</option>
            </select>

            <select
              name="message_type"
              value={filters.message_type}
              onChange={handleFilterChange}
              className={styles.filterSelect}
            >
              <option value="">All Types</option>
              <option value="email">Email</option>
              <option value="sms">SMS</option>
              <option value="push">Push</option>
            </select>

            <select
              name="is_active"
              value={filters.is_active}
              onChange={handleFilterChange}
              className={styles.filterSelect}
            >
              <option value="">All Status</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>
        </div>

        {error && (
          <div className={styles.error}>
            Error: {error}
          </div>
        )}

        {loading ? (
          <div className={styles.loading}>Loading automations...</div>
        ) : (
          <>
            <div className={styles.automationGrid}>
              {automations.length === 0 ? (
                <div className={styles.noResults}>
                  No automations found. Create your first automation to get started.
                </div>
              ) : (
                automations.map((automation) => (
                  <div key={automation.id} className={styles.automationCard}>
                    <div className={styles.automationHeader}>
                      <div className={styles.automationName}>{automation.name}</div>
                      <div className={styles.automationStatus}>
                        <button
                          className={`${styles.statusToggle} ${automation.is_active ? styles.statusActive : styles.statusInactive}`}
                          onClick={() => handleToggleStatus(automation.id, automation.is_active)}
                          disabled={statusUpdateLoading === automation.id}
                        >
                          {statusUpdateLoading === automation.id ? (
                            <span className={styles.statusLoading}>...</span>
                          ) : (
                            automation.is_active ? 'Active' : 'Inactive'
                          )}
                        </button>
                      </div>
                    </div>

                    <div className={styles.automationTrigger}>
                      <strong>Trigger:</strong> {getTriggerTypeLabel(automation.trigger_type)}
                      {automation.trigger_type === 'event' && (
                        <span className={styles.triggerDetail}>
                          {getEventTypeLabel(automation.trigger_config)}
                        </span>
                      )}
                      {automation.trigger_type === 'schedule' && (
                        <span className={styles.triggerDetail}>
                          {getScheduleFrequencyLabel(automation.trigger_config)}
                        </span>
                      )}
                    </div>

                    <div className={styles.automationMessage}>
                      <strong>Message:</strong> {automation.message_type === 'email' ? 'Email' :
                                               automation.message_type === 'sms' ? 'SMS' : 'Push Notification'}
                    </div>

                    <div className={styles.automationSegment}>
                      <strong>Segment:</strong> {automation.segment ? automation.segment.name : '-'}
                    </div>

                    {automation.description && (
                      <div className={styles.automationDescription}>
                        {automation.description}
                      </div>
                    )}

                    <div className={styles.automationStats}>
                      <div className={styles.statItem}>
                        <span className={styles.statLabel}>Executions:</span>
                        <span className={styles.statValue}>{automation.stats.total_executions}</span>
                      </div>
                      <div className={styles.statItem}>
                        <span className={styles.statLabel}>Success:</span>
                        <span className={styles.statValue}>{automation.stats.successful_executions}</span>
                      </div>
                      {automation.stats.last_execution && (
                        <div className={styles.statItem}>
                          <span className={styles.statLabel}>Last Run:</span>
                          <span className={styles.statValue}>
                            {formatDate(automation.stats.last_execution.sent_at)}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className={styles.automationActions}>
                      <Link href={`/admin/marketing/automations/${automation.id}`}>
                        <a className={styles.viewButton}>View</a>
                      </Link>
                      <Link href={`/admin/marketing/automations/${automation.id}/edit`}>
                        <a className={styles.editButton}>Edit</a>
                      </Link>
                    </div>
                  </div>
                ))
              )}
            </div>

            {totalPages > 1 && (
              <div className={styles.pagination}>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(1)}
                  disabled={pagination.page === 1}
                >
                  &laquo;
                </button>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                >
                  &lsaquo;
                </button>
                <span className={styles.paginationInfo}>
                  Page {pagination.page} of {totalPages}
                </span>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === totalPages}
                >
                  &rsaquo;
                </button>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(totalPages)}
                  disabled={pagination.page === totalPages}
                >
                  &raquo;
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </AdminLayout>
  )
}

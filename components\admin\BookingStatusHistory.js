import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { STATUS_DISPLAY_NAMES } from '@/lib/booking-status';
import styles from '@/styles/admin/BookingStatusHistory.module.css';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Component to display booking status history
 *
 * @param {Object} props - Component props
 * @param {string} props.bookingId - Booking ID
 * @returns {JSX.Element}
 */
export default function BookingStatusHistory({ bookingId }) {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch status history
  useEffect(() => {
    const fetchHistory = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch status history from Supabase
        const { data, error } = await supabase
          .from('booking_status_history')
          .select(`
            id,
            previous_status,
            new_status,
            notes,
            created_at,
            changed_by,
            user_profiles!booking_status_history_changed_by_fkey (email, display_name, role)
          `)
          .eq('booking_id', bookingId)
          .order('created_at', { ascending: false });

        if (error) throw error;

        setHistory(data || []);
      } catch (error) {
        console.error('Error fetching booking status history:', error);
        setError('Failed to load status history');
      } finally {
        setLoading(false);
      }
    };

    if (bookingId) {
      fetchHistory();
    }
  }, [bookingId]);

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-AU', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get display name for status
  const getStatusDisplayName = (status) => {
    return STATUS_DISPLAY_NAMES[status] || status;
  };

  // Get user display name
  const getUserDisplayName = (user) => {
    if (!user) return 'System';
    return user.display_name || user.email || 'Unknown User';
  };

  return (
    <div className={styles.historyContainer}>
      <h3 className={styles.historyTitle}>Status History</h3>

      {loading ? (
        <div className={styles.loading}>Loading status history...</div>
      ) : error ? (
        <div className={styles.error}>{error}</div>
      ) : history.length === 0 ? (
        <div className={styles.emptyState}>No status changes recorded</div>
      ) : (
        <ul className={styles.historyList}>
          {history.map((item) => (
            <li key={item.id} className={styles.historyItem}>
              <div className={styles.historyHeader}>
                <span className={styles.historyDate}>{formatDate(item.created_at)}</span>
                <span className={styles.historyUser}>by {getUserDisplayName(item.user_profiles)}</span>
              </div>
              <div className={styles.statusChange}>
                <span className={`${styles.status} ${styles[item.previous_status || 'none']}`}>
                  {getStatusDisplayName(item.previous_status) || 'New'}
                </span>
                <span className={styles.arrow}>→</span>
                <span className={`${styles.status} ${styles[item.new_status]}`}>
                  {getStatusDisplayName(item.new_status)}
                </span>
              </div>
              {item.notes && (
                <div className={styles.notes}>{item.notes}</div>
              )}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for inventory dashboard data
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Authenticate request
  const { authorized, error, user } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get admin client
    const supabase = getAdminClient();
    if (!supabase) {
      return res.status(500).json({ error: 'Failed to initialize admin client' });
    }

    // Get total products count
    const { count: totalProducts, error: countError } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      throw countError;
    }

    // Get products with inventory data
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select(`
        id,
        name,
        price,
        cost_price,
        category,
        stock,
        low_stock_threshold
      `);

    if (productsError) {
      throw productsError;
    }

    // Calculate inventory metrics
    const totalValue = products.reduce((sum, product) => {
      const costPrice = product.cost_price || product.price * 0.6; // Fallback to 60% of price if cost not set
      return sum + (costPrice * (product.stock || 0));
    }, 0);

    const lowStockCount = products.filter(product =>
      product.stock > 0 && product.stock <= product.low_stock_threshold
    ).length;

    const outOfStockCount = products.filter(product =>
      product.stock <= 0
    ).length;

    // Get category distribution
    const categoryMap = products.reduce((acc, product) => {
      const category = product.category || 'Uncategorized';
      if (!acc[category]) {
        acc[category] = { name: category, count: 0 };
      }
      acc[category].count++;
      return acc;
    }, {});

    const categoryDistribution = Object.values(categoryMap)
      .sort((a, b) => b.count - a.count);

    // Get top products by stock level
    const stockLevels = products
      .map(product => ({
        id: product.id,
        name: product.name,
        stock: product.stock || 0,
        threshold: product.low_stock_threshold || 5
      }))
      .sort((a, b) => b.stock - a.stock)
      .slice(0, 10);

    // Get recent stock movements
    const { data: recentMovements, error: movementsError } = await supabase
      .from('inventory_transactions')
      .select(`
        id,
        product_id,
        products:product_id (name),
        quantity,
        transaction_type,
        created_at,
        created_by,
        user_profiles!inventory_transactions_created_by_fkey (email, display_name)
      `)
      .order('created_at', { ascending: false })
      .limit(10);

    if (movementsError) {
      throw movementsError;
    }

    // Format movements data
    const formattedMovements = recentMovements.map(movement => ({
      id: movement.id,
      product_id: movement.product_id,
      product_name: movement.products?.name || 'Unknown Product',
      quantity: movement.quantity,
      type: movement.transaction_type,
      created_at: movement.created_at,
      user_name: movement.user_profiles?.display_name || movement.user_profiles?.email || 'Unknown User'
    }));

    // Return dashboard data
    return res.status(200).json({
      totalProducts,
      totalValue,
      lowStockCount,
      outOfStockCount,
      categoryDistribution,
      stockLevels,
      recentMovements: formattedMovements
    });
  } catch (err) {
    console.error('Error fetching inventory dashboard data:', err);
    return res.status(500).json({ error: 'Failed to fetch inventory dashboard data' });
  }
}

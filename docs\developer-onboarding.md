# Developer Onboarding Guide

This guide will help you set up your development environment for the Ocean Soul Sparkles project.

## Prerequisites

- Node.js (v16 or later)
- npm (v7 or later)
- Git
- A code editor (VS Code recommended)
- A Supabase account

## Getting Started

1. Clone the repository:
   ```bash
   git clone https://github.com/Thorlee15/website-ocean-soul-sparkles.git
   cd website-ocean-soul-sparkles
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   - Create a `.env.local` file in the project root
   - Add the required environment variables (see below)

4. Start the development server:
   ```bash
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

## Environment Variables

The application requires the following environment variables:

### Supabase Configuration (Required)

```
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

To get these values:

1. Go to your Supabase project dashboard
2. Click on "Settings" in the sidebar
3. Click on "API" in the submenu
4. Copy the "Project URL" for `NEXT_PUBLIC_SUPABASE_URL`
5. Copy the "anon public" key for `NEXT_PUBLIC_SUPABASE_ANON_KEY`
6. Copy the "service_role" key for `SUPABASE_SERVICE_ROLE_KEY` (keep this secure!)

### OneSignal Configuration (Optional)

```
NEXT_PUBLIC_ONESIGNAL_APP_ID=your-onesignal-app-id
ONESIGNAL_API_KEY=your-onesignal-api-key
ONESIGNAL_REST_API_KEY=your-onesignal-rest-api-key
```

### Site Configuration

```
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_ADMIN_URL=http://localhost:3000/admin
```

## Supabase Setup

The application uses Supabase for authentication and database operations. To set up Supabase:

1. Create a new Supabase project at [https://app.supabase.io](https://app.supabase.io)
2. Set up the database schema:
   - Go to the "SQL Editor" in your Supabase dashboard
   - Run the SQL scripts in the `supabase/migrations` directory

### Database Schema

The application requires the following tables:

#### User Roles Table

```sql
CREATE TABLE public.user_roles (
  id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL PRIMARY KEY,
  role TEXT NOT NULL CHECK (role IN ('admin', 'staff')) DEFAULT 'staff',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Row Level Security (RLS) Policies

Set up RLS policies to secure your data:

```sql
-- Enable RLS on the user_roles table
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to read their own role
CREATE POLICY "Users can read their own role" ON public.user_roles
  FOR SELECT USING (auth.uid() = id);

-- Create policy to allow admins to read all roles
CREATE POLICY "Admins can read all roles" ON public.user_roles
  FOR SELECT USING (
    auth.uid() IN (
      SELECT id FROM public.user_roles WHERE role = 'admin'
    )
  );

-- Create policy to allow admins to update roles
CREATE POLICY "Admins can update roles" ON public.user_roles
  FOR UPDATE USING (
    auth.uid() IN (
      SELECT id FROM public.user_roles WHERE role = 'admin'
    )
  );

-- Create policy to allow admins to insert roles
CREATE POLICY "Admins can insert roles" ON public.user_roles
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT id FROM public.user_roles WHERE role = 'admin'
    )
  );
```

## Authentication

The application uses Supabase for authentication. The authentication flow is as follows:

1. User signs in with email and password
2. Supabase validates the credentials and returns a JWT token
3. The token is stored in the browser's local storage
4. The token is included in all API requests
5. API routes validate the token and check the user's role

### Creating an Admin User

To create an admin user:

1. Sign up a new user through the application
2. Go to the Supabase dashboard
3. Go to the "SQL Editor"
4. Run the following SQL query, replacing `<user_id>` with the ID of the user:

```sql
INSERT INTO public.user_roles (id, role)
VALUES ('<user_id>', 'admin');
```

## Development Workflow

1. Create a new branch for your feature or bug fix
2. Make your changes
3. Run tests: `npm test`
4. Commit your changes
5. Push your branch to GitHub
6. Create a pull request

## Useful Commands

- `npm run dev`: Start the development server
- `npm run build`: Build the application for production
- `npm start`: Start the production server
- `npm test`: Run tests
- `npm run lint`: Run linting
- `npm run sync-services`: Sync services with the database

## Troubleshooting

### Authentication Issues

If you encounter authentication issues:

1. Check that your Supabase environment variables are correct
2. Ensure that the user has the correct role in the `user_roles` table
3. Check the browser console for error messages
4. Try clearing your browser's local storage and signing in again

### Database Issues

If you encounter database issues:

1. Check that your Supabase environment variables are correct
2. Ensure that the database schema is set up correctly
3. Check the RLS policies to ensure they are configured correctly
4. Check the Supabase logs for error messages

## Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.io/docs)
- [Supabase JavaScript Client](https://supabase.io/docs/reference/javascript/supabase-client)
- [Supabase Auth Documentation](https://supabase.io/docs/reference/javascript/auth-signin)

/**
 * Production-specific OneSignal integration
 * 
 * This module provides a reliable way to interact with OneSignal in production environments,
 * with proper initialization checks, error handling, and fallbacks.
 */

// Mock implementation for when OneSignal is not available
const oneSignalMock = {
  getNotificationPermission: () => Promise.resolve('default'),
  isPushNotificationsEnabled: () => Promise.resolve(false),
  showNativePrompt: () => Promise.resolve(),
  showHttpPrompt: () => Promise.resolve(),
  showCategorySlidedown: () => Promise.resolve(),
  getUserId: () => Promise.resolve('mock-user-id'),
  setExternalUserId: () => Promise.resolve(),
  removeExternalUserId: () => Promise.resolve(),
  setEmail: () => Promise.resolve(),
  sendTag: () => Promise.resolve(),
  getTags: () => Promise.resolve({}),
  on: () => {},
  once: () => {},
  off: () => {},
};

// Global initialization state
let isInitialized = false;
let initializationPromise = null;

/**
 * Initialize OneSignal safely
 * This should be called once at application startup
 */
export function initializeOneSignal() {
  // Only run in browser
  if (typeof window === 'undefined') return Promise.resolve(false);
  
  // Skip if already initialized or initializing
  if (isInitialized) return Promise.resolve(true);
  if (initializationPromise) return initializationPromise;
  
  // Create initialization promise
  initializationPromise = new Promise((resolve) => {
    // Function to check if OneSignal is available and fully loaded
    const checkOneSignal = () => {
      if (window.OneSignal && typeof window.OneSignal.getNotificationPermission === 'function') {
        console.log('[OneSignal] Successfully initialized');
        isInitialized = true;
        return true;
      }
      return false;
    };
    
    // Check immediately in case it's already loaded
    if (checkOneSignal()) {
      resolve(true);
      return;
    }
    
    // Set up event listener for OneSignal initialization
    const handleInitialized = () => {
      if (checkOneSignal()) {
        document.removeEventListener('onesignal:initialized', handleInitialized);
        resolve(true);
      }
    };
    
    // Listen for custom initialization event
    document.addEventListener('onesignal:initialized', handleInitialized);
    
    // Also poll periodically in case event is missed
    let attempts = 0;
    const maxAttempts = 20; // 10 seconds total (20 * 500ms)
    
    const checkInterval = setInterval(() => {
      attempts++;
      
      if (checkOneSignal()) {
        clearInterval(checkInterval);
        document.removeEventListener('onesignal:initialized', handleInitialized);
        resolve(true);
        return;
      }
      
      // Give up after max attempts
      if (attempts >= maxAttempts) {
        clearInterval(checkInterval);
        document.removeEventListener('onesignal:initialized', handleInitialized);
        console.warn('[OneSignal] Initialization timed out, using mock implementation');
        resolve(false);
      }
    }, 500);
  });
  
  return initializationPromise;
}

/**
 * Get the OneSignal instance safely
 * Returns mock if OneSignal is not available
 */
export function getOneSignal() {
  if (typeof window === 'undefined') return oneSignalMock;
  
  if (isInitialized && window.OneSignal && 
      typeof window.OneSignal.getNotificationPermission === 'function') {
    return window.OneSignal;
  }
  
  return oneSignalMock;
}

/**
 * Safe wrapper for OneSignal method calls
 * Ensures OneSignal is initialized before calling methods
 */
export async function callOneSignal(methodName, ...args) {
  try {
    // Initialize if needed
    await initializeOneSignal();
    
    // Get OneSignal instance (real or mock)
    const oneSignal = getOneSignal();
    
    // Check if method exists
    if (typeof oneSignal[methodName] !== 'function') {
      console.warn(`[OneSignal] Method ${methodName} is not available`);
      return null;
    }
    
    // Call method
    return await oneSignal[methodName](...args);
  } catch (error) {
    console.error(`[OneSignal] Error calling ${methodName}:`, error);
    return null;
  }
}

// Convenience methods for common OneSignal operations

export async function getNotificationPermission() {
  return callOneSignal('getNotificationPermission');
}

export async function isPushNotificationsEnabled() {
  return callOneSignal('isPushNotificationsEnabled');
}

export async function showNativePrompt() {
  return callOneSignal('showNativePrompt');
}

export async function setExternalUserId(userId) {
  return callOneSignal('setExternalUserId', userId);
}

export async function setEmail(email) {
  return callOneSignal('setEmail', email);
}

export async function sendTag(key, value) {
  return callOneSignal('sendTag', key, value);
}

export async function getTags() {
  return callOneSignal('getTags');
}

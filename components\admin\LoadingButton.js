/**
 * LoadingButton component for the Ocean Soul Sparkles admin panel
 * Displays a button with a loading spinner when in loading state
 */
import React from 'react';
import styles from '@/styles/admin/LoadingButton.module.css';

/**
 * LoadingButton component
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.loading - Whether the button is in loading state
 * @param {string} props.loadingText - Text to display when loading (default: "Loading...")
 * @param {string} props.type - Button type (default: "button")
 * @param {string} props.variant - Button variant (default: "primary")
 * @param {boolean} props.fullWidth - Whether the button should take full width
 * @param {React.ReactNode} props.children - Button content
 * @param {Function} props.onClick - Click handler
 * @param {string} props.className - Additional CSS classes
 * @returns {JSX.Element}
 */
const LoadingButton = ({
  loading = false,
  loadingText = 'Loading...',
  type = 'button',
  variant = 'primary',
  fullWidth = false,
  children,
  onClick,
  className = '',
  ...props
}) => {
  // Determine button classes based on variant and other props
  const buttonClasses = [
    styles.button,
    styles[variant],
    fullWidth ? styles.fullWidth : '',
    loading ? styles.loading : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <button
      type={type}
      className={buttonClasses}
      onClick={onClick}
      disabled={loading || props.disabled}
      {...props}
    >
      {loading ? (
        <span className={styles.loadingContent}>
          <span className={styles.spinner}></span>
          <span className={styles.loadingText}>{loadingText}</span>
        </span>
      ) : (
        children
      )}
    </button>
  );
};

export default LoadingButton;

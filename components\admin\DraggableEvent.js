import { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { getTextColor } from '@/lib/color-utils';
import { STATUS_DISPLAY_NAMES } from '@/lib/booking-status';
import styles from '@/styles/admin/DraggableEvent.module.css';

// Item type for drag and drop
const ITEM_TYPE = 'BOOKING';

/**
 * DraggableEvent component for calendar events
 * 
 * @param {Object} props - Component props
 * @param {Object} props.event - Event data
 * @param {Function} props.onMoveEvent - Function to call when event is moved
 * @param {Function} props.onSelectEvent - Function to call when event is clicked
 * @returns {JSX.Element}
 */
export default function DraggableEvent({ event, onMoveEvent, onSelectEvent }) {
  const ref = useRef(null);
  
  // Set up drag functionality
  const [{ isDragging }, drag] = useDrag({
    type: ITEM_TYPE,
    item: { id: event.id, event },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  
  // Set up drop functionality
  const [{ isOver }, drop] = useDrop({
    accept: ITEM_TYPE,
    drop: (item, monitor) => {
      // Don't do anything if dropping on itself
      if (item.id === event.id) {
        return;
      }
      
      // Calculate position difference
      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      
      // Determine if moving before or after
      const isBefore = hoverClientY < hoverMiddleY;
      
      // Call the move function
      onMoveEvent(item.event, event, isBefore);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });
  
  // Initialize drag and drop refs
  drag(drop(ref));
  
  // Get event data
  const { title, resource } = event;
  const { status, serviceColor } = resource;
  
  // Determine text color based on background color
  const textColor = getTextColor(serviceColor);
  
  // Handle click event
  const handleClick = (e) => {
    e.stopPropagation();
    onSelectEvent(event);
  };
  
  return (
    <div
      ref={ref}
      className={`${styles.draggableEvent} ${isDragging ? styles.dragging : ''} ${isOver ? styles.over : ''}`}
      onClick={handleClick}
      style={{
        opacity: isDragging ? 0.5 : 1,
        cursor: 'pointer',
      }}
    >
      <div className={styles.eventTitle} style={{ color: textColor }}>
        {title}
      </div>
      {status && (
        <div className={styles.eventStatus} style={{ color: textColor }}>
          {STATUS_DISPLAY_NAMES[status] || status}
        </div>
      )}
    </div>
  );
}

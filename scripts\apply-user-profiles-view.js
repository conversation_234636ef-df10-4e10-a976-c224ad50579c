/**
 * <PERSON><PERSON><PERSON> to apply the user_profiles view to Supabase
 * 
 * This script uses the Supabase API to execute SQL statements directly
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase admin client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Missing Supabase URL or service role key in environment variables');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// SQL statements to create the user_profiles view
const sqlStatements = [
  // Drop the view if it exists
  `DROP VIEW IF EXISTS public.user_profiles;`,
  
  // Create the view
  `CREATE VIEW public.user_profiles AS
  SELECT 
    au.id,
    au.email,
    au.raw_user_meta_data->>'name' as display_name,
    au.created_at,
    au.last_sign_in_at,
    ur.role
  FROM 
    auth.users au
  LEFT JOIN 
    public.user_roles ur ON au.id = ur.id;`,
  
  // Enable RLS on the view
  `ALTER VIEW public.user_profiles ENABLE ROW LEVEL SECURITY;`,
  
  // Create policies for the view
  `CREATE POLICY "Users can view their own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);`,
  
  `CREATE POLICY "Admins can view all profiles" ON public.user_profiles
    FOR SELECT USING (
      EXISTS (
        SELECT 1 FROM public.user_roles 
        WHERE id = auth.uid() AND role = 'admin'
      )
    );`,
  
  // Grant permissions
  `GRANT SELECT ON public.user_profiles TO authenticated;`,
  `GRANT SELECT ON public.user_profiles TO service_role;`
];

async function applyMigration() {
  try {
    console.log('Applying user_profiles view migration...');
    
    // Execute each SQL statement
    for (const [index, sql] of sqlStatements.entries()) {
      console.log(`Executing statement ${index + 1}/${sqlStatements.length}...`);
      
      // Use the Supabase API to execute SQL directly
      const { data, error } = await supabaseAdmin.from('_sql').select('*').execute(sql);
      
      if (error) {
        console.error(`Error executing statement ${index + 1}:`, error);
        console.log('This error might be expected if the statement is creating objects that already exist.');
        console.log('Continuing with next statement...');
      } else {
        console.log(`Statement ${index + 1} executed successfully.`);
      }
    }
    
    console.log('Migration completed. Verifying user_profiles view...');
    
    // Verify the view was created
    const { data, error: verifyError } = await supabaseAdmin
      .from('user_profiles')
      .select('id, email, display_name, role')
      .limit(1);
    
    if (verifyError) {
      console.error('Error verifying user_profiles view:', verifyError);
      process.exit(1);
    }
    
    console.log('User profiles view is working correctly!');
    if (data && data.length > 0) {
      console.log('Sample user profile:', data[0]);
    } else {
      console.log('No user profiles found, but view is created successfully.');
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

applyMigration();

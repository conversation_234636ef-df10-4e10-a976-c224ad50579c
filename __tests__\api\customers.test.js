/**
 * Unit tests for Customer API endpoints
 */

import { createMocks } from 'node-mocks-http';
import handler from '@/pages/api/customers/index';
import customerHandler from '@/pages/api/customers/[id]';
import exportHandler from '@/pages/api/customers/export';
import gdprHandler from '@/pages/api/customers/[id]/gdpr-delete';
import notificationHandler from '@/pages/api/notifications/send';

// Mock the supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    offset: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    count: jest.fn().mockReturnThis(),
  }
}));

// Mock the Supabase client
jest.mock('@/lib/supabase', () => ({
  getCurrentUser: jest.fn().mockResolvedValue({
    user: { id: 'test-user-id' },
    role: 'admin'
  }),
  verifyAdminRole: jest.fn().mockResolvedValue(true)
}));

// Mock the notifications library
jest.mock('@/lib/notifications', () => ({
  sendCustomerNotification: jest.fn().mockResolvedValue({ success: true })
}));

describe('Customer API Endpoints', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/customers', () => {
    it('should return a list of customers', async () => {
      const { req, res } = createMocks({
        method: 'GET',
        query: {}
      });

      const mockCustomers = [
        { id: '1', name: 'John Doe', email: '<EMAIL>' },
        { id: '2', name: 'Jane Smith', email: '<EMAIL>' }
      ];

      require('@/lib/supabase').supabase.select.mockResolvedValueOnce({
        data: mockCustomers,
        count: mockCustomers.length,
        error: null
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual({
        customers: mockCustomers,
        total: mockCustomers.length
      });
    });

    it('should handle search and filters', async () => {
      const { req, res } = createMocks({
        method: 'GET',
        query: {
          search: 'john',
          city: 'Sydney',
          state: 'NSW',
          has_bookings: 'true',
          date_added: '30',
          marketing_consent: 'true'
        }
      });

      const mockCustomers = [
        { id: '1', name: 'John Doe', email: '<EMAIL>' }
      ];

      require('@/lib/supabase').supabase.select.mockResolvedValueOnce({
        data: mockCustomers,
        count: mockCustomers.length,
        error: null
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual({
        customers: mockCustomers,
        total: mockCustomers.length
      });
    });
  });

  describe('POST /api/customers', () => {
    it('should create a new customer', async () => {
      const newCustomer = {
        name: 'New Customer',
        email: '<EMAIL>',
        phone: '1234567890',
        marketing_consent: true
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: newCustomer
      });

      require('@/lib/supabase').supabase.select.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' }
      });

      require('@/lib/supabase').supabase.insert.mockResolvedValueOnce({
        data: [{ ...newCustomer, id: '3' }],
        error: null
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(201);
      expect(JSON.parse(res._getData())).toEqual({
        ...newCustomer,
        id: '3'
      });
    });
  });

  describe('GET /api/customers/[id]', () => {
    it('should return a customer with booking history', async () => {
      const { req, res } = createMocks({
        method: 'GET',
        query: { id: '1' }
      });

      const mockCustomer = {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>'
      };

      const mockBookings = [
        { id: 'b1', customer_id: '1', service_id: 's1', start_time: '2023-01-01T10:00:00Z' }
      ];

      const mockPreferences = [
        { customer_id: '1', preference_key: 'favorite_color', preference_value: 'blue' }
      ];

      require('@/lib/supabase').supabase.select.mockResolvedValueOnce({
        data: mockCustomer,
        error: null
      });

      require('@/lib/supabase').supabase.select.mockResolvedValueOnce({
        data: mockBookings,
        error: null
      });

      require('@/lib/supabase').supabase.select.mockResolvedValueOnce({
        data: mockPreferences,
        error: null
      });

      await customerHandler(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual({
        customer: mockCustomer,
        bookings: mockBookings,
        preferences: mockPreferences
      });
    });
  });

  describe('POST /api/notifications/send', () => {
    it('should send a notification to a customer', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: {
          customer_id: '1',
          title: 'Test Notification',
          message: 'This is a test notification',
          type: 'email'
        }
      });

      const mockCustomer = {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>'
      };

      require('@/lib/supabase').supabase.select.mockResolvedValueOnce({
        data: mockCustomer,
        error: null
      });

      require('@/lib/supabase').supabase.insert.mockResolvedValueOnce({
        data: [{ id: 'n1', title: 'Test Notification' }],
        error: null
      });

      await notificationHandler(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData()).success).toBe(true);
    });
  });
});

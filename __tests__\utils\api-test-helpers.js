/**
 * Simplified API testing utilities
 * 
 * This module provides helper functions for testing API endpoints
 * without relying on complex mocking.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.test' });

// Create a Supabase client for testing
export const testSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * Create a test user with the specified role
 * 
 * @param {string} role - User role (admin, staff)
 * @returns {Promise<Object>} - Created user object
 */
export async function createTestUser(role = 'admin') {
  // Generate a unique email
  const timestamp = new Date().getTime();
  const email = `test-${timestamp}@example.com`;
  const password = 'Test123456!';
  
  try {
    // Create user in Supabase Auth
    const { data: authData, error: authError } = await testSupabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true
    });
    
    if (authError) throw authError;
    
    // Add user role
    const { error: roleError } = await testSupabase
      .from('user_roles')
      .insert([{ id: authData.user.id, role }]);
    
    if (roleError) throw roleError;
    
    // Return user with credentials
    return {
      id: authData.user.id,
      email,
      password,
      role
    };
  } catch (error) {
    console.error('Error creating test user:', error);
    throw error;
  }
}

/**
 * Delete a test user
 * 
 * @param {string} userId - User ID to delete
 */
export async function deleteTestUser(userId) {
  try {
    // Delete user role
    await testSupabase
      .from('user_roles')
      .delete()
      .eq('id', userId);
    
    // Delete user from Auth
    await testSupabase.auth.admin.deleteUser(userId);
  } catch (error) {
    console.error('Error deleting test user:', error);
  }
}

/**
 * Sign in as a test user
 * 
 * @param {Object} credentials - User credentials
 * @param {string} credentials.email - User email
 * @param {string} credentials.password - User password
 * @returns {Promise<Object>} - Session object
 */
export async function signInTestUser({ email, password }) {
  try {
    const { data, error } = await testSupabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    console.error('Error signing in test user:', error);
    throw error;
  }
}

/**
 * Create a test customer
 * 
 * @param {Object} customerData - Customer data (optional)
 * @returns {Promise<Object>} - Created customer object
 */
export async function createTestCustomer(customerData = {}) {
  // Generate a unique email
  const timestamp = new Date().getTime();
  const email = `customer-${timestamp}@example.com`;
  
  // Default customer data
  const defaultData = {
    name: `Test Customer ${timestamp}`,
    email,
    phone: '0412345678',
    address: '123 Test Street',
    city: 'Sydney',
    state: 'NSW',
    postal_code: '2000',
    country: 'Australia',
    notes: 'Test customer created for API testing',
    marketing_consent: true
  };
  
  // Merge default data with provided data
  const customer = { ...defaultData, ...customerData };
  
  try {
    const { data, error } = await testSupabase
      .from('customers')
      .insert([customer])
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    console.error('Error creating test customer:', error);
    throw error;
  }
}

/**
 * Delete a test customer
 * 
 * @param {string} customerId - Customer ID to delete
 */
export async function deleteTestCustomer(customerId) {
  try {
    await testSupabase
      .from('customers')
      .delete()
      .eq('id', customerId);
  } catch (error) {
    console.error('Error deleting test customer:', error);
  }
}

/**
 * Clean up test data
 * 
 * @param {Object} testData - Test data to clean up
 * @param {Array<string>} testData.customerIds - Customer IDs to delete
 * @param {Array<string>} testData.userIds - User IDs to delete
 */
export async function cleanupTestData({ customerIds = [], userIds = [] }) {
  // Delete test customers
  for (const customerId of customerIds) {
    await deleteTestCustomer(customerId);
  }
  
  // Delete test users
  for (const userId of userIds) {
    await deleteTestUser(userId);
  }
}

/**
 * Make an authenticated API request
 * 
 * @param {Object} options - Request options
 * @param {string} options.url - API endpoint URL
 * @param {string} options.method - HTTP method
 * @param {Object} options.body - Request body
 * @param {Object} options.session - Auth session
 * @returns {Promise<Object>} - Response object
 */
export async function makeAuthenticatedRequest({ url, method = 'GET', body, session }) {
  const headers = {
    'Content-Type': 'application/json'
  };
  
  if (session?.access_token) {
    headers['Authorization'] = `Bearer ${session.access_token}`;
  }
  
  const options = {
    method,
    headers,
    credentials: 'include'
  };
  
  if (body && (method === 'POST' || method === 'PUT')) {
    options.body = JSON.stringify(body);
  }
  
  const response = await fetch(url, options);
  
  // Parse response
  let data;
  const contentType = response.headers.get('content-type');
  
  if (contentType && contentType.includes('application/json')) {
    data = await response.json();
  } else {
    data = await response.text();
  }
  
  return {
    status: response.status,
    headers: Object.fromEntries(response.headers.entries()),
    data
  };
}

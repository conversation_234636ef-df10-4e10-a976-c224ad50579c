import { getAdminClient, getCurrentUserFromRequest, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUserFromRequest(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  const { id } = req.query

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getMessages(id, req, res)
    case 'POST':
      return createMessage(id, req, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get messages for a campaign
async function getMessages(campaignId, req, res) {
  try {
    const client = getClient();
    if (!client) {
      console.error("Client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    // Check if campaign exists
    const { data: campaign, error: campaignError } = await client
      .from('marketing_campaigns')
      .select('id')
      .eq('id', campaignId)
      .single()

    if (campaignError) {
      throw campaignError
    }

    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' })
    }

    // Get messages
    const { data: messages, error } = await client
      .from('campaign_messages')
      .select('*')
      .eq('campaign_id', campaignId)
      .order('created_at', { ascending: false })

    if (error) {
      throw error
    }

    return res.status(200).json(messages || [])
  } catch (error) {
    console.error('Error fetching campaign messages:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Create a new message for a campaign
async function createMessage(campaignId, req, res) {
  const {
    subject,
    content,
    message_type,
    scheduled_date
  } = req.body

  try {
    // Check if campaign exists
    const { data: campaign, error: campaignError } = await supabase
      .from('marketing_campaigns')
      .select('id, status')
      .eq('id', campaignId)
      .single()

    if (campaignError) {
      throw campaignError
    }

    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' })
    }

    // Validate campaign status
    if (campaign.status === 'completed' || campaign.status === 'canceled') {
      return res.status(400).json({ error: `Cannot add messages to a ${campaign.status} campaign` })
    }

    // Validate required fields
    if (!subject || !content || !message_type) {
      return res.status(400).json({ error: 'Subject, content, and message type are required' })
    }

    // Create message
    const { data, error } = await supabase
      .from('campaign_messages')
      .insert([
        {
          campaign_id: campaignId,
          subject,
          content,
          message_type,
          scheduled_date,
          status: scheduled_date ? 'scheduled' : 'draft'
        }
      ])
      .select()

    if (error) {
      throw error
    }

    return res.status(201).json(data[0])
  } catch (error) {
    console.error('Error creating campaign message:', error)
    return res.status(500).json({ error: error.message })
  }
}

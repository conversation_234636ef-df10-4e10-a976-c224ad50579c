# OceanSoulSparkles Website Testing Tools

This document provides instructions on how to use the testing tools for the OceanSoulSparkles website, including frontend testing tools and API testing approaches.

## Getting Started

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Open the test runner in your browser:
   ```
   http://localhost:3000/test-runner.html
   ```

## Available Testing Tools

The test runner provides access to the following testing tools:

### 1. Compatibility Testing

Tests the website across different viewport sizes and checks for common compatibility issues.

- Click the "Run Compatibility Tests" button to start the tests
- The results will show any issues with responsive design, element visibility, and performance metrics
- Use these results to identify and fix compatibility issues

### 2. Accessibility Testing

Checks the website for common accessibility issues according to WCAG guidelines.

- Click the "Run Accessibility Tests" button to start the tests
- The results will show issues with alt text, contrast, keyboard accessibility, form labels, and more
- Use these results to improve the website's accessibility

### 3. Performance Testing

Analyzes the website's performance metrics.

- Click the "Run Performance Tests" button to start the tests
- The results will show loading times, paint metrics, and resource usage
- Use these results to optimize the website's performance

### 4. Security Testing

Checks the website for common security issues.

- Click the "Run Security Tests" button to start the tests
- The results will show issues with form security, input validation, cookies, localStorage, scripts, and links
- Use these results to improve the website's security

### 5. SEO Testing

Checks the website for common SEO issues and optimization opportunities.

- Click the "Run SEO Tests" button to start the tests
- The results will show issues with meta tags, heading structure, images, structured data, content, and links
- Use these results to improve the website's search engine visibility and ranking

### 6. Payment Gateway Testing

Tests payment gateway integration without processing real payments.

- Click the "Initialize Payment Test Environment" button to set up the test environment
- Navigate to the checkout page to test payment functionality
- The test environment provides mock payment buttons for testing successful, failed, and cancelled payments

### 7. Test Report

Generates a comprehensive test report based on all test results.

- Run all the tests first
- Click the "Generate Test Report" button to create a combined report
- The report can be copied and saved for documentation

## Manual Testing Checklists

In addition to the automated tests, the following manual testing checklists are available:

1. `manual-testing-checklist.md` - General website functionality
2. `performance-optimization-checklist.md` - Performance optimization
3. `security-hardening-checklist.md` - Security hardening
4. `seo-audit-report.md` - SEO audit and recommendations
5. `pre-launch-checklist.md` - Final pre-launch checks

## Additional Testing Resources

### Browser Testing

Test the website in multiple browsers:
- Chrome
- Firefox
- Edge
- Safari

### Device Testing

Test the website on multiple devices:
- Desktop computers
- Tablets
- Mobile phones

### Touch vs. Non-Touch Testing

Test the website with both:
- Mouse and keyboard
- Touch screens

## Reporting Issues

When documenting issues found during testing:

1. Describe the problem clearly
2. Note the environment where it occurs (device, browser, etc.)
3. Include steps to reproduce
4. Add screenshots if possible
5. Assign a priority level (Critical, High, Medium, Low)

Use the `compatibility-testing-report.md` template for documenting issues.

## API Testing

The admin panel and API endpoints require thorough testing to ensure proper functionality. We provide multiple testing approaches:

### 1. Manual Testing Script

The simplest and most reliable approach is to use the manual testing script:

```bash
# Set up environment variables
cp .env .env.local
# Add TEST_ADMIN_EMAIL and TEST_ADMIN_PASSWORD to .env.local

# Run the test script
node scripts/test-customer-api.js
```

This script:
- Signs in as an admin user
- Tests all CRUD operations for customers
- Tests customer export functionality
- Provides clear console output of test results

**When to use**: For quick validation during development or when automated tests have compatibility issues.

### 2. API Integration Tests

For more comprehensive testing, we provide integration tests that use a real Supabase test database:

```bash
# Set up test environment
cp .env.test.example .env.test
# Edit .env.test with your test database credentials

# Run integration tests
npm run test:integration
```

These tests:
- Create temporary test users and data
- Test all API endpoints with real HTTP requests
- Verify authentication and authorization
- Clean up test data after tests

**When to use**: For thorough validation of API functionality in a CI/CD pipeline or before deployment.

### 3. Automated Unit Tests (Limited Compatibility)

We also have unit tests that use mocking to isolate API endpoints:

```bash
npm run test:api
```

**Note**: These tests currently have compatibility issues with the Node.js runtime and may require updates to the testing infrastructure.

### Known Issues with Automated Tests

The automated test suite has the following known issues:

1. The `node-mocks-http` package is incompatible with the current Node.js version
2. Jest configuration has issues handling ES modules in the test environment
3. Mock implementations for Supabase and authentication need updates

### API Testing Checklist

For the customer database management system, verify:

#### Customer List
- [ ] Customers are displayed in a paginated list
- [ ] Search functionality works for name, email, and phone
- [ ] Filtering by city and state works
- [ ] Filtering by booking status works
- [ ] Sorting by different columns works
- [ ] Pagination controls work

#### Customer Creation
- [ ] Required fields are validated
- [ ] Duplicate email addresses are prevented
- [ ] Customer is created with correct data
- [ ] Success message is displayed

#### Customer Details
- [ ] Customer information is displayed correctly
- [ ] Booking history is displayed
- [ ] Customer preferences are displayed
- [ ] Edit and Delete buttons work

#### Customer Update
- [ ] Required fields are validated
- [ ] Duplicate email addresses are prevented
- [ ] Customer is updated with correct data
- [ ] Success message is displayed

#### Customer Export
- [ ] CSV export works
- [ ] JSON export works
- [ ] Filtering by marketing consent works
- [ ] Only admins can export data

#### GDPR Compliance
- [ ] Customer data can be anonymized
- [ ] Only admins can anonymize data
- [ ] Anonymized data preserves booking history
- [ ] Success message is displayed

## Conclusion

Following this testing process will help ensure that the OceanSoulSparkles website launches with a high level of quality, compatibility, performance, accessibility, and security. Address all critical issues before launch and create a plan for addressing non-critical issues post-launch.

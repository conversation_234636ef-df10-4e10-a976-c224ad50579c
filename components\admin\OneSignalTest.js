import { useState, useEffect } from 'react';
import { 
  waitForOneSignal, 
  getNotificationPermissionStatus, 
  showNativePrompt, 
  showCategorySlidedown,
  isPushNotificationsEnabled,
  getOneSignalTags,
  addOneSignalTag
} from '@/lib/onesignal';
import styles from '@/styles/admin/OneSignalTest.module.css';

export default function OneSignalTest() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState(null);
  const [isEnabled, setIsEnabled] = useState(false);
  const [tags, setTags] = useState({});
  const [newTag, setNewTag] = useState({ key: '', value: '' });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState(null);

  // Check OneSignal initialization status
  useEffect(() => {
    const checkOneSignal = async () => {
      try {
        await waitForOneSignal();
        setIsInitialized(true);
        
        // Get current permission status
        const status = await getNotificationPermissionStatus();
        setPermissionStatus(status);
        
        // Check if push notifications are enabled
        const enabled = await isPushNotificationsEnabled();
        setIsEnabled(enabled);
        
        // Get current tags
        const { success, tags: currentTags } = await getOneSignalTags();
        if (success && currentTags) {
          setTags(currentTags);
        }
      } catch (error) {
        console.error('Error initializing OneSignal test:', error);
      }
    };
    
    checkOneSignal();
  }, []);

  // Handle showing the native prompt
  const handleShowPrompt = async () => {
    setLoading(true);
    setMessage(null);
    
    try {
      await showNativePrompt();
      
      // Update permission status after prompt
      const status = await getNotificationPermissionStatus();
      setPermissionStatus(status);
      
      // Check if push notifications are enabled
      const enabled = await isPushNotificationsEnabled();
      setIsEnabled(enabled);
      
      setMessage({ type: 'success', text: 'Notification prompt shown successfully' });
    } catch (error) {
      console.error('Error showing notification prompt:', error);
      setMessage({ type: 'error', text: `Error: ${error.message}` });
    } finally {
      setLoading(false);
    }
  };

  // Handle showing the category slidedown
  const handleShowCategories = async () => {
    setLoading(true);
    setMessage(null);
    
    try {
      await showCategorySlidedown();
      setMessage({ type: 'success', text: 'Category slidedown shown successfully' });
    } catch (error) {
      console.error('Error showing category slidedown:', error);
      setMessage({ type: 'error', text: `Error: ${error.message}` });
    } finally {
      setLoading(false);
    }
  };

  // Handle adding a new tag
  const handleAddTag = async (e) => {
    e.preventDefault();
    
    if (!newTag.key || !newTag.value) {
      setMessage({ type: 'error', text: 'Both key and value are required' });
      return;
    }
    
    setLoading(true);
    setMessage(null);
    
    try {
      const { success } = await addOneSignalTag(newTag.key, newTag.value);
      
      if (success) {
        // Update tags list
        const { success: getSuccess, tags: currentTags } = await getOneSignalTags();
        if (getSuccess && currentTags) {
          setTags(currentTags);
        }
        
        // Clear form
        setNewTag({ key: '', value: '' });
        setMessage({ type: 'success', text: 'Tag added successfully' });
      } else {
        throw new Error('Failed to add tag');
      }
    } catch (error) {
      console.error('Error adding tag:', error);
      setMessage({ type: 'error', text: `Error: ${error.message}` });
    } finally {
      setLoading(false);
    }
  };

  // Handle input change for new tag form
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewTag(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className={styles.oneSignalTest}>
      <h2>OneSignal Integration Test</h2>
      
      <div className={styles.statusSection}>
        <h3>Status</h3>
        <div className={styles.statusGrid}>
          <div className={styles.statusItem}>
            <span className={styles.statusLabel}>Initialization:</span>
            <span className={`${styles.statusValue} ${isInitialized ? styles.success : styles.error}`}>
              {isInitialized ? 'Initialized' : 'Not Initialized'}
            </span>
          </div>
          
          <div className={styles.statusItem}>
            <span className={styles.statusLabel}>Permission:</span>
            <span className={`${styles.statusValue} ${
              permissionStatus === 'granted' ? styles.success : 
              permissionStatus === 'denied' ? styles.error : 
              styles.warning
            }`}>
              {permissionStatus || 'Unknown'}
            </span>
          </div>
          
          <div className={styles.statusItem}>
            <span className={styles.statusLabel}>Push Enabled:</span>
            <span className={`${styles.statusValue} ${isEnabled ? styles.success : styles.warning}`}>
              {isEnabled ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        </div>
      </div>
      
      {message && (
        <div className={`${styles.message} ${styles[message.type]}`}>
          {message.text}
        </div>
      )}
      
      <div className={styles.actionsSection}>
        <h3>Actions</h3>
        <div className={styles.actionButtons}>
          <button 
            className={styles.actionButton}
            onClick={handleShowPrompt}
            disabled={loading || !isInitialized}
          >
            Show Notification Prompt
          </button>
          
          <button 
            className={styles.actionButton}
            onClick={handleShowCategories}
            disabled={loading || !isInitialized || !isEnabled}
          >
            Show Category Preferences
          </button>
        </div>
      </div>
      
      <div className={styles.tagsSection}>
        <h3>User Tags</h3>
        
        {Object.keys(tags).length > 0 ? (
          <div className={styles.tagsList}>
            {Object.entries(tags).map(([key, value]) => (
              <div key={key} className={styles.tag}>
                <span className={styles.tagKey}>{key}:</span>
                <span className={styles.tagValue}>{value}</span>
              </div>
            ))}
          </div>
        ) : (
          <p className={styles.noTags}>No tags set</p>
        )}
        
        <form onSubmit={handleAddTag} className={styles.addTagForm}>
          <h4>Add New Tag</h4>
          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="tagKey">Key</label>
              <input
                id="tagKey"
                name="key"
                type="text"
                value={newTag.key}
                onChange={handleInputChange}
                className={styles.input}
                disabled={loading || !isInitialized || !isEnabled}
              />
            </div>
            
            <div className={styles.formGroup}>
              <label htmlFor="tagValue">Value</label>
              <input
                id="tagValue"
                name="value"
                type="text"
                value={newTag.value}
                onChange={handleInputChange}
                className={styles.input}
                disabled={loading || !isInitialized || !isEnabled}
              />
            </div>
            
            <button
              type="submit"
              className={styles.addButton}
              disabled={loading || !isInitialized || !isEnabled}
            >
              Add Tag
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

import { getAdminClient, getCurrentUserFromRequest, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUserFromRequest(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { period = 'month', start_date, end_date } = req.query

  try {
    // Calculate date range based on period
    const today = new Date()
    let startDate = new Date()
    let endDate = new Date()

    if (start_date && end_date) {
      // Custom date range
      startDate = new Date(start_date)
      endDate = new Date(end_date)
    } else {
      // Predefined periods
      switch (period) {
        case 'week':
          startDate.setDate(today.getDate() - 7)
          break
        case 'month':
          startDate.setMonth(today.getMonth() - 1)
          break
        case 'quarter':
          startDate.setMonth(today.getMonth() - 3)
          break
        case 'year':
          startDate.setFullYear(today.getFullYear() - 1)
          break
        default:
          startDate.setMonth(today.getMonth() - 1) // Default to last month
      }
    }

    // Format dates for Supabase queries
    const formattedStartDate = startDate.toISOString()
    const formattedEndDate = endDate.toISOString()

    // Get client from Supabase
    const client = getClient();
    if (!client) {
      console.error("Supabase client not available for analytics marketing.");
      return res.status(503).json({ error: 'Service temporarily unavailable. Please try again later.' });
    }

    // Get campaign metrics
    const { data: campaignMetrics, error: campaignError } = await client
      .from('campaign_messages')
      .select(`
        id,
        campaign_id,
        status,
        sent_date,
        campaigns:campaign_id (name, campaign_type)
      `)
      .gte('sent_date', formattedStartDate)
      .lte('sent_date', formattedEndDate)

    if (campaignError) throw campaignError

    // Get automation metrics
    const { data: automationLogs, error: automationError } = await client
      .from('automation_logs')
      .select(`
        id,
        automation_id,
        status,
        sent_at,
        automations:automation_id (name, message_type)
      `)
      .gte('sent_at', formattedStartDate)
      .lte('sent_at', formattedEndDate)

    if (automationError) throw automationError

    // Get customer engagement metrics
    const { data: customerEngagement, error: customerError } = await client
      .from('customers')
      .select('id, marketing_consent, created_at')
      .gte('created_at', formattedStartDate)
      .lte('created_at', formattedEndDate)

    if (customerError) throw customerError

    // Calculate campaign metrics
    const totalCampaignMessages = campaignMetrics ? campaignMetrics.length : 0
    const successfulCampaignMessages = campaignMetrics
      ? campaignMetrics.filter(msg => msg.status === 'sent').length
      : 0

    // Calculate automation metrics
    const totalAutomationMessages = automationLogs ? automationLogs.length : 0
    const successfulAutomationMessages = automationLogs
      ? automationLogs.filter(log => log.status === 'success').length
      : 0

    // Calculate customer metrics
    const newCustomers = customerEngagement ? customerEngagement.length : 0
    const customersWithConsent = customerEngagement
      ? customerEngagement.filter(customer => customer.marketing_consent).length
      : 0

    // Calculate message type distribution
    const messageTypeDistribution = {
      email: 0,
      sms: 0,
      push: 0
    }

    // Count campaign message types
    campaignMetrics?.forEach(msg => {
      if (msg.campaigns?.campaign_type) {
        messageTypeDistribution[msg.campaigns.campaign_type] =
          (messageTypeDistribution[msg.campaigns.campaign_type] || 0) + 1
      }
    })

    // Count automation message types
    automationLogs?.forEach(log => {
      if (log.automations?.message_type) {
        messageTypeDistribution[log.automations.message_type] =
          (messageTypeDistribution[log.automations.message_type] || 0) + 1
      }
    })

    // Calculate daily/weekly message counts for time series chart
    const timeSeriesData = calculateTimeSeriesData(
      [...(campaignMetrics || []), ...(automationLogs || [])],
      startDate,
      endDate,
      period
    )

    // Prepare response
    const response = {
      period,
      date_range: {
        start_date: formattedStartDate,
        end_date: formattedEndDate
      },
      summary: {
        total_messages: totalCampaignMessages + totalAutomationMessages,
        successful_messages: successfulCampaignMessages + successfulAutomationMessages,
        success_rate: calculatePercentage(
          successfulCampaignMessages + successfulAutomationMessages,
          totalCampaignMessages + totalAutomationMessages
        ),
        campaign_messages: totalCampaignMessages,
        automation_messages: totalAutomationMessages,
        new_customers: newCustomers,
        customers_with_consent: customersWithConsent,
        consent_rate: calculatePercentage(customersWithConsent, newCustomers)
      },
      message_types: messageTypeDistribution,
      time_series: timeSeriesData
    }

    return res.status(200).json(response)
  } catch (error) {
    console.error('Error fetching marketing analytics:', error)
    return res.status(500).json({ error: 'Failed to fetch marketing analytics' })
  }
}

// Helper function to calculate percentage
function calculatePercentage(numerator, denominator) {
  if (!denominator) return 0
  return Math.round((numerator / denominator) * 100)
}

// Helper function to calculate time series data
function calculateTimeSeriesData(messages, startDate, endDate, period) {
  const result = []
  const dateFormat = new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: period === 'year' ? 'numeric' : undefined
  })

  // Determine interval based on period
  let interval = 'day'
  if (period === 'year') {
    interval = 'month'
  } else if (period === 'quarter') {
    interval = 'week'
  }

  // Create date buckets
  const currentDate = new Date(startDate)
  while (currentDate <= endDate) {
    const nextDate = new Date(currentDate)

    // Increment based on interval
    if (interval === 'day') {
      nextDate.setDate(currentDate.getDate() + 1)
    } else if (interval === 'week') {
      nextDate.setDate(currentDate.getDate() + 7)
    } else if (interval === 'month') {
      nextDate.setMonth(currentDate.getMonth() + 1)
    }

    // Count messages in this interval
    const messagesInInterval = messages.filter(msg => {
      const msgDate = new Date(msg.sent_date || msg.sent_at)
      return msgDate >= currentDate && msgDate < nextDate
    })

    result.push({
      date: dateFormat.format(currentDate),
      total: messagesInInterval.length,
      successful: messagesInInterval.filter(
        msg => msg.status === 'sent' || msg.status === 'success'
      ).length
    })

    currentDate.setTime(nextDate.getTime())
  }

  return result
}

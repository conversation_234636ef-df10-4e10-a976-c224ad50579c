import React, { useState, useEffect } from 'react';

/**
 * ClientOnly component wrapper
 *
 * This component ensures that its children are only rendered on the client side,
 * preventing hydration mismatches between server and client rendering.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render client-side only
 * @param {string} props.className - Optional CSS class name
 * @param {React.ReactNode} props.fallback - Optional fallback component to render during SSR
 * @returns {React.ReactElement} - The wrapped component
 */
export default function ClientOnly({ children, className = '', fallback = null }) {
  // State to track if component is mounted (client-side)
  const [isMounted, setIsMounted] = useState(false);

  // Effect runs only client-side after hydration
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Use suppressHydrationWarning to prevent hydration errors
  return (
    <div suppressHydrationWarning className={className}>
      {isMounted ? children : (fallback || null)}
    </div>
  );
}

/**
 * Higher-order component (HOC) version of ClientOnly
 *
 * Wraps a component to ensure it only renders on the client side.
 *
 * @param {React.Component} Component - Component to wrap
 * @param {React.Component} FallbackComponent - Optional fallback component for SSR
 * @returns {React.Component} - Client-side only component
 */
export function withClientOnly(Component, FallbackComponent = null) {
  function WithClientOnly(props) {
    return (
      <ClientOnly fallback={FallbackComponent ? <FallbackComponent {...props} /> : null}>
        <Component {...props} />
      </ClientOnly>
    );
  }

  // Set display name for debugging
  const displayName = Component.displayName || Component.name || 'Component';
  WithClientOnly.displayName = `WithClientOnly(${displayName})`;

  return WithClientOnly;
}

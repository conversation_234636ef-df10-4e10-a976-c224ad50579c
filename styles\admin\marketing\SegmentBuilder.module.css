.segmentBuilder {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.builderHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.builderHeader h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.previewButton {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.previewButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.previewButton:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.conditionGroup {
  margin-bottom: 20px;
}

.groupOperator {
  margin: 10px 0;
  text-align: center;
}

.operatorSelect {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
}

.groupContent {
  background-color: rgba(255, 255, 255, 0.5);
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 15px;
}

.groupHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.groupTitle {
  font-weight: 600;
  color: #333;
}

.removeGroupButton {
  background-color: transparent;
  color: #f44336;
  border: 1px solid #f44336;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.removeGroupButton:hover {
  background-color: #f44336;
  color: white;
}

.condition {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.conditionType,
.conditionField,
.conditionOperator,
.conditionValue {
  flex: 1;
}

.typeSelect,
.fieldSelect,
.operatorSelect,
.valueSelect,
.valueInput {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  background-color: white;
}

.valueInput::placeholder {
  color: #aaa;
}

.betweenValues {
  display: flex;
  align-items: center;
  gap: 8px;
}

.betweenSeparator {
  color: #666;
  font-size: 0.9rem;
}

.removeConditionButton {
  background-color: transparent;
  color: #f44336;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 1.2rem;
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.removeConditionButton:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

.addConditionButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 15px;
}

.addConditionButton {
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.addConditionButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
}

.addGroupButton {
  margin-top: 20px;
  text-align: center;
}

.addGroupButton button {
  background-color: transparent;
  color: #6e8efb;
  border: 1px dashed #6e8efb;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.addGroupButton button:hover {
  background-color: rgba(110, 142, 251, 0.1);
}

.previewResults {
  margin-top: 30px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #e0e0e0;
}

.previewResults h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.1rem;
  color: #333;
}

.previewStats {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.previewStat {
  display: flex;
  align-items: center;
  gap: 8px;
}

.previewStatLabel {
  font-size: 0.9rem;
  color: #666;
}

.previewStatValue {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.previewTable {
  width: 100%;
  overflow-x: auto;
}

.previewTable table {
  width: 100%;
  border-collapse: collapse;
}

.previewTable th {
  text-align: left;
  padding: 10px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  font-size: 0.9rem;
  color: #333;
}

.previewTable td {
  padding: 10px;
  border-bottom: 1px solid #eee;
  font-size: 0.9rem;
  color: #666;
}

.previewMore {
  margin-top: 10px;
  text-align: center;
  color: #6e8efb;
  font-size: 0.9rem;
}

.noResults {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

@media (max-width: 768px) {
  .condition {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .conditionType,
  .conditionField,
  .conditionOperator,
  .conditionValue {
    width: 100%;
  }
  
  .removeConditionButton {
    align-self: flex-end;
    margin-top: -30px;
  }
  
  .addConditionButtons {
    flex-direction: column;
  }
}

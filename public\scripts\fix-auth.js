/**
 * Authentication Fix Script
 * 
 * This script helps fix authentication issues by:
 * 1. Clearing all stored tokens
 * 2. Refreshing the authentication session
 * 3. Storing the new token in sessionStorage
 * 
 * To use this script:
 * 1. Open the browser console (F12 or right-click > Inspect > Console)
 * 2. Copy and paste the entire script into the console
 * 3. Press Enter to run the script
 * 4. Refresh the page after the script completes
 */

(async function() {
  console.log('Starting authentication fix...');
  
  // Step 1: Clear all stored tokens
  console.log('Clearing all stored tokens...');
  
  // Clear sessionStorage
  try {
    if (window.sessionStorage) {
      sessionStorage.removeItem('oss_auth_token_cache');
      sessionStorage.removeItem('oss_session');
      console.log('Cleared tokens from sessionStorage');
    }
  } catch (e) {
    console.error('Error clearing sessionStorage:', e);
  }
  
  // Clear localStorage (legacy storage)
  try {
    if (window.localStorage) {
      localStorage.removeItem('oss_auth_token');
      localStorage.removeItem('sb_auth_token');
      console.log('Cleared tokens from localStorage');
    }
  } catch (e) {
    console.error('Error clearing localStorage:', e);
  }
  
  // Clear cookies
  try {
    document.cookie = 'oss_auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = 'sb_auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    console.log('Cleared tokens from cookies');
  } catch (e) {
    console.error('Error clearing cookies:', e);
  }
  
  // Step 2: Get current user session from Supabase
  console.log('Checking current session...');
  
  try {
    // Try to get the current session from the diagnostics endpoint
    const sessionCheck = await fetch('/api/admin/diagnostics/session-check')
      .then(response => response.json())
      .catch(error => {
        console.error('Error checking session:', error);
        return { error: 'Failed to check session' };
      });
    
    if (sessionCheck.error) {
      console.log('No active session found. Please log in again.');
      window.location.href = '/admin/login';
      return;
    }
    
    if (sessionCheck.session) {
      console.log('Active session found:', sessionCheck.session.user.email);
      
      // Step 3: Store the token in sessionStorage
      try {
        const token = sessionCheck.session.access_token;
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + 1); // Set expiry to 1 hour from now
        
        sessionStorage.setItem('oss_auth_token_cache', JSON.stringify({
          token: token,
          expiry: expiresAt.getTime(),
          refreshed: Date.now()
        }));
        
        console.log('Token stored in sessionStorage');
        console.log('Authentication fix complete!');
        console.log('Please refresh the page and try accessing the admin panel again.');
      } catch (e) {
        console.error('Error storing token in sessionStorage:', e);
      }
    } else {
      console.log('No active session found. Please log in again.');
      window.location.href = '/admin/login';
    }
  } catch (error) {
    console.error('Authentication fix failed:', error);
    console.log('Please try logging out and logging back in.');
    window.location.href = '/admin/login';
  }
})();

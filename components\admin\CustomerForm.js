import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { useAuth } from '@/contexts/AuthContext'
import { authenticatedFetch } from '@/lib/auth-utils'
import LoadingButton from '@/components/admin/LoadingButton'
import styles from '@/styles/admin/CustomerForm.module.css'

export default function CustomerForm({ customer, onSave, onCancel }) {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    country: 'Australia',
    notes: '',
    marketing_consent: false
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [successMessage, setSuccessMessage] = useState(null)
  const [preferences, setPreferences] = useState([])
  const [newPreference, setNewPreference] = useState({ key: '', value: '' })

  // Initialize form with customer data if editing
  useEffect(() => {
    if (customer) {
      setFormData({
        name: customer.name || '',
        email: customer.email || '',
        phone: customer.phone || '',
        address: customer.address || '',
        city: customer.city || '',
        state: customer.state || '',
        postal_code: customer.postal_code || '',
        country: customer.country || 'Australia',
        notes: customer.notes || '',
        marketing_consent: customer.marketing_consent || false
      })
    }
  }, [customer])

  // Load customer preferences if editing
  useEffect(() => {
    if (customer?.id && isAuthenticated) {
      const fetchPreferences = async () => {
        try {
          // Use the authenticatedFetch utility
          const data = await authenticatedFetch(`/api/customers/${customer.id}`);

          if (data.preferences && Array.isArray(data.preferences)) {
            const formattedPreferences = data.preferences.map(pref => ({
              key: pref.preference_key,
              value: pref.preference_value
            }));
            setPreferences(formattedPreferences);
          }
        } catch (error) {
          console.error('Error fetching preferences:', error);
          setError('Failed to load customer preferences. Please try again.');
        }
      };

      fetchPreferences();
    }
  }, [customer, isAuthenticated])

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setSuccessMessage(null)

    try {
      // Validate required fields
      if (!formData.name || !formData.email) {
        throw new Error('Name and email are required')
      }

      // Prepare request data
      const requestData = {
        ...formData,
        preferences: preferences.map(pref => ({
          key: pref.key,
          value: pref.value
        }))
      }

      let savedCustomer;

      if (customer?.id) {
        // Update existing customer using authenticatedFetch
        savedCustomer = await authenticatedFetch(`/api/customers/${customer.id}`, {
          method: 'PUT',
          body: JSON.stringify(requestData)
        });
      } else {
        // Create new customer using authenticatedFetch
        savedCustomer = await authenticatedFetch('/api/customers', {
          method: 'POST',
          body: JSON.stringify(requestData)
        });
      }

      setSuccessMessage('Customer saved successfully')

      // Call onSave callback if provided
      if (onSave) {
        onSave(savedCustomer)
      } else {
        // Navigate to customer details page after a short delay
        setTimeout(() => {
          router.push(`/admin/customers/${savedCustomer.id}`)
        }, 1500)
      }
    } catch (error) {
      console.error('Error saving customer:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  // Handle adding a new preference
  const handleAddPreference = () => {
    if (newPreference.key && newPreference.value) {
      setPreferences([...preferences, { ...newPreference }])
      setNewPreference({ key: '', value: '' })
    }
  }

  // Handle removing a preference
  const handleRemovePreference = (index) => {
    setPreferences(preferences.filter((_, i) => i !== index))
  }

  // Handle preference input changes
  const handlePreferenceChange = (e) => {
    const { name, value } = e.target
    setNewPreference(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <div className={styles.customerForm}>
      {error && <div className={styles.error}>{error}</div>}
      {successMessage && <div className={styles.success}>{successMessage}</div>}

      <form onSubmit={handleSubmit}>
        <div className={styles.formGrid}>
          <div className={styles.formGroup}>
            <label htmlFor="name">Name *</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              disabled={loading}
              className={styles.input}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="email">Email *</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              disabled={loading}
              className={styles.input}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="phone">Phone</label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              disabled={loading}
              className={styles.input}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="address">Address</label>
            <input
              type="text"
              id="address"
              name="address"
              value={formData.address}
              onChange={handleChange}
              disabled={loading}
              className={styles.input}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="city">City</label>
            <input
              type="text"
              id="city"
              name="city"
              value={formData.city}
              onChange={handleChange}
              disabled={loading}
              className={styles.input}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="state">State</label>
            <select
              id="state"
              name="state"
              value={formData.state}
              onChange={handleChange}
              disabled={loading}
              className={styles.select}
            >
              <option value="">Select State</option>
              <option value="NSW">NSW</option>
              <option value="VIC">VIC</option>
              <option value="QLD">QLD</option>
              <option value="WA">WA</option>
              <option value="SA">SA</option>
              <option value="TAS">TAS</option>
              <option value="ACT">ACT</option>
              <option value="NT">NT</option>
            </select>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="postal_code">Postal Code</label>
            <input
              type="text"
              id="postal_code"
              name="postal_code"
              value={formData.postal_code}
              onChange={handleChange}
              disabled={loading}
              className={styles.input}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="country">Country</label>
            <input
              type="text"
              id="country"
              name="country"
              value={formData.country}
              onChange={handleChange}
              disabled={loading}
              className={styles.input}
            />
          </div>
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="notes">Notes</label>
          <textarea
            id="notes"
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            disabled={loading}
            className={styles.textarea}
            rows={4}
          />
        </div>

        <div className={styles.checkboxGroup}>
          <input
            type="checkbox"
            id="marketing_consent"
            name="marketing_consent"
            checked={formData.marketing_consent}
            onChange={handleChange}
            disabled={loading}
            className={styles.checkbox}
          />
          <label htmlFor="marketing_consent">
            Customer has consented to receive marketing communications
          </label>
        </div>

        <div className={styles.preferencesSection}>
          <h3>Customer Preferences</h3>

          {preferences.length > 0 && (
            <div className={styles.preferencesList}>
              {preferences.map((pref, index) => (
                <div key={index} className={styles.preferenceItem}>
                  <div className={styles.preferenceContent}>
                    <strong>{pref.key}:</strong> {pref.value}
                  </div>
                  <button
                    type="button"
                    onClick={() => handleRemovePreference(index)}
                    className={styles.removeButton}
                    disabled={loading}
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}

          <div className={styles.addPreference}>
            <div className={styles.preferenceInputs}>
              <input
                type="text"
                placeholder="Preference Key"
                name="key"
                value={newPreference.key}
                onChange={handlePreferenceChange}
                disabled={loading}
                className={styles.input}
              />
              <input
                type="text"
                placeholder="Preference Value"
                name="value"
                value={newPreference.value}
                onChange={handlePreferenceChange}
                disabled={loading}
                className={styles.input}
              />
            </div>
            <button
              type="button"
              onClick={handleAddPreference}
              disabled={!newPreference.key || !newPreference.value || loading}
              className={styles.addButton}
            >
              Add
            </button>
          </div>
        </div>

        <div className={styles.formActions}>
          <LoadingButton
            type="button"
            onClick={onCancel || (() => router.back())}
            loading={loading}
            variant="secondary"
            className={styles.cancelButton}
          >
            Cancel
          </LoadingButton>
          <LoadingButton
            type="submit"
            loading={loading}
            loadingText="Saving..."
            variant="primary"
            className={styles.saveButton}
          >
            {customer?.id ? 'Update Customer' : 'Create Customer'}
          </LoadingButton>
        </div>
      </form>
    </div>
  )
}

import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/inventory/StockMovementLog.module.css';

/**
 * StockMovementLog component for displaying inventory transaction history
 * 
 * @param {Object} props - Component props
 * @param {string} props.productId - Optional product ID to filter movements
 * @returns {JSX.Element}
 */
export default function StockMovementLog({ productId = null }) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [movements, setMovements] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState({
    type: '',
    dateFrom: '',
    dateTo: '',
    search: ''
  });
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  
  const ITEMS_PER_PAGE = 20;

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(filters.search);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [filters.search]);

  // Fetch stock movements
  useEffect(() => {
    async function fetchMovements() {
      try {
        setLoading(true);
        setError(null);
        
        // Build query params
        const queryParams = new URLSearchParams({
          page,
          limit: ITEMS_PER_PAGE,
          sort_by: sortBy,
          sort_order: sortOrder
        });
        
        if (productId) {
          queryParams.append('product_id', productId);
        }
        
        if (filters.type) {
          queryParams.append('type', filters.type);
        }
        
        if (filters.dateFrom) {
          queryParams.append('date_from', filters.dateFrom);
        }
        
        if (filters.dateTo) {
          queryParams.append('date_to', filters.dateTo);
        }
        
        if (debouncedSearch) {
          queryParams.append('search', debouncedSearch);
        }
        
        const response = await fetch(`/api/admin/inventory/movements?${queryParams.toString()}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch stock movements');
        }
        
        const data = await response.json();
        setMovements(data.movements);
        setTotalCount(data.count);
      } catch (err) {
        console.error('Error fetching stock movements:', err);
        setError(err.message || 'Failed to load stock movements');
        toast.error('Failed to load stock movements');
      } finally {
        setLoading(false);
      }
    }
    
    fetchMovements();
  }, [productId, page, sortBy, sortOrder, debouncedSearch, filters.type, filters.dateFrom, filters.dateTo]);

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Reset to first page when filters change
    setPage(1);
  };

  // Handle sort
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  // Render sort indicator
  const renderSortIndicator = (field) => {
    if (sortBy !== field) return null;
    
    return (
      <span className={styles.sortIndicator}>
        {sortOrder === 'asc' ? '↑' : '↓'}
      </span>
    );
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  // Get transaction type class
  const getTransactionTypeClass = (type) => {
    switch (type) {
      case 'restock':
        return styles.restock;
      case 'sale':
        return styles.sale;
      case 'adjustment':
        return styles.adjustment;
      case 'return':
        return styles.return;
      case 'damaged':
        return styles.damaged;
      case 'lost':
        return styles.lost;
      default:
        return '';
    }
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

  return (
    <div className={styles.stockMovementLog}>
      <div className={styles.filters}>
        <div className={styles.filterGroup}>
          <input
            type="text"
            name="search"
            placeholder="Search products..."
            value={filters.search}
            onChange={handleFilterChange}
            className={styles.searchInput}
          />
        </div>
        
        <div className={styles.filterGroup}>
          <select
            name="type"
            value={filters.type}
            onChange={handleFilterChange}
            className={styles.filterSelect}
          >
            <option value="">All Transaction Types</option>
            <option value="restock">Restock</option>
            <option value="sale">Sale</option>
            <option value="adjustment">Adjustment</option>
            <option value="return">Return</option>
            <option value="damaged">Damaged</option>
            <option value="lost">Lost</option>
          </select>
        </div>
        
        <div className={styles.filterGroup}>
          <input
            type="date"
            name="dateFrom"
            value={filters.dateFrom}
            onChange={handleFilterChange}
            className={styles.dateInput}
          />
          <span className={styles.dateSeparator}>to</span>
          <input
            type="date"
            name="dateTo"
            value={filters.dateTo}
            onChange={handleFilterChange}
            className={styles.dateInput}
          />
        </div>
      </div>
      
      {loading ? (
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p>Loading stock movements...</p>
        </div>
      ) : error ? (
        <div className={styles.errorContainer}>
          <p className={styles.errorMessage}>{error}</p>
          <button 
            className={styles.retryButton}
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      ) : movements.length === 0 ? (
        <div className={styles.noData}>
          <p>No stock movements found</p>
        </div>
      ) : (
        <>
          <div className={styles.tableContainer}>
            <table className={styles.movementsTable}>
              <thead>
                <tr>
                  <th onClick={() => handleSort('product_name')}>
                    Product {renderSortIndicator('product_name')}
                  </th>
                  <th onClick={() => handleSort('transaction_type')}>
                    Type {renderSortIndicator('transaction_type')}
                  </th>
                  <th onClick={() => handleSort('quantity')}>
                    Quantity {renderSortIndicator('quantity')}
                  </th>
                  <th onClick={() => handleSort('created_at')}>
                    Date {renderSortIndicator('created_at')}
                  </th>
                  <th onClick={() => handleSort('user_name')}>
                    User {renderSortIndicator('user_name')}
                  </th>
                  <th>Notes</th>
                </tr>
              </thead>
              <tbody>
                {movements.map((movement) => (
                  <tr key={movement.id}>
                    <td>{movement.product_name}</td>
                    <td>
                      <span className={`${styles.transactionType} ${getTransactionTypeClass(movement.type)}`}>
                        {movement.type}
                      </span>
                    </td>
                    <td className={movement.quantity > 0 ? styles.positive : styles.negative}>
                      {movement.quantity > 0 ? `+${movement.quantity}` : movement.quantity}
                    </td>
                    <td>{formatDate(movement.created_at)}</td>
                    <td>{movement.user_name}</td>
                    <td>{movement.notes || '-'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {totalPages > 1 && (
            <div className={styles.pagination}>
              <button
                className={styles.paginationButton}
                onClick={() => setPage(prev => Math.max(prev - 1, 1))}
                disabled={page === 1}
              >
                Previous
              </button>
              
              <span className={styles.pageInfo}>
                Page {page} of {totalPages}
              </span>
              
              <button
                className={styles.paginationButton}
                onClick={() => setPage(prev => Math.min(prev + 1, totalPages))}
                disabled={page === totalPages}
              >
                Next
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}

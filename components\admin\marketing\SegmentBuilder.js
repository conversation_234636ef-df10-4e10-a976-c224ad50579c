import { useState, useEffect } from 'react'
import styles from '@/styles/admin/marketing/SegmentBuilder.module.css'

const CONDITION_TYPES = {
  CUSTOMER: 'customer',
  BOOKING: 'booking',
  PURCHASE: 'purchase',
  TIME: 'time'
}

const OPERATORS = {
  EQUALS: 'equals',
  NOT_EQUALS: 'not_equals',
  CONTAINS: 'contains',
  GREATER_THAN: 'greater_than',
  LESS_THAN: 'less_than',
  BETWEEN: 'between',
  IN: 'in',
  NOT_IN: 'not_in'
}

const LOGICAL_OPERATORS = {
  AND: 'and',
  OR: 'or'
}

// Initial condition templates
const getInitialCondition = (type) => {
  switch (type) {
    case CONDITION_TYPES.CUSTOMER:
      return { field: 'city', operator: OPERATORS.EQUALS, value: '' }
    case CONDITION_TYPES.BOOKING:
      return { field: 'booking_count', operator: OPERATORS.GREATER_THAN, value: 0 }
    case CONDITION_TYPES.PURCHASE:
      return { field: 'total_spent', operator: OPERATORS.GREATER_THAN, value: 0 }
    case CONDITION_TYPES.TIME:
      return { field: 'created_at', operator: OPERATORS.GREATER_THAN, value: '30d' }
    default:
      return { field: '', operator: '', value: '' }
  }
}

// Field options for each condition type
const FIELD_OPTIONS = {
  [CONDITION_TYPES.CUSTOMER]: [
    { value: 'city', label: 'City' },
    { value: 'state', label: 'State' },
    { value: 'country', label: 'Country' },
    { value: 'marketing_consent', label: 'Marketing Consent' }
  ],
  [CONDITION_TYPES.BOOKING]: [
    { value: 'booking_count', label: 'Number of Bookings' },
    { value: 'service_id', label: 'Service Booked' },
    { value: 'total_spent_bookings', label: 'Total Spent on Bookings' },
    { value: 'last_booking', label: 'Last Booking Date' }
  ],
  [CONDITION_TYPES.PURCHASE]: [
    { value: 'purchase_count', label: 'Number of Purchases' },
    { value: 'product_id', label: 'Product Purchased' },
    { value: 'total_spent_products', label: 'Total Spent on Products' },
    { value: 'last_purchase', label: 'Last Purchase Date' }
  ],
  [CONDITION_TYPES.TIME]: [
    { value: 'created_at', label: 'Customer Since' },
    { value: 'updated_at', label: 'Last Updated' },
    { value: 'inactive_days', label: 'Inactive Days' }
  ]
}

// Operator options based on field type
const getOperatorOptions = (field) => {
  if (['marketing_consent'].includes(field)) {
    return [
      { value: OPERATORS.EQUALS, label: 'Is' },
      { value: OPERATORS.NOT_EQUALS, label: 'Is Not' }
    ]
  } else if (['city', 'state', 'country', 'service_id', 'product_id'].includes(field)) {
    return [
      { value: OPERATORS.EQUALS, label: 'Equals' },
      { value: OPERATORS.NOT_EQUALS, label: 'Does Not Equal' },
      { value: OPERATORS.CONTAINS, label: 'Contains' },
      { value: OPERATORS.IN, label: 'Is One Of' },
      { value: OPERATORS.NOT_IN, label: 'Is Not One Of' }
    ]
  } else if (['booking_count', 'total_spent_bookings', 'purchase_count', 'total_spent_products', 'inactive_days'].includes(field)) {
    return [
      { value: OPERATORS.EQUALS, label: 'Equals' },
      { value: OPERATORS.NOT_EQUALS, label: 'Does Not Equal' },
      { value: OPERATORS.GREATER_THAN, label: 'Greater Than' },
      { value: OPERATORS.LESS_THAN, label: 'Less Than' },
      { value: OPERATORS.BETWEEN, label: 'Between' }
    ]
  } else if (['created_at', 'updated_at', 'last_booking', 'last_purchase'].includes(field)) {
    return [
      { value: OPERATORS.GREATER_THAN, label: 'After' },
      { value: OPERATORS.LESS_THAN, label: 'Before' },
      { value: OPERATORS.BETWEEN, label: 'Between' }
    ]
  }
  
  return []
}

export default function SegmentBuilder({ initialQuery = null, onChange, onPreview }) {
  const [groups, setGroups] = useState([])
  const [previewLoading, setPreviewLoading] = useState(false)
  const [previewResults, setPreviewResults] = useState(null)
  
  // Initialize with default group or from initialQuery
  useEffect(() => {
    if (initialQuery) {
      try {
        setGroups(initialQuery.groups || [])
      } catch (error) {
        console.error('Error parsing initial query:', error)
        setGroups([{
          operator: LOGICAL_OPERATORS.AND,
          conditions: [{ type: CONDITION_TYPES.CUSTOMER, ...getInitialCondition(CONDITION_TYPES.CUSTOMER) }]
        }])
      }
    } else {
      setGroups([{
        operator: LOGICAL_OPERATORS.AND,
        conditions: [{ type: CONDITION_TYPES.CUSTOMER, ...getInitialCondition(CONDITION_TYPES.CUSTOMER) }]
      }])
    }
  }, [initialQuery])
  
  // Update parent component when groups change
  useEffect(() => {
    if (onChange && groups.length > 0) {
      onChange({ groups })
    }
  }, [groups, onChange])
  
  // Add a new condition group
  const addGroup = () => {
    setGroups([...groups, {
      operator: LOGICAL_OPERATORS.AND,
      conditions: [{ type: CONDITION_TYPES.CUSTOMER, ...getInitialCondition(CONDITION_TYPES.CUSTOMER) }]
    }])
  }
  
  // Remove a condition group
  const removeGroup = (groupIndex) => {
    const newGroups = [...groups]
    newGroups.splice(groupIndex, 1)
    setGroups(newGroups)
  }
  
  // Update group logical operator
  const updateGroupOperator = (groupIndex, operator) => {
    const newGroups = [...groups]
    newGroups[groupIndex].operator = operator
    setGroups(newGroups)
  }
  
  // Add a condition to a group
  const addCondition = (groupIndex, type) => {
    const newGroups = [...groups]
    newGroups[groupIndex].conditions.push({ 
      type, 
      ...getInitialCondition(type) 
    })
    setGroups(newGroups)
  }
  
  // Remove a condition from a group
  const removeCondition = (groupIndex, conditionIndex) => {
    const newGroups = [...groups]
    newGroups[groupIndex].conditions.splice(conditionIndex, 1)
    
    // If group has no conditions, remove the group
    if (newGroups[groupIndex].conditions.length === 0) {
      newGroups.splice(groupIndex, 1)
    }
    
    setGroups(newGroups)
  }
  
  // Update a condition
  const updateCondition = (groupIndex, conditionIndex, field, value) => {
    const newGroups = [...groups]
    newGroups[groupIndex].conditions[conditionIndex][field] = value
    
    // If field changes, reset operator and value
    if (field === 'field') {
      const conditionType = newGroups[groupIndex].conditions[conditionIndex].type
      const operators = getOperatorOptions(value)
      newGroups[groupIndex].conditions[conditionIndex].operator = operators.length > 0 ? operators[0].value : ''
      newGroups[groupIndex].conditions[conditionIndex].value = ''
    }
    
    setGroups(newGroups)
  }
  
  // Preview segment results
  const handlePreview = async () => {
    if (!onPreview) return
    
    setPreviewLoading(true)
    try {
      const results = await onPreview({ groups })
      setPreviewResults(results)
    } catch (error) {
      console.error('Error previewing segment:', error)
    } finally {
      setPreviewLoading(false)
    }
  }
  
  return (
    <div className={styles.segmentBuilder}>
      <div className={styles.builderHeader}>
        <h3>Segment Conditions</h3>
        <button 
          className={styles.previewButton} 
          onClick={handlePreview}
          disabled={previewLoading}
        >
          {previewLoading ? 'Loading...' : 'Preview Segment'}
        </button>
      </div>
      
      {groups.map((group, groupIndex) => (
        <div key={groupIndex} className={styles.conditionGroup}>
          {groupIndex > 0 && (
            <div className={styles.groupOperator}>
              <select
                value={group.operator}
                onChange={(e) => updateGroupOperator(groupIndex, e.target.value)}
                className={styles.operatorSelect}
              >
                <option value={LOGICAL_OPERATORS.AND}>AND</option>
                <option value={LOGICAL_OPERATORS.OR}>OR</option>
              </select>
            </div>
          )}
          
          <div className={styles.groupContent}>
            <div className={styles.groupHeader}>
              <span className={styles.groupTitle}>Condition Group {groupIndex + 1}</span>
              {groups.length > 1 && (
                <button
                  className={styles.removeGroupButton}
                  onClick={() => removeGroup(groupIndex)}
                >
                  Remove Group
                </button>
              )}
            </div>
            
            {group.conditions.map((condition, conditionIndex) => (
              <div key={conditionIndex} className={styles.condition}>
                <div className={styles.conditionType}>
                  <select
                    value={condition.type}
                    onChange={(e) => {
                      const newType = e.target.value
                      updateCondition(groupIndex, conditionIndex, 'type', newType)
                      const initialCondition = getInitialCondition(newType)
                      updateCondition(groupIndex, conditionIndex, 'field', initialCondition.field)
                      updateCondition(groupIndex, conditionIndex, 'operator', initialCondition.operator)
                      updateCondition(groupIndex, conditionIndex, 'value', initialCondition.value)
                    }}
                    className={styles.typeSelect}
                  >
                    <option value={CONDITION_TYPES.CUSTOMER}>Customer</option>
                    <option value={CONDITION_TYPES.BOOKING}>Booking</option>
                    <option value={CONDITION_TYPES.PURCHASE}>Purchase</option>
                    <option value={CONDITION_TYPES.TIME}>Time</option>
                  </select>
                </div>
                
                <div className={styles.conditionField}>
                  <select
                    value={condition.field}
                    onChange={(e) => updateCondition(groupIndex, conditionIndex, 'field', e.target.value)}
                    className={styles.fieldSelect}
                  >
                    {FIELD_OPTIONS[condition.type].map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className={styles.conditionOperator}>
                  <select
                    value={condition.operator}
                    onChange={(e) => updateCondition(groupIndex, conditionIndex, 'operator', e.target.value)}
                    className={styles.operatorSelect}
                  >
                    {getOperatorOptions(condition.field).map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className={styles.conditionValue}>
                  {condition.operator === OPERATORS.BETWEEN ? (
                    <div className={styles.betweenValues}>
                      <input
                        type={['booking_count', 'total_spent_bookings', 'purchase_count', 'total_spent_products', 'inactive_days'].includes(condition.field) ? 'number' : 'text'}
                        value={Array.isArray(condition.value) ? condition.value[0] || '' : ''}
                        onChange={(e) => {
                          const newValue = e.target.value
                          const currentValue = Array.isArray(condition.value) ? condition.value : ['', '']
                          updateCondition(groupIndex, conditionIndex, 'value', [newValue, currentValue[1]])
                        }}
                        className={styles.valueInput}
                        placeholder="Min"
                      />
                      <span className={styles.betweenSeparator}>and</span>
                      <input
                        type={['booking_count', 'total_spent_bookings', 'purchase_count', 'total_spent_products', 'inactive_days'].includes(condition.field) ? 'number' : 'text'}
                        value={Array.isArray(condition.value) ? condition.value[1] || '' : ''}
                        onChange={(e) => {
                          const newValue = e.target.value
                          const currentValue = Array.isArray(condition.value) ? condition.value : ['', '']
                          updateCondition(groupIndex, conditionIndex, 'value', [currentValue[0], newValue])
                        }}
                        className={styles.valueInput}
                        placeholder="Max"
                      />
                    </div>
                  ) : condition.field === 'marketing_consent' ? (
                    <select
                      value={condition.value}
                      onChange={(e) => updateCondition(groupIndex, conditionIndex, 'value', e.target.value === 'true')}
                      className={styles.valueSelect}
                    >
                      <option value="true">Yes</option>
                      <option value="false">No</option>
                    </select>
                  ) : (
                    <input
                      type={['booking_count', 'total_spent_bookings', 'purchase_count', 'total_spent_products', 'inactive_days'].includes(condition.field) ? 'number' : 'text'}
                      value={condition.value}
                      onChange={(e) => updateCondition(groupIndex, conditionIndex, 'value', e.target.value)}
                      className={styles.valueInput}
                      placeholder="Value"
                    />
                  )}
                </div>
                
                <button
                  className={styles.removeConditionButton}
                  onClick={() => removeCondition(groupIndex, conditionIndex)}
                >
                  &times;
                </button>
              </div>
            ))}
            
            <div className={styles.addConditionButtons}>
              <button
                className={styles.addConditionButton}
                onClick={() => addCondition(groupIndex, CONDITION_TYPES.CUSTOMER)}
              >
                + Customer
              </button>
              <button
                className={styles.addConditionButton}
                onClick={() => addCondition(groupIndex, CONDITION_TYPES.BOOKING)}
              >
                + Booking
              </button>
              <button
                className={styles.addConditionButton}
                onClick={() => addCondition(groupIndex, CONDITION_TYPES.PURCHASE)}
              >
                + Purchase
              </button>
              <button
                className={styles.addConditionButton}
                onClick={() => addCondition(groupIndex, CONDITION_TYPES.TIME)}
              >
                + Time
              </button>
            </div>
          </div>
        </div>
      ))}
      
      <div className={styles.addGroupButton}>
        <button onClick={addGroup}>
          + Add Condition Group
        </button>
      </div>
      
      {previewResults && (
        <div className={styles.previewResults}>
          <h3>Preview Results</h3>
          <div className={styles.previewStats}>
            <div className={styles.previewStat}>
              <span className={styles.previewStatLabel}>Matching Customers:</span>
              <span className={styles.previewStatValue}>{previewResults.total || 0}</span>
            </div>
          </div>
          
          {previewResults.customers && previewResults.customers.length > 0 ? (
            <div className={styles.previewTable}>
              <table>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Location</th>
                    <th>Marketing Consent</th>
                  </tr>
                </thead>
                <tbody>
                  {previewResults.customers.slice(0, 5).map((customer) => (
                    <tr key={customer.id}>
                      <td>{customer.name}</td>
                      <td>{customer.email}</td>
                      <td>{customer.city}, {customer.state}</td>
                      <td>{customer.marketing_consent ? 'Yes' : 'No'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {previewResults.customers.length > 5 && (
                <div className={styles.previewMore}>
                  + {previewResults.customers.length - 5} more customers
                </div>
              )}
            </div>
          ) : (
            <div className={styles.noResults}>
              No customers match these criteria
            </div>
          )}
        </div>
      )}
    </div>
  )
}

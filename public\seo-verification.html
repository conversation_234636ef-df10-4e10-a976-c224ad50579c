<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OceanSoulSparkles | Melbourne Facepaint & Entertainment</title>
  
  <!-- Essential Meta Tags -->
  <meta name="description" content="OceanSoulSparkles - Melbourne's premier face painting, airbrush body art, and braiding service for events, festivals, and parties.">
  <meta name="keywords" content="face painting, airbrush body art, braiding, Melbourne, events, festivals, eco-friendly, biodegradable glitter">
  <link rel="canonical" href="https://www.oceansoulsparkles.com.au">
  <link rel="icon" href="/favicon.ico">
  
  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="OceanSoulSparkles | Melbourne Face Painting & Entertainment">
  <meta property="og:description" content="Professional face painting, airbrush body art, and braiding services for events, festivals, and parties in Melbourne.">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://www.oceansoulsparkles.com.au">
  <meta property="og:image" content="https://www.oceansoulsparkles.com.au/images/og-image.jpg">
  
  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="OceanSoulSparkles | Melbourne Face Painting & Entertainment">
  <meta name="twitter:description" content="Professional face painting, airbrush body art, and braiding services for events, festivals, and parties in Melbourne.">
  <meta name="twitter:image" content="https://www.oceansoulsparkles.com.au/images/og-image.jpg">
  
  <!-- Robots Meta Tag -->
  <meta name="robots" content="index, follow">
  
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    h1 {
      color: #4ECDC4;
    }
    .section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      overflow: auto;
      white-space: pre-wrap;
    }
    button {
      background-color: #4ECDC4;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 5px;
      cursor: pointer;
      margin-top: 10px;
    }
    button:hover {
      background-color: #3DBCB4;
    }
  </style>
</head>
<body>
  <h1>OceanSoulSparkles SEO Verification</h1>
  
  <div class="section">
    <h2>Meta Tags</h2>
    <p>This page contains all the required meta tags for proper SEO.</p>
    <button onclick="checkMetaTags()">Check Meta Tags</button>
    <pre id="meta-tags-result">Click the button to check meta tags...</pre>
  </div>
  
  <div class="section">
    <h2>Structured Data</h2>
    <p>The following structured data has been implemented:</p>
    
    <h3>Organization Schema</h3>
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "OceanSoulSparkles",
      "url": "https://www.oceansoulsparkles.com.au",
      "logo": "https://www.oceansoulsparkles.com.au/images/bannerlogo.PNG",
      "sameAs": [
        "https://www.instagram.com/oceansoulsparkles",
        "https://www.facebook.com/OceanSoulSparkles/"
      ],
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+61-XXX-XXX-XXX",
        "contactType": "customer service",
        "email": "<EMAIL>"
      }
    }
    </script>
    
    <h3>LocalBusiness Schema</h3>
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "EntertainmentBusiness",
      "name": "OceanSoulSparkles",
      "image": "https://www.oceansoulsparkles.com.au/images/bannerlogo.PNG",
      "url": "https://www.oceansoulsparkles.com.au",
      "telephone": "+61-XXX-XXX-XXX",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Melbourne",
        "addressRegion": "Victoria",
        "addressCountry": "AU"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "-37.8136",
        "longitude": "144.9631"
      },
      "priceRange": "$$"
    }
    </script>
    
    <h3>BreadcrumbList Schema</h3>
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://www.oceansoulsparkles.com.au/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "SEO Verification",
          "item": "https://www.oceansoulsparkles.com.au/seo-verification.html"
        }
      ]
    }
    </script>
    
    <button onclick="checkStructuredData()">Check Structured Data</button>
    <pre id="structured-data-result">Click the button to check structured data...</pre>
  </div>
  
  <script>
    function checkMetaTags() {
      const result = document.getElementById('meta-tags-result');
      
      // Check meta tags
      const metaTags = {
        description: document.querySelector('meta[name="description"]'),
        canonical: document.querySelector('link[rel="canonical"]'),
        ogTitle: document.querySelector('meta[property="og:title"]'),
        ogDescription: document.querySelector('meta[property="og:description"]'),
        ogType: document.querySelector('meta[property="og:type"]'),
        ogUrl: document.querySelector('meta[property="og:url"]'),
        ogImage: document.querySelector('meta[property="og:image"]'),
        twitterCard: document.querySelector('meta[name="twitter:card"]'),
        twitterTitle: document.querySelector('meta[name="twitter:title"]'),
        twitterDescription: document.querySelector('meta[name="twitter:description"]'),
        twitterImage: document.querySelector('meta[name="twitter:image"]'),
        robots: document.querySelector('meta[name="robots"]')
      };
      
      let output = '## Meta Tags Check\n\n';
      
      for (const [name, tag] of Object.entries(metaTags)) {
        if (tag) {
          const content = tag.getAttribute('content') || tag.getAttribute('href');
          output += `✅ ${name}: ${content}\n`;
        } else {
          output += `❌ ${name}: Not found\n`;
        }
      }
      
      result.textContent = output;
    }
    
    function checkStructuredData() {
      const result = document.getElementById('structured-data-result');
      
      // Check structured data
      const structuredDataScripts = document.querySelectorAll('script[type="application/ld+json"]');
      
      let output = '## Structured Data Check\n\n';
      
      if (structuredDataScripts.length === 0) {
        output += '❌ No structured data found';
      } else {
        output += `✅ Found ${structuredDataScripts.length} structured data elements:\n\n`;
        
        structuredDataScripts.forEach((script, index) => {
          try {
            const data = JSON.parse(script.textContent);
            output += `${index + 1}. Type: ${data['@type']}\n`;
          } catch (error) {
            output += `${index + 1}. Error parsing JSON: ${error.message}\n`;
          }
        });
      }
      
      result.textContent = output;
    }
  </script>
</body>
</html>

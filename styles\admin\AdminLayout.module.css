.adminLayout {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background-color: #2c3e50;
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 100;
  transition: transform 0.3s ease;
}

.sidebarHeader {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logoContainer {
  display: flex;
  justify-content: center;
  width: 100%;
}

.logo {
  max-width: 150px;
  height: auto;
}

.closeSidebar {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.sidebarNav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.sidebarNav a {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.2s;
}

.sidebarNav a:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.sidebarNav a.active {
  background-color: #6a0dad;
  color: white;
}

.sidebarNav a svg {
  margin-right: 10px;
}

.sidebarFooter {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.userInfo {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.userName {
  font-weight: 500;
  margin-bottom: 5px;
}

.userRole {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  text-transform: uppercase;
}

.signOutButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.signOutButton:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.signOutButton svg {
  margin-right: 8px;
}

.authRecoveryButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 8px;
  margin-bottom: 8px;
  background-color: rgba(255, 193, 7, 0.8);
  border: none;
  border-radius: 4px;
  color: #212529;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.authRecoveryButton:hover {
  background-color: rgba(255, 193, 7, 1);
}

.authRecoveryButton svg {
  margin-right: 6px;
}

.mainContent {
  flex: 1;
  margin-left: 250px;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  transition: margin-left 0.3s ease;
}

.header {
  height: 70px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.menuButton {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  margin-right: 15px;
}

.pageTitle {
  font-size: 20px;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.headerActions {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.content {
  padding: 20px;
  flex: 1;
}

/* Mobile styles */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: 280px;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .closeSidebar {
    display: block;
  }
  
  .mainContent {
    margin-left: 0;
  }
  
  .menuButton {
    display: block;
  }
}

.paymentMethodSelector {
  margin-bottom: 2rem;
}

.sectionTitle {
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: #333;
}

.paymentOptions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.paymentOption {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.paymentOption:hover {
  border-color: #1A73E8;
  background-color: rgba(26, 115, 232, 0.05);
}

.paymentOption.selected {
  border-color: #1A73E8;
  background-color: rgba(26, 115, 232, 0.05);
}

.radioButton {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #999;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.paymentOption.selected .radioButton {
  border-color: #1A73E8;
}

.radioSelected {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #1A73E8;
}

.paymentLogo {
  display: flex;
  align-items: center;
}

.paymentLogo img {
  max-height: 32px;
  max-width: 120px;
}

.securePaymentNote {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #4CAF50;
  margin-top: 1rem;
}

.securePaymentNote svg {
  margin-right: 0.5rem;
}

@media (max-width: 768px) {
  .paymentOptions {
    flex-direction: column;
  }
  
  .paymentOption {
    width: 100%;
  }
}

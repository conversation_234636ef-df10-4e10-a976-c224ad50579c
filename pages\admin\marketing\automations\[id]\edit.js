import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import AdminLayout from '@/components/admin/AdminLayout'
import AutomationForm from '@/components/admin/marketing/AutomationForm'
import styles from '@/styles/admin/marketing/AutomationCreate.module.css'

export default function EditAutomation() {
  const router = useRouter()
  const { id } = router.query
  const [automation, setAutomation] = useState(null)
  const [loading, setLoading] = useState(false)
  const [fetchLoading, setFetchLoading] = useState(true)
  const [error, setError] = useState(null)
  const [successMessage, setSuccessMessage] = useState(null)

  // Fetch automation data
  useEffect(() => {
    if (!id) return

    const fetchAutomation = async () => {
      setFetchLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/marketing/automations/${id}`)
        
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch automation')
        }

        const data = await response.json()
        setAutomation(data.automation || null)
      } catch (error) {
        console.error('Error fetching automation:', error)
        setError(error.message)
      } finally {
        setFetchLoading(false)
      }
    }

    fetchAutomation()
  }, [id])

  // Handle form submission
  const handleSubmit = async (automationData) => {
    setLoading(true)
    setError(null)
    setSuccessMessage(null)

    try {
      // Update automation
      const response = await fetch(`/api/marketing/automations/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(automationData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update automation')
      }

      const data = await response.json()
      setSuccessMessage('Automation updated successfully')

      // Redirect to automation detail page after a short delay
      setTimeout(() => {
        router.push(`/admin/marketing/automations/${id}`)
      }, 1500)
    } catch (error) {
      console.error('Error updating automation:', error)
      setError(error.message)
      setLoading(false)
    }
  }

  if (fetchLoading) {
    return (
      <AdminLayout>
        <div className={styles.automationCreate}>
          <div className={styles.loading}>Loading automation data...</div>
        </div>
      </AdminLayout>
    )
  }

  if (!automation && !fetchLoading) {
    return (
      <AdminLayout>
        <div className={styles.automationCreate}>
          <div className={styles.error}>
            Automation not found or you don't have permission to edit it.
          </div>
          <button
            className={styles.backButton}
            onClick={() => router.push('/admin/marketing/automations')}
          >
            Back to Automations
          </button>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className={styles.automationCreate}>
        <div className={styles.header}>
          <h2>Edit Automated Message</h2>
          <button
            className={styles.cancelButton}
            onClick={() => router.push(`/admin/marketing/automations/${id}`)}
            disabled={loading}
          >
            Cancel
          </button>
        </div>

        {error && (
          <div className={styles.error}>
            Error: {error}
          </div>
        )}

        {successMessage && (
          <div className={styles.success}>
            {successMessage}
          </div>
        )}

        <AutomationForm
          initialAutomation={automation}
          onSubmit={handleSubmit}
          onCancel={() => router.push(`/admin/marketing/automations/${id}`)}
        />
      </div>
    </AdminLayout>
  )
}

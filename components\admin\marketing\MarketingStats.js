import { useState, useEffect } from 'react'
import { getAuthToken } from '@/lib/auth-utils'
import styles from '@/styles/admin/marketing/Analytics.module.css'

export default function MarketingStats() {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [stats, setStats] = useState(null)
  const [segments, setSegments] = useState([])
  const [campaigns, setCampaigns] = useState([])

  // Fetch marketing stats
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      setError(null)

      try {
        // Get authentication token
        const authToken = await getAuthToken()

        if (!authToken) {
          throw new Error('Authentication required')
        }

        const headers = {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }

        // Fetch marketing stats
        const statsResponse = await fetch('/api/admin/marketing/stats', {
          headers
        })

        if (!statsResponse.ok) {
          const errorData = await statsResponse.json()
          throw new Error(errorData.error || 'Failed to fetch marketing stats')
        }

        const statsData = await statsResponse.json()
        setStats(statsData)

        // Fetch recent segments
        const segmentsResponse = await fetch('/api/admin/marketing/segments?limit=5', {
          headers
        })

        if (segmentsResponse.ok) {
          const segmentsData = await segmentsResponse.json()
          setSegments(segmentsData.segments || [])
        }

        // Fetch recent campaigns
        const campaignsResponse = await fetch('/api/admin/marketing/campaigns?limit=3', {
          headers
        })

        if (campaignsResponse.ok) {
          const campaignsData = await campaignsResponse.json()
          setCampaigns(campaignsData.campaigns || [])
        }

      } catch (error) {
        console.error('Error fetching marketing data:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return <div className={styles.loading}>Loading marketing stats...</div>
  }

  if (error) {
    return <div className={styles.error}>Error: {error}</div>
  }

  if (!stats) {
    return <div className={styles.noData}>No marketing stats available</div>
  }

  return (
    <div className={styles.analyticsSection}>
      <h3>Marketing Overview</h3>

      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{stats.totalSegments}</div>
          <div className={styles.metricLabel}>Customer Segments</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{stats.totalCampaigns}</div>
          <div className={styles.metricLabel}>Total Campaigns</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{stats.activeCampaigns}</div>
          <div className={styles.metricLabel}>Active Campaigns</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{stats.customersWithConsent}</div>
          <div className={styles.metricLabel}>Customers with Consent</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{stats.totalCustomers}</div>
          <div className={styles.metricLabel}>Total Customers</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>
            {stats.totalCustomers > 0
              ? Math.round((stats.customersWithConsent / stats.totalCustomers) * 100)
              : 0}%
          </div>
          <div className={styles.metricLabel}>Consent Rate</div>
        </div>
      </div>

      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <h4>Recent Segments</h4>
          {segments.length > 0 ? (
            <div className={styles.listContainer}>
              {segments.map((segment, index) => (
                <div key={segment.id || index} className={styles.listItem}>
                  <div className={styles.listItemName}>{segment.name || 'Unnamed Segment'}</div>
                  <div className={styles.listItemMeta}>
                    {segment.description || 'No description'}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className={styles.emptyState}>No segments created yet</div>
          )}
        </div>

        <div className={styles.statCard}>
          <h4>Recent Campaigns</h4>
          {campaigns.length > 0 ? (
            <div className={styles.listContainer}>
              {campaigns.map((campaign, index) => (
                <div key={campaign.id || index} className={styles.listItem}>
                  <div className={styles.listItemName}>{campaign.name || 'Unnamed Campaign'}</div>
                  <div className={styles.listItemMeta}>
                    Status: {campaign.status || 'Unknown'}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className={styles.emptyState}>No campaigns created yet</div>
          )}
        </div>
      </div>

      {stats._meta && (
        <div className={styles.metaInfo}>
          <small>Last updated: {new Date(stats._meta.timestamp).toLocaleString()}</small>
        </div>
      )}
    </div>
  )
}

.salesDashboard {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.dashboardHeader h2 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.comparisonToggle {
  display: flex;
  align-items: center;
}

.comparisonToggle label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #555;
  cursor: pointer;
}

.comparisonToggle input {
  margin-right: 8px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #4a90e2;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.errorMessage {
  color: #d32f2f;
  margin-bottom: 16px;
  text-align: center;
}

.retryButton {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.retryButton:hover {
  background-color: #3a7bc8;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

@media (min-width: 576px) {
  .metricsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 992px) {
  .metricsGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.metricCard {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.metricValue {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.changeIndicator {
  font-size: 14px;
  margin-left: 8px;
  font-weight: 500;
}

.positive {
  color: #2e7d32;
}

.negative {
  color: #c62828;
}

.metricLabel {
  font-size: 14px;
  color: #666;
}

.chartsGrid {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.chartRow {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

@media (min-width: 768px) {
  .chartRow {
    grid-template-columns: repeat(2, 1fr);
  }
}

.chartCard {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.chartContainer {
  height: 300px;
  position: relative;
}

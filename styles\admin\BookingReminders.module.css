.container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 25px;
  margin-bottom: 30px;
}

.header {
  margin-bottom: 20px;
}

.title {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 10px;
}

.description {
  color: #666;
  margin-bottom: 20px;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.result {
  background-color: #f0f9ff;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  border-left: 4px solid #0ea5e9;
}

.result h3 {
  color: #0c4a6e;
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.stats {
  display: flex;
  gap: 20px;
  margin: 15px 0;
}

.statItem {
  display: flex;
  flex-direction: column;
  background-color: white;
  padding: 10px 15px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-width: 100px;
}

.statLabel {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.statValue {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.resultDetails {
  margin-top: 15px;
}

.resultDetails h4 {
  font-size: 1rem;
  margin-bottom: 10px;
  color: #333;
}

.resultList {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
}

.resultList li {
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
}

.resultList li:last-child {
  border-bottom: none;
}

.success {
  color: #0f766e;
  background-color: #f0fdfa;
}

.failure {
  color: #9f1239;
  background-color: #fff1f2;
}

.actions {
  margin: 20px 0;
}

.sendButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.sendButton:hover {
  background-color: #5a0b9d;
}

.sendButton:disabled {
  background-color: #9d5abe;
  cursor: not-allowed;
}

.info {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  margin-top: 20px;
}

.info h3 {
  font-size: 1.1rem;
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.info p {
  color: #666;
  margin-bottom: 10px;
}

.info p:last-child {
  margin-bottom: 0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .stats {
    flex-direction: column;
    gap: 10px;
  }
  
  .statItem {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .statLabel {
    margin-bottom: 0;
  }
  
  .statValue {
    font-size: 1.2rem;
  }
}

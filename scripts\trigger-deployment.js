/**
 * Trigger Deployment Script for Ocean Soul Sparkles
 * 
 * This script triggers a deployment to Vercel using the configured webhook URLs.
 * It can be used in CI/CD workflows or manually to trigger deployments.
 * 
 * Usage:
 *   node scripts/trigger-deployment.js [environment]
 * 
 * Arguments:
 *   environment - The environment to deploy to (PRODUCTION or PREVIEW)
 *                 Defaults to PRODUCTION if not specified
 * 
 * Example:
 *   node scripts/trigger-deployment.js PRODUCTION
 */

const { triggerDeployment, DEPLOYMENT_ENVIRONMENTS } = require('../config/deployment');

// Get the environment from command line arguments
const environment = process.argv[2]?.toUpperCase() || 'PRODUCTION';

// Validate the environment
if (!DEPLOYMENT_ENVIRONMENTS[environment]) {
  console.error(`Error: Invalid environment "${environment}"`);
  console.error(`Valid environments: ${Object.keys(DEPLOYMENT_ENVIRONMENTS).join(', ')}`);
  process.exit(1);
}

// Display deployment information
console.log(`Triggering deployment to ${DEPLOYMENT_ENVIRONMENTS[environment].name} environment`);
console.log(`Target URL: ${DEPLOYMENT_ENVIRONMENTS[environment].url}`);
console.log(`Branch: ${DEPLOYMENT_ENVIRONMENTS[environment].branch}`);
console.log('');

// Trigger the deployment
triggerDeployment(environment)
  .then(response => {
    if (response.ok) {
      console.log(`✅ Deployment triggered successfully!`);
      console.log(`Status: ${response.status} ${response.statusText}`);
      return response.json();
    } else {
      console.error(`❌ Failed to trigger deployment`);
      console.error(`Status: ${response.status} ${response.statusText}`);
      return response.text().then(text => {
        try {
          return JSON.parse(text);
        } catch (e) {
          return text;
        }
      });
    }
  })
  .then(data => {
    if (data) {
      console.log('\nResponse:');
      console.log(typeof data === 'string' ? data : JSON.stringify(data, null, 2));
    }
  })
  .catch(error => {
    console.error('Error:', error.message);
    process.exit(1);
  });

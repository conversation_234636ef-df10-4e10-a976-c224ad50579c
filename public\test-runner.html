<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OceanSoulSparkles Website Testing</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      color: #333;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .test-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    button {
      background-color: #0066cc;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 5px;
      cursor: pointer;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    button:hover {
      background-color: #0055aa;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      overflow: auto;
      max-height: 400px;
    }
    .results {
      margin-top: 15px;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
    .tabs {
      margin-bottom: 20px;
    }
    .tab-button {
      background-color: #f1f1f1;
      color: #333;
      border: none;
      padding: 10px 15px;
      cursor: pointer;
    }
    .tab-button.active {
      background-color: #0066cc;
      color: white;
    }
  </style>
</head>
<body>
  <h1>OceanSoulSparkles Website Testing</h1>

  <div class="tabs">
    <button class="tab-button active" onclick="openTab(event, 'compatibility')">Compatibility</button>
    <button class="tab-button" onclick="openTab(event, 'accessibility')">Accessibility</button>
    <button class="tab-button" onclick="openTab(event, 'performance')">Performance</button>
    <button class="tab-button" onclick="openTab(event, 'security')">Security</button>
    <button class="tab-button" onclick="openTab(event, 'seo')">SEO</button>
    <button class="tab-button" onclick="openTab(event, 'payment')">Payment</button>
    <button class="tab-button" onclick="openTab(event, 'report')">Test Report</button>
  </div>

  <div id="compatibility" class="tab-content active">
    <div class="test-section">
      <h2>Compatibility Testing</h2>
      <p>Test the website across different viewport sizes and check for common compatibility issues.</p>
      <button onclick="loadScript('/js/compatibility-test-script.js', runCompatibilityTests)">Run Compatibility Tests</button>
      <div class="results">
        <h3>Results:</h3>
        <pre id="compatibility-results">Run tests to see results...</pre>
      </div>
    </div>
  </div>

  <div id="accessibility" class="tab-content">
    <div class="test-section">
      <h2>Accessibility Testing</h2>
      <p>Check the website for common accessibility issues according to WCAG guidelines.</p>
      <button onclick="loadScript('/js/accessibility-test.js', runAccessibilityTests)">Run Accessibility Tests</button>
      <div class="results">
        <h3>Results:</h3>
        <pre id="accessibility-results">Run tests to see results...</pre>
      </div>
    </div>
  </div>

  <div id="performance" class="tab-content">
    <div class="test-section">
      <h2>Performance Testing</h2>
      <p>Analyze the website's performance metrics.</p>
      <button onclick="runPerformanceTests()">Run Performance Tests</button>
      <div class="results">
        <h3>Results:</h3>
        <pre id="performance-results">Run tests to see results...</pre>
      </div>
    </div>
  </div>

  <div id="security" class="tab-content">
    <div class="test-section">
      <h2>Security Testing</h2>
      <p>Check the website for common security issues.</p>
      <button onclick="loadScript('/js/security-test.js', runSecurityTests)">Run Security Tests</button>
      <div class="results">
        <h3>Results:</h3>
        <pre id="security-results">Run tests to see results...</pre>
      </div>
    </div>
  </div>

  <div id="seo" class="tab-content">
    <div class="test-section">
      <h2>SEO Testing</h2>
      <p>Check the website for common SEO issues and optimization opportunities.</p>
      <button onclick="loadScript('/js/seo-test.js', runSeoTests)">Run SEO Tests</button>
      <div class="results">
        <h3>Results:</h3>
        <pre id="seo-results">Run tests to see results...</pre>
      </div>
    </div>
  </div>

  <div id="payment" class="tab-content">
    <div class="test-section">
      <h2>Payment Gateway Testing</h2>
      <p>Test payment gateway integration without processing real payments.</p>
      <button onclick="loadScript('/js/payment-gateway-test.js', runPaymentTests)">Initialize Payment Test Environment</button>
      <p>After initialization, navigate to the checkout page to test payment functionality.</p>
      <div class="results">
        <h3>Results:</h3>
        <pre id="payment-results">Run tests to see results...</pre>
      </div>
    </div>
  </div>

  <div id="report" class="tab-content">
    <div class="test-section">
      <h2>Test Report</h2>
      <p>Generate a comprehensive test report based on all test results.</p>
      <button onclick="generateTestReport()">Generate Test Report</button>
      <div class="results">
        <h3>Report:</h3>
        <pre id="test-report">Generate report to see results...</pre>
      </div>
    </div>
  </div>

  <script>
    // Tab functionality
    function openTab(evt, tabName) {
      const tabContents = document.getElementsByClassName("tab-content");
      for (let i = 0; i < tabContents.length; i++) {
        tabContents[i].classList.remove("active");
      }

      const tabButtons = document.getElementsByClassName("tab-button");
      for (let i = 0; i < tabButtons.length; i++) {
        tabButtons[i].classList.remove("active");
      }

      document.getElementById(tabName).classList.add("active");
      evt.currentTarget.classList.add("active");
    }

    // Load script dynamically
    function loadScript(src, callback) {
      const script = document.createElement('script');
      script.src = src;
      script.onload = callback;
      document.head.appendChild(script);
    }

    // Run compatibility tests
    function runCompatibilityTests() {
      if (typeof window.compatibilityTests !== 'undefined') {
        document.getElementById('compatibility-results').textContent = 'Running tests...';

        window.compatibilityTests.runAllTests().then(results => {
          const markdown = formatResultsAsMarkdown(results);
          document.getElementById('compatibility-results').textContent = markdown;
        }).catch(error => {
          document.getElementById('compatibility-results').textContent = 'Error running tests: ' + error.message;
        });
      } else {
        document.getElementById('compatibility-results').textContent = 'Compatibility test script not loaded properly.';
      }
    }

    // Run accessibility tests
    function runAccessibilityTests() {
      if (typeof window.accessibilityTests !== 'undefined') {
        document.getElementById('accessibility-results').textContent = 'Running tests...';

        const results = window.accessibilityTests.runAll();
        document.getElementById('accessibility-results').textContent = formatResultsAsMarkdown(results);
      } else {
        document.getElementById('accessibility-results').textContent = 'Accessibility test script not loaded properly.';
      }
    }

    // Run performance tests
    function runPerformanceTests() {
      document.getElementById('performance-results').textContent = 'Running tests...';

      // Basic performance metrics
      const perfEntries = performance.getEntriesByType('navigation');
      const paintEntries = performance.getEntriesByType('paint');

      let results = {
        navigation: perfEntries.length > 0 ? perfEntries[0] : null,
        paint: {},
        resources: performance.getEntriesByType('resource')
      };

      // Extract paint metrics
      for (const entry of paintEntries) {
        results.paint[entry.name] = entry.startTime;
      }

      document.getElementById('performance-results').textContent = JSON.stringify(results, null, 2);
    }

    // Run security tests
    function runSecurityTests() {
      if (typeof window.securityTests !== 'undefined') {
        document.getElementById('security-results').textContent = 'Running tests...';

        const results = window.securityTests.runAll();
        document.getElementById('security-results').textContent = formatResultsAsMarkdown(results);
      } else {
        document.getElementById('security-results').textContent = 'Security test script not loaded properly.';
      }
    }

    // Run SEO tests
    function runSeoTests() {
      if (typeof window.seoTests !== 'undefined') {
        document.getElementById('seo-results').textContent = 'Running tests...';

        const results = window.seoTests.runAll();
        document.getElementById('seo-results').textContent = formatResultsAsMarkdown(results);
      } else {
        document.getElementById('seo-results').textContent = 'SEO test script not loaded properly.';
      }
    }

    // Run payment tests
    function runPaymentTests() {
      document.getElementById('payment-results').textContent = 'Payment test environment initialized.\n\nNavigate to the checkout page to test payment functionality.';
    }

    // Generate test report
    function generateTestReport() {
      document.getElementById('test-report').textContent = 'Generating report...';

      // Combine all test results
      const report = `# OceanSoulSparkles Website Test Report

## Compatibility Testing
${document.getElementById('compatibility-results').textContent}

## Accessibility Testing
${document.getElementById('accessibility-results').textContent}

## Performance Testing
${document.getElementById('performance-results').textContent}

## Security Testing
${document.getElementById('security-results').textContent}

## SEO Testing
${document.getElementById('seo-results').textContent}

## Payment Gateway Testing
${document.getElementById('payment-results').textContent}
      `;

      document.getElementById('test-report').textContent = report;
    }
  </script>
</body>
</html>

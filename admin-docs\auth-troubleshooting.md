# Authentication Troubleshooting Guide

This guide provides solutions for common authentication issues in the OceanSoulSparkles admin panel.

## Common Authentication Issues

1. **"No authentication token provided in Authorization header"**
   - This error occurs when the API request doesn't include a valid authentication token
   - The token should be stored in sessionStorage and included in the Authorization header

2. **"Authentication failed" or "Unauthorized access"**
   - This error occurs when the token is invalid, expired, or the user doesn't have the required role
   - Try refreshing the token or logging in again

3. **"No active session found"**
   - This error occurs when there's no active Supabase session
   - You need to log in again to create a new session

## Troubleshooting Steps

### 1. Check Authentication Status

1. Navigate to `/admin/auth-check`
2. This page shows detailed information about your authentication status:
   - Current user and role
   - Token information (expiry, payload)
   - Authentication test results

### 2. Refresh the Token

1. On the auth-check page, click the "Refresh Token" button
2. This will attempt to refresh your authentication token
3. If successful, the page will reload with the new token information

### 3. Clear Browser Storage and Cookies

If refreshing the token doesn't work, try clearing browser storage:

1. Open your browser's developer tools (F12 or right-click > Inspect)
2. Go to the "Application" tab (Chrome/Edge) or "Storage" tab (Firefox)
3. Clear the following storage:
   - SessionStorage: `oss_auth_token_cache`, `oss_session`
   - LocalStorage: `oss_auth_token`, `sb_auth_token`
   - Cookies: `oss_auth_token`, `sb_auth_token`

### 4. Run the Authentication Fix Script

For a quick fix, run one of the authentication fix scripts:

#### Option 1: Standard Fix (Recommended)

1. Open your browser's developer console (F12 or right-click > Inspect > Console)
2. Copy and paste the following code:

```javascript
fetch('/scripts/fix-auth.js')
  .then(response => response.text())
  .then(script => eval(script))
  .catch(error => console.error('Error loading fix script:', error));
```

3. Press Enter to run the script
4. The script will:
   - Clear all stored tokens
   - Check for an active session
   - Store the token in sessionStorage
   - Refresh the page

#### Option 2: Direct Fix (If Option 1 doesn't work)

If the standard fix doesn't work, try the direct fix script:

1. Open your browser's developer console (F12 or right-click > Inspect > Console)
2. Copy and paste the following code:

```javascript
fetch('/scripts/auth-fix-direct.js')
  .then(response => response.text())
  .then(script => eval(script))
  .catch(error => console.error('Error loading fix script:', error));
```

3. Press Enter to run the script
4. The script will:
   - Clear all stored tokens
   - Get the current session directly from the API
   - Store the token in sessionStorage
   - Patch the fetch function to include the token in all admin API requests
   - Reload the page

#### Option 3: Use the Diagnostics Page

You can also run the fix script directly from the diagnostics page:

1. Navigate to `/admin/diagnostics`
2. In the "Manual Authentication Testing" section, click the "Run Direct Fix Script" button

### 5. Log Out and Log In Again

If all else fails, try logging out and logging in again:

1. Navigate to `/admin/logout` or click the "Log Out" button
2. Go to `/admin/login`
3. Enter your credentials and log in

## Technical Details

### Authentication Flow

1. User logs in with email/password
2. Supabase creates a session with access token and refresh token
3. The token is stored in sessionStorage as `oss_auth_token_cache`
4. API requests include the token in the Authorization header
5. The server validates the token and checks the user's role
6. If the token is valid and the user has the required role, the request is processed

### Token Storage

The authentication token is stored in sessionStorage with the key `oss_auth_token_cache`. The value is a JSON object with the following properties:

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiry": 1621234567890,
  "refreshed": 1621234567890
}
```

### API Authentication

API requests to `/api/admin/*` endpoints require authentication. The token is included in the Authorization header:

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Still Having Issues?

If you're still experiencing authentication issues after trying these steps, please contact the system <NAME_EMAIL>.

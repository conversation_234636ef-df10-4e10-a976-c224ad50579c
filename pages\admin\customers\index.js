import { useState, useEffect } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import CustomerList from '@/components/admin/CustomerList'
import styles from '@/styles/admin/CustomersPage.module.css'

export default function CustomersPage() {
  return (
    <ProtectedRoute>
      <AdminLayout title="Customers">
        <div className={styles.customersPage}>
          <CustomerList />
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}

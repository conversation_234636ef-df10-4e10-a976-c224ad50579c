import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import supabase from '@/lib/supabase';
import styles from '@/styles/admin/AuthTest.module.css';

export default function AuthTest() {
  const { user, role, loading, isAdmin, isAuthenticated, signOut } = useAuth();
  const [testResults, setTestResults] = useState({
    loginStatus: null,
    sessionPersistence: null,
    protectedRoutes: null,
    roleBasedAccess: null
  });
  const [testInProgress, setTestInProgress] = useState(false);
  const [testCompleted, setTestCompleted] = useState(false);

  // Test login functionality
  const testLogin = async () => {
    try {
      setTestInProgress(true);

      // Check if user is authenticated
      if (isAuthenticated && user) {
        setTestResults(prev => ({
          ...prev,
          loginStatus: {
            success: true,
            message: `Successfully authenticated as ${user.email}`
          }
        }));
      } else {
        setTestResults(prev => ({
          ...prev,
          loginStatus: {
            success: false,
            message: 'Not authenticated'
          }
        }));
      }
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        loginStatus: {
          success: false,
          message: `Error: ${error.message}`
        }
      }));
    }
  };

  // Test session persistence
  const testSessionPersistence = async () => {
    try {
      const client = supabase;
      // Get current session
      const { data: { session } } = await client.auth.getSession();

      if (session) {
        setTestResults(prev => ({
          ...prev,
          sessionPersistence: {
            success: true,
            message: `Session exists and expires at ${new Date(session.expires_at * 1000).toLocaleString()}`
          }
        }));
      } else {
        setTestResults(prev => ({
          ...prev,
          sessionPersistence: {
            success: false,
            message: 'No active session found'
          }
        }));
      }
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        sessionPersistence: {
          success: false,
          message: `Error: ${error.message}`
        }
      }));
    }
  };

  // Test protected routes
  const testProtectedRoutes = async () => {
    try {
      const client = supabase;
      // Try to access a protected resource
      const { data, error } = await client
        .from('user_roles')
        .select('*')
        .limit(1);

      if (error) {
        setTestResults(prev => ({
          ...prev,
          protectedRoutes: {
            success: false,
            message: `Access denied: ${error.message}`
          }
        }));
      } else {
        setTestResults(prev => ({
          ...prev,
          protectedRoutes: {
            success: true,
            message: 'Successfully accessed protected resource'
          }
        }));
      }
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        protectedRoutes: {
          success: false,
          message: `Error: ${error.message}`
        }
      }));
    }
  };

  // Test role-based access
  const testRoleBasedAccess = async () => {
    try {
      if (role) {
        // Check if user has the correct role
        setTestResults(prev => ({
          ...prev,
          roleBasedAccess: {
            success: true,
            message: `User has role: ${role}`
          }
        }));
      } else {
        setTestResults(prev => ({
          ...prev,
          roleBasedAccess: {
            success: false,
            message: 'User has no role assigned'
          }
        }));
      }
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        roleBasedAccess: {
          success: false,
          message: `Error: ${error.message}`
        }
      }));
    } finally {
      setTestInProgress(false);
      setTestCompleted(true);
    }
  };

  // Run all tests
  const runAllTests = async () => {
    setTestInProgress(true);
    setTestCompleted(false);

    await testLogin();
    await testSessionPersistence();
    await testProtectedRoutes();
    await testRoleBasedAccess();
  };

  return (
    <div className={styles.authTest}>
      <h2>Authentication System Test</h2>

      <div className={styles.testControls}>
        <button
          className={styles.testButton}
          onClick={runAllTests}
          disabled={testInProgress}
        >
          {testInProgress ? 'Testing...' : 'Run Authentication Tests'}
        </button>
      </div>

      {testCompleted && (
        <div className={styles.testResults}>
          <h3>Test Results</h3>

          <div className={styles.testResult}>
            <h4>Login Functionality</h4>
            {testResults.loginStatus && (
              <div className={`${styles.resultMessage} ${testResults.loginStatus.success ? styles.success : styles.error}`}>
                {testResults.loginStatus.message}
              </div>
            )}
          </div>

          <div className={styles.testResult}>
            <h4>Session Persistence</h4>
            {testResults.sessionPersistence && (
              <div className={`${styles.resultMessage} ${testResults.sessionPersistence.success ? styles.success : styles.error}`}>
                {testResults.sessionPersistence.message}
              </div>
            )}
          </div>

          <div className={styles.testResult}>
            <h4>Protected Routes</h4>
            {testResults.protectedRoutes && (
              <div className={`${styles.resultMessage} ${testResults.protectedRoutes.success ? styles.success : styles.error}`}>
                {testResults.protectedRoutes.message}
              </div>
            )}
          </div>

          <div className={styles.testResult}>
            <h4>Role-Based Access Control</h4>
            {testResults.roleBasedAccess && (
              <div className={`${styles.resultMessage} ${testResults.roleBasedAccess.success ? styles.success : styles.error}`}>
                {testResults.roleBasedAccess.message}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

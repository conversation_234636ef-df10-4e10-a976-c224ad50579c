# Environment Variables

This document describes the environment variables used in the Ocean Soul Sparkles application.

## Overview

Environment variables are used to configure the application for different environments (development, staging, production). They are stored in `.env` files or set in the deployment environment.

## Required Environment Variables

### Supabase Configuration

| Variable | Description | Example |
|----------|-------------|---------|
| `NEXT_PUBLIC_SUPABASE_URL` | The URL of your Supabase project | `https://xyzabc123.supabase.co` |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | The anonymous key for your Supabase project | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `SUPABASE_SERVICE_ROLE_KEY` | The service role key for your Supabase project (server-side only) | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |

### Site Configuration

| Variable | Description | Example |
|----------|-------------|---------|
| `NEXT_PUBLIC_SITE_URL` | The URL of your website | `https://oceansoulsparkles.com.au` |
| `NEXT_PUBLIC_ADMIN_URL` | The URL of your admin panel | `https://admin.oceansoulsparkles.com.au` |

## Optional Environment Variables

### OneSignal Configuration

| Variable | Description | Example |
|----------|-------------|---------|
| `NEXT_PUBLIC_ONESIGNAL_APP_ID` | The app ID for OneSignal notifications | `************************************` |
| `ONESIGNAL_API_KEY` | The API key for OneSignal | `abcdef1234567890abcdef1234567890` |
| `ONESIGNAL_REST_API_KEY` | The REST API key for OneSignal | `abcdef1234567890abcdef1234567890` |

### Email Configuration

| Variable | Description | Example |
|----------|-------------|---------|
| `EMAIL_SERVER_HOST` | The SMTP server host | `smtp.example.com` |
| `EMAIL_SERVER_PORT` | The SMTP server port | `587` |
| `EMAIL_SERVER_USER` | The SMTP server username | `<EMAIL>` |
| `EMAIL_SERVER_PASSWORD` | The SMTP server password | `password123` |
| `EMAIL_FROM` | The email address to send from | `<EMAIL>` |

### Development Configuration

| Variable | Description | Example |
|----------|-------------|---------|
| `NEXT_PUBLIC_DEV_MODE` | Enable development mode | `true` |
| `NEXT_PUBLIC_DEBUG_AUTH` | Enable authentication debugging | `true` |
| `NEXT_PUBLIC_ALLOW_CROSS_ORIGIN` | Allow cross-origin requests | `true` |
| `NEXT_PUBLIC_DEV_URL` | The URL of your development server | `http://localhost:3000` |

## Authentication Recovery Variables

These variables control the automatic authentication recovery system:

| Variable | Description | Example |
|----------|-------------|---------|
| `ENABLE_AUTH_BYPASS` | Enable development authentication bypass | `true` |
| `ENABLE_AUTH_AUTO_RECOVERY` | Enable automatic authentication recovery | `true` |

**Usage:**
- `ENABLE_AUTH_BYPASS`: Only use in development. Allows bypassing authentication for testing.
- `ENABLE_AUTH_AUTO_RECOVERY`: Enables the system to automatically detect and fix stuck authentication states.

## Environment Files

The application uses the following environment files:

- `.env`: Default environment variables for all environments
- `.env.local`: Local environment variables (not committed to Git)
- `.env.development`: Development environment variables
- `.env.production`: Production environment variables

## Setting Up Environment Variables

### Local Development

1. Create a `.env.local` file in the project root
2. Add the required environment variables

Example `.env.local` file:

```
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_ADMIN_URL=http://localhost:3000/admin
NEXT_PUBLIC_DEV_MODE=true
```

### Vercel Deployment

1. Go to your Vercel project settings
2. Navigate to the "Environment Variables" section
3. Add your environment variables

## Security Considerations

- **Never commit sensitive environment variables to Git**
- **Keep the `SUPABASE_SERVICE_ROLE_KEY` secure** - it bypasses RLS policies
- Use different keys for development and production
- Prefix client-side variables with `NEXT_PUBLIC_`
- Server-side variables (without `NEXT_PUBLIC_`) are not exposed to the client

## Accessing Environment Variables

### Client-Side

```javascript
// Access client-side environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
```

### Server-Side

```javascript
// Access server-side environment variables
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
```

## Troubleshooting

### Environment Variables Not Working

1. Ensure you've prefixed client-side variables with `NEXT_PUBLIC_`
2. Restart the development server after changing environment variables
3. Check that the variables are set in the correct environment file

### Environment Variables Not Available in Production

1. Check that the variables are set in your Vercel project settings
2. Ensure you've deployed the latest version of your application
3. Check the deployment logs for any errors

## Historical Environment Variables

The following environment variables were used in previous versions and are no longer needed:

| Variable | Description | Current Equivalent |
|----------|-------------|-------------------|
| `NEXT_PUBLIC_LEGACY_URL` | The URL of the legacy authentication server | `NEXT_PUBLIC_SUPABASE_URL` |
| `NEXT_PUBLIC_LEGACY_KEY` | The public key for the legacy server | `NEXT_PUBLIC_SUPABASE_ANON_KEY` |
| `LEGACY_SERVICE_KEY` | The service key for the legacy server | `SUPABASE_SERVICE_ROLE_KEY` |
| `LEGACY_ADMIN_KEY` | The admin key for the legacy server | `SUPABASE_SERVICE_ROLE_KEY` |

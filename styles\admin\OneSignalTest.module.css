.oneSignalTest {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.oneSignalTest h2 {
  margin-top: 0;
  color: #333;
  font-size: 1.5rem;
  margin-bottom: 20px;
}

.oneSignalTest h3 {
  font-size: 1.2rem;
  color: #495057;
  margin-top: 0;
  margin-bottom: 15px;
}

.oneSignalTest h4 {
  font-size: 1rem;
  color: #495057;
  margin-top: 0;
  margin-bottom: 10px;
}

.statusSection,
.actionsSection,
.tagsSection {
  background-color: white;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.statusGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.statusItem {
  display: flex;
  flex-direction: column;
}

.statusLabel {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 5px;
}

.statusValue {
  font-weight: 600;
  font-size: 1rem;
}

.success {
  color: #28a745;
}

.warning {
  color: #ffc107;
}

.error {
  color: #dc3545;
}

.message {
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.actionButtons {
  display: flex;
  gap: 10px;
}

.actionButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.actionButton:hover {
  background-color: #5a0b9d;
}

.actionButton:disabled {
  background-color: #b78de0;
  cursor: not-allowed;
}

.tagsList {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.tag {
  background-color: #e9ecef;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.tagKey {
  font-weight: 600;
  margin-right: 5px;
  color: #495057;
}

.tagValue {
  color: #6c757d;
}

.noTags {
  color: #6c757d;
  font-style: italic;
  margin-bottom: 20px;
}

.addTagForm {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.formRow {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.formGroup {
  flex: 1;
}

.formGroup label {
  display: block;
  margin-bottom: 5px;
  font-size: 0.9rem;
  color: #495057;
}

.input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.95rem;
}

.addButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  height: 38px;
}

.addButton:hover {
  background-color: #5a0b9d;
}

.addButton:disabled {
  background-color: #b78de0;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .statusGrid {
    grid-template-columns: 1fr;
  }
  
  .formRow {
    flex-direction: column;
    gap: 15px;
  }
  
  .actionButtons {
    flex-direction: column;
  }
}

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import styles from '@/styles/admin/marketing/SegmentList.module.css'
import { debounce } from 'lodash'

export default function SegmentList() {
  const router = useRouter()
  const [segments, setSegments] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [search, setSearch] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0
  })

  // Debounce search input
  const debouncedSetSearch = useCallback(
    debounce((value) => {
      setDebouncedSearch(value)
    }, 500),
    []
  )

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearch(e.target.value)
    debouncedSetSearch(e.target.value)
  }

  // Handle sort change
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  // Render sort indicator
  const renderSortIndicator = (field) => {
    if (sortBy !== field) return null
    return sortOrder === 'asc' ? ' ↑' : ' ↓'
  }

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPagination({ ...pagination, page: newPage })
  }

  // Fetch segments
  const fetchSegments = async () => {
    setLoading(true)
    setError(null)

    try {
      const offset = (pagination.page - 1) * pagination.limit
      const queryParams = new URLSearchParams({
        limit: pagination.limit,
        offset,
        sort_by: sortBy,
        sort_order: sortOrder
      })

      if (debouncedSearch) {
        queryParams.append('search', debouncedSearch)
      }

      const response = await fetch(`/api/marketing/segments?${queryParams.toString()}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch segments')
      }

      const data = await response.json()
      setSegments(data.segments || [])
      setPagination({ ...pagination, total: data.total || 0 })
    } catch (error) {
      console.error('Error fetching segments:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  // Fetch segments when dependencies change
  useEffect(() => {
    fetchSegments()
  }, [debouncedSearch, sortBy, sortOrder, pagination.page, pagination.limit])

  // Calculate total pages
  const totalPages = Math.ceil(pagination.total / pagination.limit)

  return (
    <AdminLayout>
      <div className={styles.segmentList}>
        <div className={styles.header}>
          <h2>Customer Segments</h2>
          <div className={styles.actions}>
            <Link href="/admin/marketing/segments/new">
              <a className={styles.addButton}>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                Create Segment
              </a>
            </Link>
          </div>
        </div>

        <div className={styles.searchContainer}>
          <input
            type="text"
            placeholder="Search segments..."
            value={search}
            onChange={handleSearchChange}
            className={styles.searchInput}
          />
        </div>

        {error && (
          <div className={styles.error}>
            Error: {error}
          </div>
        )}

        {loading ? (
          <div className={styles.loading}>Loading segments...</div>
        ) : (
          <>
            <div className={styles.tableContainer}>
              <table className={styles.segmentTable}>
                <thead>
                  <tr>
                    <th onClick={() => handleSort('name')}>
                      Name {renderSortIndicator('name')}
                    </th>
                    <th onClick={() => handleSort('description')}>
                      Description {renderSortIndicator('description')}
                    </th>
                    <th>
                      Customers
                    </th>
                    <th onClick={() => handleSort('created_at')}>
                      Created {renderSortIndicator('created_at')}
                    </th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {segments.length === 0 ? (
                    <tr>
                      <td colSpan="5" className={styles.noResults}>
                        No segments found. Create your first segment to get started.
                      </td>
                    </tr>
                  ) : (
                    segments.map((segment) => (
                      <tr key={segment.id}>
                        <td>{segment.name}</td>
                        <td className={styles.descriptionCell}>
                          {segment.description || '-'}
                        </td>
                        <td>{segment.customer_count || 0}</td>
                        <td>
                          {new Date(segment.created_at).toLocaleDateString()}
                        </td>
                        <td className={styles.actions}>
                          <Link href={`/admin/marketing/segments/${segment.id}`}>
                            <a className={styles.viewButton}>View</a>
                          </Link>
                          <Link href={`/admin/marketing/segments/${segment.id}/edit`}>
                            <a className={styles.editButton}>Edit</a>
                          </Link>
                          <Link href={`/admin/marketing/campaigns/new?segment=${segment.id}`}>
                            <a className={styles.campaignButton}>Create Campaign</a>
                          </Link>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {totalPages > 1 && (
              <div className={styles.pagination}>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(1)}
                  disabled={pagination.page === 1}
                >
                  &laquo;
                </button>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                >
                  &lsaquo;
                </button>
                <span className={styles.paginationInfo}>
                  Page {pagination.page} of {totalPages}
                </span>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === totalPages}
                >
                  &rsaquo;
                </button>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(totalPages)}
                  disabled={pagination.page === totalPages}
                >
                  &raquo;
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </AdminLayout>
  )
}

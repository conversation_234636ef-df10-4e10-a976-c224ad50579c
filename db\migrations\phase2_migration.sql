-- Ocean Soul Sparkles - Phase 2 Migration Script
-- This script updates the database schema for enhanced booking and customer management

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable text search extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- =============================================
-- BOOKING ENHANCEMENTS
-- =============================================

-- Update bookings table with new status options
DO $$ 
BEGIN
  -- Check if the constraint exists
  IF EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'bookings_status_check' AND conrelid = 'public.bookings'::regclass
  ) THEN
    -- Drop the existing constraint
    ALTER TABLE public.bookings DROP CONSTRAINT bookings_status_check;
    
    -- Add the new constraint with expanded status options
    ALTER TABLE public.bookings ADD CONSTRAINT bookings_status_check 
    CHECK (status IN ('pending', 'confirmed', 'in_progress', 'completed', 'canceled', 'no_show', 'rescheduled'));
  END IF;
END $$;

-- Add recurring booking fields to bookings table
ALTER TABLE public.bookings 
ADD COLUMN IF NOT EXISTS is_recurring BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS recurring_pattern TEXT,
ADD COLUMN IF NOT EXISTS recurring_end_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS parent_booking_id UUID REFERENCES public.bookings(id),
ADD COLUMN IF NOT EXISTS booking_series_id UUID;

-- Create booking_status_history table
CREATE TABLE IF NOT EXISTS public.booking_status_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  previous_status TEXT,
  new_status TEXT NOT NULL,
  changed_by UUID REFERENCES auth.users(id),
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create booking_conflicts table
CREATE TABLE IF NOT EXISTS public.booking_conflicts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  conflicting_booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  conflict_type TEXT NOT NULL,
  resolved BOOLEAN DEFAULT FALSE,
  resolution_notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create locations table
CREATE TABLE IF NOT EXISTS public.locations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  address TEXT,
  city TEXT,
  state TEXT,
  postal_code TEXT,
  country TEXT DEFAULT 'Australia',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- CUSTOMER ENHANCEMENTS
-- =============================================

-- Enhance customers table with additional fields
ALTER TABLE public.customers
ADD COLUMN IF NOT EXISTS birth_date DATE,
ADD COLUMN IF NOT EXISTS occupation TEXT,
ADD COLUMN IF NOT EXISTS referral_source TEXT,
ADD COLUMN IF NOT EXISTS customer_since DATE DEFAULT CURRENT_DATE,
ADD COLUMN IF NOT EXISTS lifetime_value DECIMAL(10, 2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_booking_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS booking_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS vip BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS profile_image_url TEXT;

-- Create customer_tags table
CREATE TABLE IF NOT EXISTS public.customer_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  color TEXT DEFAULT '#6a0dad',
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create customer_tag_assignments table
CREATE TABLE IF NOT EXISTS public.customer_tag_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES public.customer_tags(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(customer_id, tag_id)
);

-- =============================================
-- NOTIFICATION SYSTEM
-- =============================================

-- Create notification_templates table
CREATE TABLE IF NOT EXISTS public.notification_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  type TEXT NOT NULL,
  subject TEXT,
  body TEXT NOT NULL,
  variables JSONB,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create notification_history table
CREATE TABLE IF NOT EXISTS public.notification_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  recipient_id UUID,
  recipient_email TEXT,
  recipient_phone TEXT,
  template_id UUID REFERENCES public.notification_templates(id),
  subject TEXT,
  body TEXT,
  status TEXT,
  error_message TEXT,
  related_entity_type TEXT,
  related_entity_id UUID,
  sent_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- INDEXES
-- =============================================

-- Booking indexes
CREATE INDEX IF NOT EXISTS bookings_booking_series_id_idx ON public.bookings(booking_series_id);
CREATE INDEX IF NOT EXISTS bookings_parent_booking_id_idx ON public.bookings(parent_booking_id);
CREATE INDEX IF NOT EXISTS bookings_is_recurring_idx ON public.bookings(is_recurring);
CREATE INDEX IF NOT EXISTS booking_status_history_booking_id_idx ON public.booking_status_history(booking_id);
CREATE INDEX IF NOT EXISTS booking_conflicts_booking_id_idx ON public.booking_conflicts(booking_id);
CREATE INDEX IF NOT EXISTS booking_conflicts_conflicting_booking_id_idx ON public.booking_conflicts(conflicting_booking_id);
CREATE INDEX IF NOT EXISTS booking_conflicts_resolved_idx ON public.booking_conflicts(resolved);
CREATE INDEX IF NOT EXISTS locations_name_idx ON public.locations(name);
CREATE INDEX IF NOT EXISTS locations_is_active_idx ON public.locations(is_active);

-- Customer indexes
CREATE INDEX IF NOT EXISTS customers_vip_idx ON public.customers(vip);
CREATE INDEX IF NOT EXISTS customers_last_booking_date_idx ON public.customers(last_booking_date);
CREATE INDEX IF NOT EXISTS customers_booking_count_idx ON public.customers(booking_count);
CREATE INDEX IF NOT EXISTS customers_lifetime_value_idx ON public.customers(lifetime_value);
CREATE INDEX IF NOT EXISTS customers_customer_since_idx ON public.customers(customer_since);
CREATE INDEX IF NOT EXISTS customer_tag_assignments_customer_id_idx ON public.customer_tag_assignments(customer_id);
CREATE INDEX IF NOT EXISTS customer_tag_assignments_tag_id_idx ON public.customer_tag_assignments(tag_id);

-- Create search index for customers
CREATE INDEX IF NOT EXISTS customers_search_idx ON public.customers 
USING GIN ((
  setweight(to_tsvector('english', coalesce(name, '')), 'A') ||
  setweight(to_tsvector('english', coalesce(email, '')), 'B') ||
  setweight(to_tsvector('english', coalesce(phone, '')), 'C') ||
  setweight(to_tsvector('english', coalesce(notes, '')), 'D')
));

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Create function to record booking status changes
CREATE OR REPLACE FUNCTION public.record_booking_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Only record if status has changed
  IF OLD.status <> NEW.status THEN
    INSERT INTO public.booking_status_history (
      booking_id,
      previous_status,
      new_status,
      changed_by,
      notes
    ) VALUES (
      NEW.id,
      OLD.status,
      NEW.status,
      auth.uid(),
      'Status changed via booking update'
    );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to call the function
DROP TRIGGER IF EXISTS booking_status_change_trigger ON public.bookings;
CREATE TRIGGER booking_status_change_trigger
AFTER UPDATE OF status ON public.bookings
FOR EACH ROW
EXECUTE FUNCTION public.record_booking_status_change();

-- Create function to update customer statistics
CREATE OR REPLACE FUNCTION update_customer_statistics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update customer statistics when a booking is created, updated, or deleted
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    -- Update last_booking_date and booking_count
    UPDATE public.customers
    SET 
      last_booking_date = (
        SELECT MAX(start_time)
        FROM public.bookings
        WHERE customer_id = NEW.customer_id
          AND status != 'canceled'
      ),
      booking_count = (
        SELECT COUNT(*)
        FROM public.bookings
        WHERE customer_id = NEW.customer_id
          AND status != 'canceled'
      )
    WHERE id = NEW.customer_id;
    
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Update last_booking_date and booking_count
    UPDATE public.customers
    SET 
      last_booking_date = (
        SELECT MAX(start_time)
        FROM public.bookings
        WHERE customer_id = OLD.customer_id
          AND status != 'canceled'
      ),
      booking_count = (
        SELECT COUNT(*)
        FROM public.bookings
        WHERE customer_id = OLD.customer_id
          AND status != 'canceled'
      )
    WHERE id = OLD.customer_id;
    
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for customer statistics
DROP TRIGGER IF EXISTS booking_customer_statistics_trigger ON public.bookings;
CREATE TRIGGER booking_customer_statistics_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.bookings
FOR EACH ROW
EXECUTE FUNCTION update_customer_statistics();

-- =============================================
-- DEFAULT DATA
-- =============================================

-- Insert default locations
INSERT INTO public.locations (name, is_active)
VALUES 
  ('Studio', true),
  ('Client Location', true),
  ('Online', true),
  ('Outdoor', true)
ON CONFLICT (id) DO NOTHING;

-- Insert default customer tags
INSERT INTO public.customer_tags (name, color, description)
VALUES 
  ('VIP', '#FFD700', 'Very important customers'),
  ('Regular', '#4CAF50', 'Regular customers'),
  ('New', '#2196F3', 'New customers'),
  ('Inactive', '#9E9E9E', 'Customers who haven''t booked in a while'),
  ('High Value', '#F44336', 'Customers with high lifetime value')
ON CONFLICT (name) DO NOTHING;

-- Insert default notification templates
INSERT INTO public.notification_templates (name, type, subject, body, variables)
VALUES 
  ('booking_confirmation', 'email', 'Your booking is confirmed', 'Hello {{customer_name}}, your booking for {{service_name}} on {{booking_date}} at {{booking_time}} has been confirmed.', '{"customer_name":"","service_name":"","booking_date":"","booking_time":""}'),
  ('booking_reminder', 'email', 'Reminder: Your upcoming appointment', 'Hello {{customer_name}}, this is a reminder about your upcoming appointment for {{service_name}} on {{booking_date}} at {{booking_time}}.', '{"customer_name":"","service_name":"","booking_date":"","booking_time":""}'),
  ('booking_canceled', 'email', 'Your booking has been canceled', 'Hello {{customer_name}}, your booking for {{service_name}} on {{booking_date}} at {{booking_time}} has been canceled.', '{"customer_name":"","service_name":"","booking_date":"","booking_time":""}')
ON CONFLICT (id) DO NOTHING;

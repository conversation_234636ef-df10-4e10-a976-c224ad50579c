.diagnosticResults {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.resultCategory {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.resultCategory h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 1.2rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.resultList {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.resultItem {
  display: flex;
  flex-direction: column;
  padding: 10px;
  border-radius: 4px;
  background-color: rgba(250, 250, 250, 0.7);
}

.resultName {
  font-weight: 600;
  margin-bottom: 5px;
  color: #555;
  text-transform: capitalize;
}

.resultValue {
  font-family: monospace;
  padding: 8px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-word;
}

.success {
  background-color: rgba(220, 255, 220, 0.7);
  color: #2a6b2a;
}

.failure {
  background-color: rgba(255, 220, 220, 0.7);
  color: #6b2a2a;
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.select {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: white;
  font-size: 14px;
}

.button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  background-color: #6a0dad;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button:hover {
  background-color: #5a0b9d;
}

.button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error {
  background-color: rgba(255, 220, 220, 0.7);
  color: #6b2a2a;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.warning {
  background-color: rgba(255, 243, 205, 0.7);
  color: #856404;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.noResults {
  text-align: center;
  padding: 40px;
  color: #666;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #666;
}

.loginButton {
  margin-top: 10px;
  padding: 6px 12px;
  border-radius: 4px;
  border: none;
  background-color: #0066cc;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.loginButton:hover {
  background-color: #0055aa;
}

.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dashboardHeader h2 {
  margin: 0;
}

.sessionTesting {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.sessionButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.sessionResult {
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-word;
}

.authFixInfo {
  margin-top: 20px;
  padding: 15px;
  border-radius: 4px;
  background-color: rgba(240, 240, 255, 0.7);
  border-left: 4px solid #6a0dad;
}

.codeSnippet {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.9rem;
  overflow-x: auto;
  margin: 10px 0;
}

.infoText {
  font-size: 0.9rem;
  color: #666;
  font-style: italic;
}

@media (max-width: 768px) {
  .dashboardHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .controls {
    width: 100%;
  }

  .select, .button {
    flex: 1;
  }
}

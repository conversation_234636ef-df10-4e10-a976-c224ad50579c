name: Deploy to Vercel

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'PRODUCTION'
        type: choice
        options:
          - PRODUCTION
          - PREVIEW

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Trigger Vercel deployment
        run: |
          # Get the environment from workflow dispatch input or default to PRODUCTION
          ENVIRONMENT="${{ github.event.inputs.environment || 'PRODUCTION' }}"
          echo "Deploying to $ENVIRONMENT environment"
          
          # Get the webhook URL from config file
          WEBHOOK_URL=$(node -e "console.log(require('./config/deployment').VERCEL_WEBHOOKS.$ENVIRONMENT)")
          
          if [ -z "$WEBHOOK_URL" ]; then
            echo "Error: No webhook URL configured for environment: $ENVIRONMENT"
            exit 1
          fi
          
          echo "Triggering deployment using webhook..."
          curl -X POST "$WEBHOOK_URL"
          
          echo "Deployment triggered successfully!"

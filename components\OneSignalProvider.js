import React, { createContext, useContext, useState, useEffect } from 'react';
import ClientOnly from './ClientOnly';

// Create context for OneSignal
const OneSignalContext = createContext({
  isInitialized: false,
  isAvailable: false,
  isSubscribed: false,
  permissionStatus: null,
});

/**
 * OneSignal Provider Component
 *
 * This component provides OneSignal state and methods to its children,
 * ensuring that OneSignal is only accessed on the client side and
 * properly initialized before use.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @returns {React.ReactElement} - Provider component
 */
function OneSignalProviderContent({ children }) {
  // State for OneSignal status
  const [state, setState] = useState({
    isInitialized: false,
    isAvailable: false,
    isSubscribed: false,
    permissionStatus: null,
  });

  // Effect to check OneSignal status
  useEffect(() => {
    // Skip if not in browser
    if (typeof window === 'undefined') return;

    // Skip in development
    if (process.env.NODE_ENV === 'development') {
      setState({
        isInitialized: true,
        isAvailable: true,
        isSubscribed: false,
        permissionStatus: 'default',
      });
      return;
    }

    // Function to check OneSignal status
    const checkOneSignalStatus = async () => {
      try {
        // Check if OneSignal is available
        if (!window.OneSignal) {
          setState(prev => ({ ...prev, isAvailable: false }));
          return;
        }

        // Check if OneSignal is initialized
        const isInitialized = window.__ONESIGNAL_INITIALIZED__ === true;

        // Check if OneSignal is a mock
        const isMock = window.OneSignal._mock === true;

        // Update state
        setState({
          isInitialized,
          isAvailable: !isMock,
          isSubscribed: false, // Will be updated below if available
          permissionStatus: 'default', // Will be updated below if available
        });

        // If OneSignal is available and initialized, get subscription status
        if (isInitialized && !isMock && typeof window.OneSignal.getNotificationPermission === 'function') {
          try {
            // Get permission status
            const permissionStatus = await window.OneSignal.getNotificationPermission();

            // Get subscription status
            const isSubscribed = await window.OneSignal.isPushNotificationsEnabled();

            // Update state
            setState(prev => ({
              ...prev,
              isSubscribed,
              permissionStatus,
            }));
          } catch (error) {
            console.error('[OneSignalProvider] Error getting subscription status:', error);
          }
        }
      } catch (error) {
        console.error('[OneSignalProvider] Error checking OneSignal status:', error);
      }
    };

    // Check status immediately
    checkOneSignalStatus();

    // Listen for OneSignal initialization
    const handleInitialized = () => {
      checkOneSignalStatus();
    };

    // Add event listener
    document.addEventListener('onesignal:initialized', handleInitialized);

    // Cleanup
    return () => {
      document.removeEventListener('onesignal:initialized', handleInitialized);
    };
  }, []);

  // Return provider with current state
  return (
    <OneSignalContext.Provider value={state}>
      {children}
    </OneSignalContext.Provider>
  );
}

/**
 * OneSignal Provider Component (Client-Side Only)
 *
 * This component wraps the OneSignalProviderContent in a ClientOnly component
 * to ensure it only renders on the client side.
 */
export function OneSignalProvider({ children }) {
  // In production, use the client-only wrapper
  if (process.env.NODE_ENV === 'production') {
    return (
      <ClientOnly fallback={children}>
        <OneSignalProviderContent>
          {children}
        </OneSignalProviderContent>
      </ClientOnly>
    );
  }

  // In development, just render the children directly to avoid any issues
  return children;
}

/**
 * Hook to use OneSignal context
 *
 * @returns {Object} OneSignal context
 */
export function useOneSignalContext() {
  return useContext(OneSignalContext);
}

export default OneSignalProvider;

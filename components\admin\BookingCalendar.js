import { useState, useEffect, useCallback, useMemo } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import moment from 'moment';
import authTokenManager from '@/lib/auth-token-manager';
import { moveBooking } from '@/lib/booking-utils';
import { adjustColor, getTextColor } from '@/lib/color-utils';
import { STATUS_COLORS } from '@/lib/booking-status';
import BookingEventWrapper from './BookingEventWrapper';
import DraggableEvent from './DraggableEvent';
import styles from '@/styles/admin/BookingCalendar.module.css';
import 'react-big-calendar/lib/css/react-big-calendar.css';

// Setup the localizer for the calendar
const localizer = momentLocalizer(moment);

export default function BookingCalendar({ onSelectBooking, onSelectSlot, refreshKey }) {
  const [bookings, setBookings] = useState([]);
  const [services, setServices] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [view, setView] = useState('month');
  const [date, setDate] = useState(new Date());
  const [draggedEvent, setDraggedEvent] = useState(null);
  const [resizeError, setResizeError] = useState(null);

  // Fetch bookings and services data with improved performance
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null); // Clear any previous errors

      // Get auth token using the centralized auth token manager
      const authToken = await authTokenManager.getAuthToken();

      // Prepare headers for API requests
      const headers = {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      };

      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }

      // Fetch services and bookings in parallel for better performance
      const [servicesResponse, bookingsResponse] = await Promise.all([
        fetch('/api/admin/services', {
          credentials: 'include',
          headers
        }),
        fetch('/api/admin/bookings', {
          credentials: 'include',
          headers
        })
      ]);

      // Handle services response
      if (!servicesResponse.ok) {
        throw new Error(`Failed to fetch services: ${servicesResponse.statusText}`);
      }

      // Handle bookings response
      if (!bookingsResponse.ok) {
        throw new Error(`Failed to fetch bookings: ${bookingsResponse.statusText}`);
      }

      // Parse responses
      const [servicesData, bookingsData] = await Promise.all([
        servicesResponse.json(),
        bookingsResponse.json()
      ]);

      // Create a map of service IDs to service data
      const serviceMap = {};
      servicesData.services.forEach(service => {
        serviceMap[service.id] = service;
      });

      // Add a default service for cases where the service ID doesn't exist
      serviceMap['default'] = {
        id: 'default',
        name: 'Unknown Service',
        color: '#6a0dad'
      };

      setServices(serviceMap);

      // Transform bookings data for the calendar
      const formattedBookings = (bookingsData.bookings || []).map(booking => {
        // Parse dates
        const startDate = new Date(booking.start_time);
        const endDate = new Date(booking.end_time);

        // Get service data, use default if not found
        const serviceData = serviceMap[booking.service_id] || serviceMap['default'];

        return {
          id: booking.id,
          title: `${booking.customers?.name || 'Unknown'} - ${serviceData.name}`,
          start: startDate,
          end: endDate,
          resource: {
            ...booking,
            serviceName: serviceData.name,
            serviceColor: serviceData.color,
            customerName: booking.customers?.name || 'Unknown',
            customerEmail: booking.customers?.email || '',
            customerPhone: booking.customers?.phone || ''
          }
        };
      });

      setBookings(formattedBookings);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching booking data:', error);
      setError(error.message);
      setLoading(false);
    }
  }, []);

  // Fetch data on initial load and when refreshKey changes
  useEffect(() => {
    fetchData();
  }, [fetchData, refreshKey]);

  // Custom event styling to use service colors and status
  const eventStyleGetter = (event) => {
    const { resource } = event;
    const { status, serviceColor } = resource;

    // Base color from service
    let backgroundColor = serviceColor || '#6a0dad';
    let borderColor = backgroundColor;
    let textColor = getTextColor(backgroundColor);

    // Modify based on status
    if (status && STATUS_COLORS[status]) {
      // For confirmed status, use the service color
      if (status === 'confirmed') {
        backgroundColor = adjustColor(backgroundColor, 10); // Slightly brighter
        borderColor = STATUS_COLORS[status]; // Green border
      }
      // For pending status, use a muted version of the service color
      else if (status === 'pending') {
        backgroundColor = adjustColor(backgroundColor, -15); // Slightly darker
        borderColor = STATUS_COLORS[status]; // Orange border
      }
      // For canceled status, use gray
      else if (status === 'canceled') {
        backgroundColor = '#aaaaaa'; // Gray
        borderColor = STATUS_COLORS[status]; // Red border
      }
      // For other statuses, use appropriate styling
      else {
        borderColor = STATUS_COLORS[status];
      }
    }

    return {
      style: {
        backgroundColor,
        borderLeft: `4px solid ${borderColor}`,
        color: textColor,
        borderRadius: '4px',
        opacity: status === 'canceled' ? 0.7 : 1,
        border: '0px',
        borderLeft: `4px solid ${borderColor}`,
        display: 'block'
      }
    };
  };

  // Handle event selection
  const handleSelectEvent = (event) => {
    if (onSelectBooking) {
      onSelectBooking(event.resource);
    }
  };

  // Handle event drag and drop
  const handleEventDrop = async ({ event, start, end }) => {
    try {
      setLoading(true);
      setResizeError(null);

      // Update the booking in the database
      const updatedBooking = await moveBooking(event.id, start, end);

      // Update the local state
      const updatedBookings = bookings.map(booking => {
        if (booking.id === event.id) {
          return {
            ...booking,
            start,
            end,
            resource: {
              ...booking.resource,
              start_time: start.toISOString(),
              end_time: end.toISOString()
            }
          };
        }
        return booking;
      });

      setBookings(updatedBookings);
    } catch (error) {
      console.error('Error moving booking:', error);
      setResizeError(error.message);

      // Refresh data to revert the change
      fetchData();
    } finally {
      setLoading(false);
    }
  };

  // Handle event resize
  const handleEventResize = async ({ event, start, end }) => {
    try {
      setLoading(true);
      setResizeError(null);

      // Update the booking in the database
      const updatedBooking = await moveBooking(event.id, start, end);

      // Update the local state
      const updatedBookings = bookings.map(booking => {
        if (booking.id === event.id) {
          return {
            ...booking,
            start,
            end,
            resource: {
              ...booking.resource,
              start_time: start.toISOString(),
              end_time: end.toISOString()
            }
          };
        }
        return booking;
      });

      setBookings(updatedBookings);
    } catch (error) {
      console.error('Error resizing booking:', error);
      setResizeError(error.message);

      // Refresh data to revert the change
      fetchData();
    } finally {
      setLoading(false);
    }
  };

  // Handle navigation
  const handleNavigate = (newDate) => {
    setDate(newDate);
  };

  // Handle view change
  const handleViewChange = (newView) => {
    setView(newView);
  };

  // Handle slot selection for creating new bookings
  const handleSelectSlot = (slotInfo) => {
    if (onSelectSlot) {
      onSelectSlot({
        start: slotInfo.start,
        end: slotInfo.end
      });
    }
  };

  // Refresh data manually with debounce to prevent multiple calls
  const refreshData = () => {
    // Prevent refreshing if already loading
    if (loading) return;

    // Show loading indicator
    setLoading(true);

    // Add a small delay to prevent rapid multiple refreshes
    setTimeout(() => {
      fetchData();
    }, 100);
  };

  // Define custom components for the calendar
  const components = useMemo(() => ({
    eventWrapper: BookingEventWrapper,
  }), []);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className={styles.calendarContainer}>
        {loading ? (
          <div className={styles.loading}>Loading booking calendar...</div>
        ) : error ? (
          <div className={styles.error}>{error}</div>
        ) : (
          <>
            <div className={styles.calendarActions}>
              <button
                className={styles.refreshButton}
                onClick={refreshData}
                title="Refresh calendar"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M23 4v6h-6"></path>
                  <path d="M1 20v-6h6"></path>
                  <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                  <path d="M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                </svg>
                Refresh
              </button>
            </div>

            {resizeError && (
              <div className={styles.error}>
                <strong>Error:</strong> {resizeError}
                <button
                  className={styles.closeButton}
                  onClick={() => setResizeError(null)}
                  aria-label="Close error message"
                >
                  &times;
                </button>
              </div>
            )}

            <Calendar
              localizer={localizer}
              events={bookings}
              startAccessor="start"
              endAccessor="end"
              style={{ height: 700 }}
              eventPropGetter={eventStyleGetter}
              onSelectEvent={handleSelectEvent}
              onSelectSlot={handleSelectSlot}
              onEventDrop={handleEventDrop}
              onEventResize={handleEventResize}
              view={view}
              onView={handleViewChange}
              date={date}
              onNavigate={handleNavigate}
              popup
              selectable
              resizable
              draggableAccessor={() => true}
              className={styles.calendar}
              views={['month', 'week', 'day', 'agenda']}
              messages={{
                agenda: 'List',
                day: 'Day',
                month: 'Month',
                week: 'Week',
                today: 'Today',
                previous: 'Back',
                next: 'Next'
              }}
              step={30}
              timeslots={2}
              defaultView="week"
              components={components}
            />
          </>
        )}
      </div>
    </DndProvider>
  );
}

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import ProductList from '@/components/admin/ProductList';
import ServiceList from '@/components/admin/ServiceList';
import Modal from '@/components/admin/Modal';
import InventoryDashboard from '@/components/admin/inventory/InventoryDashboard';
import ProductForm from '@/components/admin/inventory/ProductForm';
import StockAdjustmentForm from '@/components/admin/inventory/StockAdjustmentForm';
import StockMovementLog from '@/components/admin/inventory/StockMovementLog';
import { authenticatedFetch } from '@/lib/auth-utils';
import styles from '@/styles/admin/InventoryPage.module.css';

export default function InventoryPage() {
  const [activeTab, setActiveTab] = useState('dashboard'); // 'dashboard', 'products', 'services', 'movements'
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(null); // 'add-product', 'edit-product', 'add-service', 'edit-service', 'adjust-stock'
  const [selectedItem, setSelectedItem] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [stats, setStats] = useState({
    totalProducts: 0,
    activeProducts: 0,
    totalServices: 0,
    activeServices: 0,
  });
  const [loading, setLoading] = useState(true);

  // Fetch inventory statistics
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);

        // Use authenticatedFetch for consistent authentication handling
        const data = await authenticatedFetch('/api/admin/inventory/stats', {}, {
          redirect: false, // Don't redirect on auth failure, just show error
          notify: true     // Show notifications for errors
        });

        setStats(data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching inventory stats:', error);
        setLoading(false);

        // Set default values on error to prevent UI issues
        setStats({
          totalProducts: 0,
          activeProducts: 0,
          totalServices: 0,
          activeServices: 0,
        });
      }
    };

    fetchStats();
  }, [refreshKey]);

  // Handle item selection for editing
  const handleSelectItem = (item, type) => {
    setSelectedItem(item);
    setModalType(type === 'product' ? 'edit-product' : 'edit-service');
    setShowModal(true);
  };

  // Handle adding new inventory item
  const handleAddItem = (type) => {
    setSelectedItem(null);
    setModalType(type === 'product' ? 'add-product' : 'add-service');
    setShowModal(true);
  };

  // Handle saving inventory item (create or update)
  const handleSaveItem = async () => {
    // Close modal
    setShowModal(false);
    // Refresh data
    setRefreshKey(prevKey => prevKey + 1);
  };

  // Handle modal close
  const handleCloseModal = () => {
    setShowModal(false);
  };

  // Render modal content based on modalType
  const renderModalContent = () => {
    switch (modalType) {
      case 'add-product':
        return (
          <div className={styles.modalContent}>
            <ProductForm
              onSave={handleSaveItem}
              onCancel={handleCloseModal}
            />
          </div>
        );
      case 'edit-product':
        return (
          <div className={styles.modalContent}>
            <ProductForm
              product={selectedItem}
              onSave={handleSaveItem}
              onCancel={handleCloseModal}
            />
          </div>
        );
      case 'adjust-stock':
        return (
          <div className={styles.modalContent}>
            <StockAdjustmentForm
              product={selectedItem}
              onSave={handleSaveItem}
              onCancel={handleCloseModal}
            />
          </div>
        );
      case 'add-service':
        return (
          <div className={styles.modalContent}>
            <h2>Add New Service</h2>
            {/* Form will be added in ServiceForm component */}
          </div>
        );
      case 'edit-service':
        return (
          <div className={styles.modalContent}>
            <h2>Edit Service</h2>
            {/* Form will be added in ServiceForm component */}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <ProtectedRoute>
      <AdminLayout title="Inventory">
        <div className={styles.inventoryPage}>
          <div className={styles.header}>
            <h1>Inventory Management</h1>
            <div className={styles.actionButtons}>
              {activeTab === 'products' && (
                <button
                  className={styles.addButton}
                  onClick={() => handleAddItem('product')}
                >
                  Add Product
                </button>
              )}
              {activeTab === 'services' && (
                <button
                  className={styles.addButton}
                  onClick={() => handleAddItem('service')}
                >
                  Add Service
                </button>
              )}
            </div>
          </div>

          <div className={styles.statsContainer}>
            <div className={styles.statsCard}>
              <h3>Total Products</h3>
              <p className={styles.statValue}>
                {loading ? '...' : stats.totalProducts}
              </p>
            </div>
            <div className={styles.statsCard}>
              <h3>Active Products</h3>
              <p className={styles.statValue}>
                {loading ? '...' : stats.activeProducts}
              </p>
            </div>
            <div className={styles.statsCard}>
              <h3>Total Services</h3>
              <p className={styles.statValue}>
                {loading ? '...' : stats.totalServices}
              </p>
            </div>
            <div className={styles.statsCard}>
              <h3>Active Services</h3>
              <p className={styles.statValue}>
                {loading ? '...' : stats.activeServices}
              </p>
            </div>
          </div>

          <div className={styles.tabsContainer}>
            <div className={styles.tabs}>
              <button
                className={`${styles.tabButton} ${
                  activeTab === 'dashboard' ? styles.activeTab : ''
                }`}
                onClick={() => setActiveTab('dashboard')}
              >
                Dashboard
              </button>
              <button
                className={`${styles.tabButton} ${
                  activeTab === 'products' ? styles.activeTab : ''
                }`}
                onClick={() => setActiveTab('products')}
              >
                Products
              </button>
              <button
                className={`${styles.tabButton} ${
                  activeTab === 'services' ? styles.activeTab : ''
                }`}
                onClick={() => setActiveTab('services')}
              >
                Services
              </button>
              <button
                className={`${styles.tabButton} ${
                  activeTab === 'movements' ? styles.activeTab : ''
                }`}
                onClick={() => setActiveTab('movements')}
              >
                Stock Movements
              </button>
            </div>

            <div className={styles.tabContent}>
              {activeTab === 'dashboard' && (
                <InventoryDashboard />
              )}
              {activeTab === 'products' && (
                <ProductList
                  refreshKey={refreshKey}
                  onSelectProduct={(product) => handleSelectItem(product, 'product')}
                  onAdjustStock={(product) => {
                    setSelectedItem(product);
                    setModalType('adjust-stock');
                    setShowModal(true);
                  }}
                />
              )}
              {activeTab === 'services' && (
                <ServiceList
                  refreshKey={refreshKey}
                  onSelectService={(service) => handleSelectItem(service, 'service')}
                />
              )}
              {activeTab === 'movements' && (
                <StockMovementLog />
              )}
            </div>
          </div>

          {showModal && (
            <Modal onClose={handleCloseModal}>
              {renderModalContent()}
            </Modal>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}

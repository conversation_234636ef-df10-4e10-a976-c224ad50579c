import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import styles from '@/styles/admin/CustomerTagManager.module.css';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Component for managing customer tags
 * 
 * @param {Object} props - Component props
 * @param {string} props.customerId - Customer ID
 * @returns {JSX.Element}
 */
export default function CustomerTagManager({ customerId }) {
  const [availableTags, setAvailableTags] = useState([]);
  const [customerTags, setCustomerTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  
  // Fetch available tags and customer's assigned tags
  useEffect(() => {
    const fetchTags = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Get all available tags
        const { data: allTags, error: tagsError } = await supabase
          .from('customer_tags')
          .select('*')
          .order('name');
          
        if (tagsError) throw tagsError;
        
        // Get customer's assigned tags
        const { data: assignments, error: assignmentsError } = await supabase
          .from('customer_tag_assignments')
          .select('tag_id')
          .eq('customer_id', customerId);
          
        if (assignmentsError) throw assignmentsError;
        
        const assignedTagIds = assignments.map(a => a.tag_id);
        
        setAvailableTags(allTags);
        setCustomerTags(allTags.filter(tag => assignedTagIds.includes(tag.id)));
      } catch (error) {
        console.error('Error fetching tags:', error);
        setError('Failed to load tags');
      } finally {
        setLoading(false);
      }
    };
    
    if (customerId) {
      fetchTags();
    }
  }, [customerId]);
  
  // Add tag to customer
  const addTag = async (tagId) => {
    try {
      setLoading(true);
      setError(null);
      setSuccessMessage(null);
      
      // Check if tag is already assigned
      if (customerTags.some(tag => tag.id === tagId)) {
        setError('Tag is already assigned to this customer');
        return;
      }
      
      // Add tag assignment
      const { error } = await supabase
        .from('customer_tag_assignments')
        .insert([{ customer_id: customerId, tag_id: tagId }]);
        
      if (error) throw error;
      
      // Update local state
      const newTag = availableTags.find(tag => tag.id === tagId);
      setCustomerTags([...customerTags, newTag]);
      setSuccessMessage('Tag added successfully');
    } catch (error) {
      console.error('Error adding tag:', error);
      setError('Failed to add tag');
    } finally {
      setLoading(false);
    }
  };
  
  // Remove tag from customer
  const removeTag = async (tagId) => {
    try {
      setLoading(true);
      setError(null);
      setSuccessMessage(null);
      
      // Remove tag assignment
      const { error } = await supabase
        .from('customer_tag_assignments')
        .delete()
        .eq('customer_id', customerId)
        .eq('tag_id', tagId);
        
      if (error) throw error;
      
      // Update local state
      setCustomerTags(customerTags.filter(tag => tag.id !== tagId));
      setSuccessMessage('Tag removed successfully');
    } catch (error) {
      console.error('Error removing tag:', error);
      setError('Failed to remove tag');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className={styles.tagManager}>
      <h3 className={styles.title}>Customer Tags</h3>
      
      {error && (
        <div className={styles.error}>{error}</div>
      )}
      
      {successMessage && (
        <div className={styles.success}>{successMessage}</div>
      )}
      
      {loading && !customerTags.length ? (
        <div className={styles.loading}>Loading tags...</div>
      ) : (
        <>
          <div className={styles.currentTags}>
            {customerTags.length === 0 ? (
              <div className={styles.emptyState}>No tags assigned</div>
            ) : (
              <div className={styles.tagList}>
                {customerTags.map(tag => (
                  <div 
                    key={tag.id} 
                    className={styles.tag}
                    style={{ backgroundColor: tag.color }}
                  >
                    <span className={styles.tagName}>{tag.name}</span>
                    <button 
                      className={styles.removeButton}
                      onClick={() => removeTag(tag.id)}
                      disabled={loading}
                      aria-label={`Remove ${tag.name} tag`}
                    >
                      &times;
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          <div className={styles.addTagSection}>
            <h4 className={styles.subtitle}>Add Tag</h4>
            <div className={styles.availableTags}>
              {availableTags
                .filter(tag => !customerTags.some(ct => ct.id === tag.id))
                .map(tag => (
                  <button
                    key={tag.id}
                    className={styles.addTagButton}
                    style={{ 
                      backgroundColor: 'transparent',
                      color: tag.color,
                      borderColor: tag.color
                    }}
                    onClick={() => addTag(tag.id)}
                    disabled={loading}
                  >
                    <span className={styles.plusIcon}>+</span>
                    {tag.name}
                  </button>
                ))
              }
            </div>
          </div>
        </>
      )}
    </div>
  );
}

.customerList {
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header h2 {
  font-size: 1.8rem;
  color: #333;
  margin: 0;
}

.actions {
  display: flex;
  gap: 12px;
}

.addButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.addButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.exportButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.exportButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
}

.exportDropdown {
  position: relative;
}

.exportOptions {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  z-index: 10;
  min-width: 200px;
  display: none;
}

.exportDropdown:hover .exportOptions {
  display: block;
}

.exportOptions button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 8px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  color: #333;
}

.exportOptions button:hover {
  background-color: #f5f5f5;
}

.filters {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.searchContainer {
  position: relative;
}

.searchInput {
  width: 100%;
  padding: 10px 16px 10px 40px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.searchInput:focus {
  border-color: #6e8efb;
  outline: none;
}

.searchIcon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.filterControls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
  min-width: 150px;
}

.filterSelect:focus {
  border-color: #6e8efb;
  outline: none;
}

.tableContainer {
  overflow-x: auto;
  margin-bottom: 24px;
}

.customerTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.95rem;
}

.customerTable th {
  background-color: #f9f9f9;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #555;
  border-bottom: 2px solid #eaeaea;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.customerTable th:hover {
  background-color: #f0f0f0;
}

.customerTable td {
  padding: 12px 16px;
  border-bottom: 1px solid #eaeaea;
  color: #333;
}

.customerTable tr:hover {
  background-color: rgba(110, 142, 251, 0.05);
}

.sortIndicator {
  display: inline-block;
  margin-left: 4px;
  color: #6e8efb;
}

.actions {
  display: flex;
  gap: 8px;
}

.viewButton {
  display: inline-block;
  padding: 6px 12px;
  background-color: #6e8efb;
  color: white;
  border-radius: 4px;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.viewButton:hover {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.editButton {
  display: inline-block;
  padding: 6px 12px;
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
  border-radius: 4px;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.editButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
  transform: translateY(-1px);
}

.marketingBadge {
  display: inline-block;
  margin-left: 8px;
  font-size: 0.8rem;
  color: #6e8efb;
}

.newBadge {
  display: inline-block;
  margin-left: 8px;
  background-color: #4caf50;
  color: white;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
}

.recentBooking {
  background-color: rgba(255, 235, 59, 0.1);
}

.noResults {
  text-align: center;
  padding: 32px;
  color: #666;
  font-size: 1.1rem;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
}

.paginationButton {
  padding: 8px 16px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.paginationButton:hover:not(:disabled) {
  background-color: #e5e5e5;
}

.paginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageInfo {
  color: #666;
  font-size: 0.9rem;
}

.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #d32f2f;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #d32f2f;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.1rem;
  color: #666;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .filterControls {
    flex-direction: column;
  }

  .customerTable th,
  .customerTable td {
    padding: 8px;
  }
}

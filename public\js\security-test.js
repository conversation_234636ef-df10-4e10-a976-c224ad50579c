/**
 * OceanSoulSparkles Security Testing Script
 * 
 * This script helps identify security issues on the website.
 * It can be run in the browser console to check for common security problems.
 */

// Configuration
const config = {
  // Security headers to check
  securityHeaders: [
    { name: 'Content-Security-Policy', required: false },
    { name: 'X-Content-Type-Options', required: true },
    { name: 'X-Frame-Options', required: true },
    { name: 'X-XSS-Protection', required: false },
    { name: 'Strict-Transport-Security', required: false },
    { name: 'Referrer-Policy', required: true }
  ],
  
  // Form selectors to test
  formSelectors: 'form',
  
  // Input types to check for validation
  inputTypes: {
    email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    tel: /^[0-9+\-() ]{6,20}$/,
    number: /^[0-9]+$/,
    url: /^https?:\/\/.+\..+/
  },
  
  // Sensitive information patterns
  sensitivePatterns: [
    /password/i,
    /credit.?card/i,
    /card.?number/i,
    /cvv/i,
    /cvc/i,
    /ssn/i,
    /social.?security/i,
    /secret/i,
    /api.?key/i
  ]
};

// Results storage
const securityResults = {
  headers: { pass: [], fail: [] },
  forms: { pass: [], fail: [] },
  inputs: { pass: [], fail: [] },
  cookies: { pass: [], fail: [] },
  localStorage: { pass: [], fail: [] },
  scripts: { pass: [], fail: [] },
  links: { pass: [], fail: [] }
};

/**
 * Check security headers
 */
function checkSecurityHeaders() {
  console.log('🔍 Checking security headers...');
  
  // We can't directly access response headers in the browser
  // This is just a placeholder - in a real test, you would use a server-side check
  // or a tool like securityheaders.com
  
  securityResults.headers.fail.push({
    header: 'Content-Security-Policy',
    issue: 'Cannot check headers directly in browser. Use a tool like https://securityheaders.com'
  });
  
  console.log('⚠️ Security headers check requires external tools.');
}

/**
 * Check form security
 */
function checkFormSecurity() {
  console.log('🔍 Checking form security...');
  
  const forms = document.querySelectorAll(config.formSelectors);
  
  forms.forEach(form => {
    // Check for HTTPS action
    const action = form.getAttribute('action');
    if (action && action.startsWith('http:')) {
      securityResults.forms.fail.push({
        form: form,
        issue: 'Form submits to non-HTTPS URL',
        details: action
      });
    } else {
      securityResults.forms.pass.push({
        form: form,
        details: 'Form action is secure'
      });
    }
    
    // Check for CSRF protection
    const csrfToken = form.querySelector('input[name="csrf_token"], input[name="_token"], input[name="authenticity_token"]');
    if (!csrfToken) {
      securityResults.forms.fail.push({
        form: form,
        issue: 'No CSRF token found in form',
        details: 'Missing CSRF protection'
      });
    } else {
      securityResults.forms.pass.push({
        form: form,
        details: 'CSRF protection found'
      });
    }
    
    // Check input validation attributes
    const inputs = form.querySelectorAll('input:not([type="hidden"]):not([type="submit"]):not([type="button"])');
    inputs.forEach(input => {
      const type = input.getAttribute('type') || 'text';
      const name = input.getAttribute('name');
      const hasValidation = input.hasAttribute('pattern') || 
                           input.hasAttribute('required') || 
                           input.hasAttribute('minlength');
      
      // Check for sensitive information
      let isSensitive = false;
      for (const pattern of config.sensitivePatterns) {
        if (pattern.test(name)) {
          isSensitive = true;
          break;
        }
      }
      
      if (isSensitive && type !== 'password' && !input.hasAttribute('autocomplete')) {
        securityResults.inputs.fail.push({
          input: input,
          issue: 'Sensitive input without proper protection',
          details: `Input name "${name}" appears to contain sensitive information but is not properly protected`
        });
      }
      
      if (!hasValidation && config.inputTypes[type]) {
        securityResults.inputs.fail.push({
          input: input,
          issue: 'Input missing validation',
          details: `Input type "${type}" should have validation attributes`
        });
      } else {
        securityResults.inputs.pass.push({
          input: input,
          details: `Input "${name}" has proper validation`
        });
      }
    });
  });
  
  console.log(`✓ Form security check complete. Issues found: ${securityResults.forms.fail.length}`);
}

/**
 * Check cookie security
 */
function checkCookieSecurity() {
  console.log('🔍 Checking cookie security...');
  
  const cookies = document.cookie.split(';');
  
  if (cookies.length === 1 && cookies[0] === '') {
    console.log('✓ No cookies found');
    return;
  }
  
  cookies.forEach(cookie => {
    const [name, value] = cookie.trim().split('=');
    
    // Check for sensitive information in cookie names
    let isSensitive = false;
    for (const pattern of config.sensitivePatterns) {
      if (pattern.test(name)) {
        isSensitive = true;
        break;
      }
    }
    
    if (isSensitive) {
      securityResults.cookies.fail.push({
        name: name,
        issue: 'Cookie name contains sensitive information',
        details: `Cookie "${name}" appears to contain sensitive information`
      });
    } else {
      securityResults.cookies.pass.push({
        name: name,
        details: 'Cookie name does not contain sensitive information'
      });
    }
    
    // We can't check for Secure and HttpOnly flags in the browser
    // This would require server-side testing
  });
  
  console.log(`✓ Cookie security check complete. Issues found: ${securityResults.cookies.fail.length}`);
}

/**
 * Check localStorage security
 */
function checkLocalStorageSecurity() {
  console.log('🔍 Checking localStorage security...');
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    const value = localStorage.getItem(key);
    
    // Check for sensitive information in localStorage
    let isSensitive = false;
    for (const pattern of config.sensitivePatterns) {
      if (pattern.test(key) || pattern.test(value)) {
        isSensitive = true;
        break;
      }
    }
    
    if (isSensitive) {
      securityResults.localStorage.fail.push({
        key: key,
        issue: 'localStorage contains sensitive information',
        details: `Key "${key}" appears to contain sensitive information`
      });
    } else {
      securityResults.localStorage.pass.push({
        key: key,
        details: 'localStorage item does not contain sensitive information'
      });
    }
  }
  
  console.log(`✓ localStorage security check complete. Issues found: ${securityResults.localStorage.fail.length}`);
}

/**
 * Check external script security
 */
function checkScriptSecurity() {
  console.log('🔍 Checking script security...');
  
  const scripts = document.querySelectorAll('script[src]');
  
  scripts.forEach(script => {
    const src = script.getAttribute('src');
    
    // Check for HTTP scripts
    if (src.startsWith('http:')) {
      securityResults.scripts.fail.push({
        script: script,
        issue: 'Script loaded over HTTP',
        details: src
      });
    } else {
      securityResults.scripts.pass.push({
        script: script,
        details: 'Script loaded securely'
      });
    }
    
    // Check for integrity attribute on external scripts
    if (src.includes('//') && !script.hasAttribute('integrity')) {
      securityResults.scripts.fail.push({
        script: script,
        issue: 'External script missing integrity attribute',
        details: src
      });
    } else if (src.includes('//') && script.hasAttribute('integrity')) {
      securityResults.scripts.pass.push({
        script: script,
        details: 'External script has integrity attribute'
      });
    }
  });
  
  console.log(`✓ Script security check complete. Issues found: ${securityResults.scripts.fail.length}`);
}

/**
 * Check link security
 */
function checkLinkSecurity() {
  console.log('🔍 Checking link security...');
  
  const links = document.querySelectorAll('a[href]');
  
  links.forEach(link => {
    const href = link.getAttribute('href');
    
    // Check for target="_blank" without rel="noopener"
    if (link.getAttribute('target') === '_blank' && 
        (!link.hasAttribute('rel') || 
         !link.getAttribute('rel').includes('noopener'))) {
      securityResults.links.fail.push({
        link: link,
        issue: 'External link with target="_blank" missing rel="noopener"',
        details: href
      });
    } else if (link.getAttribute('target') === '_blank' && 
               link.hasAttribute('rel') && 
               link.getAttribute('rel').includes('noopener')) {
      securityResults.links.pass.push({
        link: link,
        details: 'External link has proper rel attribute'
      });
    }
  });
  
  console.log(`✓ Link security check complete. Issues found: ${securityResults.links.fail.length}`);
}

/**
 * Run all security checks
 */
function runSecurityTests() {
  console.log('🚀 Starting security tests...');
  
  checkSecurityHeaders();
  checkFormSecurity();
  checkCookieSecurity();
  checkLocalStorageSecurity();
  checkScriptSecurity();
  checkLinkSecurity();
  
  console.log('🏁 All security tests completed!');
  console.log('📊 Test results:', securityResults);
  
  // Format results as markdown for easy copying
  const markdown = formatResultsAsMarkdown();
  console.log('📋 Markdown results:\n', markdown);
  
  return securityResults;
}

/**
 * Format results as markdown
 */
function formatResultsAsMarkdown() {
  let markdown = '## Security Test Results\n\n';
  
  // Form security results
  markdown += '### Form Security\n\n';
  if (securityResults.forms.fail.length === 0) {
    markdown += 'No form security issues found.\n\n';
  } else {
    markdown += '| Issue | Details |\n';
    markdown += '|-------|--------|\n';
    
    securityResults.forms.fail.forEach(issue => {
      markdown += `| ${issue.issue} | ${issue.details} |\n`;
    });
    markdown += '\n';
  }
  
  // Input validation results
  markdown += '### Input Validation\n\n';
  if (securityResults.inputs.fail.length === 0) {
    markdown += 'No input validation issues found.\n\n';
  } else {
    markdown += '| Issue | Details |\n';
    markdown += '|-------|--------|\n';
    
    securityResults.inputs.fail.forEach(issue => {
      markdown += `| ${issue.issue} | ${issue.details} |\n`;
    });
    markdown += '\n';
  }
  
  // Cookie security results
  markdown += '### Cookie Security\n\n';
  if (securityResults.cookies.fail.length === 0) {
    markdown += 'No cookie security issues found.\n\n';
  } else {
    markdown += '| Issue | Details |\n';
    markdown += '|-------|--------|\n';
    
    securityResults.cookies.fail.forEach(issue => {
      markdown += `| ${issue.issue} | ${issue.details} |\n`;
    });
    markdown += '\n';
  }
  
  // localStorage security results
  markdown += '### localStorage Security\n\n';
  if (securityResults.localStorage.fail.length === 0) {
    markdown += 'No localStorage security issues found.\n\n';
  } else {
    markdown += '| Issue | Details |\n';
    markdown += '|-------|--------|\n';
    
    securityResults.localStorage.fail.forEach(issue => {
      markdown += `| ${issue.issue} | ${issue.details} |\n`;
    });
    markdown += '\n';
  }
  
  // Script security results
  markdown += '### Script Security\n\n';
  if (securityResults.scripts.fail.length === 0) {
    markdown += 'No script security issues found.\n\n';
  } else {
    markdown += '| Issue | Details |\n';
    markdown += '|-------|--------|\n';
    
    securityResults.scripts.fail.forEach(issue => {
      markdown += `| ${issue.issue} | ${issue.details} |\n`;
    });
    markdown += '\n';
  }
  
  // Link security results
  markdown += '### Link Security\n\n';
  if (securityResults.links.fail.length === 0) {
    markdown += 'No link security issues found.\n\n';
  } else {
    markdown += '| Issue | Details |\n';
    markdown += '|-------|--------|\n';
    
    securityResults.links.fail.forEach(issue => {
      markdown += `| ${issue.issue} | ${issue.details} |\n`;
    });
    markdown += '\n';
  }
  
  return markdown;
}

// Export functions for use in browser console
window.securityTests = {
  runAll: runSecurityTests,
  checkSecurityHeaders,
  checkFormSecurity,
  checkCookieSecurity,
  checkLocalStorageSecurity,
  checkScriptSecurity,
  checkLinkSecurity,
  getResults: () => securityResults
};

console.log('💻 Security test script loaded. Run tests with: securityTests.runAll()');

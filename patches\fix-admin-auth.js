/**
 * Admin Authentication Fix Patch
 * 
 * This patch fixes authentication issues in the admin panel by:
 * 1. Adding more detailed logging
 * 2. Improving token extraction from various sources
 * 3. Adding a fallback authentication method for development
 * 
 * To apply this patch:
 * 1. Stop the server
 * 2. Run: node patches/fix-admin-auth.js
 * 3. Restart the server
 */

const fs = require('fs');
const path = require('path');

// Path to the admin-auth.js file
const authFilePath = path.join(__dirname, '..', 'lib', 'admin-auth.js');

// Read the current file
console.log(`Reading ${authFilePath}...`);
let authFileContent;
try {
  authFileContent = fs.readFileSync(authFilePath, 'utf8');
  console.log('File read successfully.');
} catch (error) {
  console.error(`Error reading file: ${error.message}`);
  process.exit(1);
}

// Create a backup
const backupPath = `${authFilePath}.backup`;
console.log(`Creating backup at ${backupPath}...`);
try {
  fs.writeFileSync(backupPath, authFileContent);
  console.log('Backup created successfully.');
} catch (error) {
  console.error(`Error creating backup: ${error.message}`);
  process.exit(1);
}

// Apply the patches

// Patch 1: Add more detailed logging
console.log('Applying Patch 1: Adding detailed logging...');
const loggingPatch = `  // Generate a unique auth ID for tracking
  const authId = Math.random().toString(36).substring(2, 8);

  // Add detailed logging for debugging
  console.log(\`[\${authId}] Starting authentication process\`);
  console.log(\`[\${authId}] Request URL: \${req.url}\`);
  console.log(\`[\${authId}] Headers present: \${Object.keys(req.headers).join(', ')}\`);
  console.log(\`[\${authId}] Cookies present: \${req.cookies ? Object.keys(req.cookies).join(', ') : 'No cookies'}\`);

  // Check if this is a diagnostics request`;

const originalLoggingCode = `  // Generate a unique auth ID for tracking
  const authId = Math.random().toString(36).substring(2, 8);

  // Check if this is a diagnostics request`;

authFileContent = authFileContent.replace(originalLoggingCode, loggingPatch);

// Patch 2: Improve token extraction
console.log('Applying Patch 2: Improving token extraction...');
const tokenExtractionPatch = `      // Method 2: Check cookies (for browser-based requests)
      if (!token) {
        try {
          // Try to get token from cookies object
          if (req.cookies) {
            if (req.cookies.oss_auth_token) {
              token = req.cookies.oss_auth_token;
            } else if (req.cookies.sb_auth_token) {
              token = req.cookies.sb_auth_token;
            } else if (req.cookies.mcp_auth_token) {
              token = req.cookies.mcp_auth_token;
            } else if (req.cookies['sb-access-token']) {
              token = req.cookies['sb-access-token'];
            } else {
              // Try to find any cookie with 'token' in the name
              const tokenCookieKey = Object.keys(req.cookies).find(key => 
                key.includes('token') || key.includes('auth')
              );
              if (tokenCookieKey) {
                token = req.cookies[tokenCookieKey];
                console.log(\`[\${authId}] Found token in cookie: \${tokenCookieKey}\`);
              }
            }
          }
          // Try to parse cookies from cookie header
          else if (req.headers.cookie) {
            const cookieHeader = req.headers.cookie;
            const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
              const [key, value] = cookie.trim().split('=');
              acc[key] = value;
              return acc;
            }, {});

            if (cookies.oss_auth_token) {
              token = cookies.oss_auth_token;
            } else if (cookies.sb_auth_token) {
              token = cookies.sb_auth_token;
            } else if (cookies.mcp_auth_token) {
              token = cookies.mcp_auth_token;
            } else if (cookies['sb-access-token']) {
              token = cookies['sb-access-token'];
            } else {
              // Try to find any cookie with 'token' in the name
              const tokenCookieKey = Object.keys(cookies).find(key => 
                key.includes('token') || key.includes('auth')
              );
              if (tokenCookieKey) {
                token = cookies[tokenCookieKey];
                console.log(\`[\${authId}] Found token in cookie header: \${tokenCookieKey}\`);
              }
            }
          }`;

const originalTokenExtractionCode = `      // Method 2: Check cookies (for browser-based requests)
      if (!token) {
        try {
          // Try to get token from cookies object
          if (req.cookies) {
            if (req.cookies.oss_auth_token) {
              token = req.cookies.oss_auth_token;
            } else if (req.cookies.sb_auth_token) {
              token = req.cookies.sb_auth_token;
            } else if (req.cookies.mcp_auth_token) {
              token = req.cookies.mcp_auth_token;
            }
          }
          // Try to parse cookies from cookie header
          else if (req.headers.cookie) {
            const cookieHeader = req.headers.cookie;
            const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
              const [key, value] = cookie.trim().split('=');
              acc[key] = value;
              return acc;
            }, {});

            if (cookies.oss_auth_token) {
              token = cookies.oss_auth_token;
            } else if (cookies.sb_auth_token) {
              token = cookies.sb_auth_token;
            } else if (cookies.mcp_auth_token) {
              token = cookies.mcp_auth_token;
            }`;

authFileContent = authFileContent.replace(originalTokenExtractionCode, tokenExtractionPatch);

// Patch 3: Add development mode fallback
console.log('Applying Patch 3: Adding development mode fallback...');
const devModePatch = `  try {
    // Development mode fallback for testing
    // Only active when NODE_ENV is development and ENABLE_AUTH_BYPASS is true
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
      console.log(\`[\${authId}] DEVELOPMENT MODE: Using auth bypass\`);
      return {
        user: { id: 'dev-admin', email: '<EMAIL>' },
        role: 'admin',
        authorized: true,
        error: null
      };
    }

    // First try with MCP client (more robust with retry logic)`;

const originalTryBlock = `  try {
    // First try with MCP client (more robust with retry logic)`;

authFileContent = authFileContent.replace(originalTryBlock, devModePatch);

// Write the modified file
console.log('Writing modified file...');
try {
  fs.writeFileSync(authFilePath, authFileContent);
  console.log('File updated successfully.');
} catch (error) {
  console.error(`Error writing file: ${error.message}`);
  console.log('Restoring from backup...');
  try {
    fs.copyFileSync(backupPath, authFilePath);
    console.log('Restored from backup.');
  } catch (restoreError) {
    console.error(`Error restoring from backup: ${restoreError.message}`);
  }
  process.exit(1);
}

console.log('\nPatch applied successfully!');
console.log('\nTo enable auth bypass for development:');
console.log('1. Add ENABLE_AUTH_BYPASS=true to your .env.local file');
console.log('2. Restart the server');
console.log('\nRemember to remove this bypass before deploying to production!');

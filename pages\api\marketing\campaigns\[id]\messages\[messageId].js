import { getAdminClient, getCurrentUserFromRequest, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUserFromRequest(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  const { id, messageId } = req.query

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getMessage(id, messageId, res)
    case 'PUT':
      return updateMessage(id, messageId, req, res)
    case 'DELETE':
      return deleteMessage(id, messageId, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get a single message
async function getMessage(campaignId, messageId, res) {
  try {
    // Get message
    const client = getClient();
    if (!client) {
      return res.status(500).json({ error: 'Database connection failed' });
    }

    const { data: message, error } = await client
      .from('campaign_messages')
      .select('*')
      .eq('id', messageId)
      .eq('campaign_id', campaignId)
      .single()

    if (error) {
      throw error
    }

    if (!message) {
      return res.status(404).json({ error: 'Message not found' })
    }

    return res.status(200).json(message)
  } catch (error) {
    console.error('Error fetching campaign message:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Update a message
async function updateMessage(campaignId, messageId, req, res) {
  const {
    subject,
    content,
    message_type,
    scheduled_date,
    status
  } = req.body

  try {
    const client = getClient();
    if (!client) {
      return res.status(500).json({ error: 'Database connection failed' });
    }

    // Get message to check current status
    const { data: message, error: messageError } = await client
      .from('campaign_messages')
      .select('status')
      .eq('id', messageId)
      .eq('campaign_id', campaignId)
      .single()

    if (messageError) {
      throw messageError
    }

    if (!message) {
      return res.status(404).json({ error: 'Message not found' })
    }

    // Cannot edit sent messages
    if (message.status === 'sent') {
      return res.status(400).json({ error: 'Cannot edit a sent message' })
    }

    // Validate required fields
    if (!subject || !content || !message_type) {
      return res.status(400).json({ error: 'Subject, content, and message type are required' })
    }

    // Determine status
    let newStatus = status
    if (!newStatus) {
      newStatus = scheduled_date ? 'scheduled' : 'draft'
    }

    // Update message
    const { data, error } = await client
      .from('campaign_messages')
      .update({
        subject,
        content,
        message_type,
        scheduled_date,
        status: newStatus,
        updated_at: new Date()
      })
      .eq('id', messageId)
      .eq('campaign_id', campaignId)
      .select()

    if (error) {
      throw error
    }

    if (!data || data.length === 0) {
      return res.status(404).json({ error: 'Message not found' })
    }

    return res.status(200).json(data[0])
  } catch (error) {
    console.error('Error updating campaign message:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Delete a message
async function deleteMessage(campaignId, messageId, res) {
  try {
    const client = getClient();
    if (!client) {
      return res.status(500).json({ error: 'Database connection failed' });
    }

    // Get message to check current status
    const { data: message, error: messageError } = await client
      .from('campaign_messages')
      .select('status')
      .eq('id', messageId)
      .eq('campaign_id', campaignId)
      .single()

    if (messageError) {
      throw messageError
    }

    if (!message) {
      return res.status(404).json({ error: 'Message not found' })
    }

    // Cannot delete sent messages
    if (message.status === 'sent') {
      return res.status(400).json({ error: 'Cannot delete a sent message' })
    }

    // Delete message
    const { error } = await client
      .from('campaign_messages')
      .delete()
      .eq('id', messageId)
      .eq('campaign_id', campaignId)

    if (error) {
      throw error
    }

    return res.status(200).json({ success: true })
  } catch (error) {
    console.error('Error deleting campaign message:', error)
    return res.status(500).json({ error: error.message })
  }
}

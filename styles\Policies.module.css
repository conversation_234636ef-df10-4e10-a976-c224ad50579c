.main {
  min-height: 100vh;
  background-color: rgba(255, 255, 255, 0.8);
  background-image: url('/UV-Generic-Psychadelia.jpg');
  background-size: cover;
  background-attachment: fixed;
  background-blend-mode: overlay;
}

.heroSection {
  position: relative;
  height: 40vh;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  overflow: hidden;
}

.heroOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.7), rgba(255, 107, 107, 0.7));
  z-index: 1;
}

.heroContent {
  position: relative;
  z-index: 2;
  max-width: 800px;
  padding: 0 2rem;
}

.heroTitle {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.heroSubtitle {
  font-size: 1.5rem;
  max-width: 600px;
  margin: 0 auto;
  opacity: 0.9;
}

.policySection {
  padding: 4rem 2rem;
}

.policyContainer {
  max-width: 900px;
  margin: 0 auto;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 3rem;
}

.policyNav {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 1rem;
}

.policyNavLink {
  margin: 0 1.5rem;
  padding: 0.5rem 1rem;
  font-weight: 600;
  color: #333;
  text-decoration: none;
  position: relative;
  transition: all 0.3s ease;
}

.policyNavLink:hover {
  color: #4ECDC4;
}

.policyNavLink::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #4ECDC4, #FF6B6B);
  transition: width 0.3s ease;
}

.policyNavLink:hover::after {
  width: 100%;
}

.policyContent {
  margin-bottom: 4rem;
  scroll-margin-top: 100px;
}

.policyTitle {
  font-size: 2.2rem;
  margin-bottom: 1.5rem;
  color: #333;
  text-align: center;
}

.policyContent h3 {
  font-size: 1.5rem;
  margin: 2rem 0 1rem;
  color: #4ECDC4;
}

.policyContent p {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: #555;
}

.policyContent ul {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

.policyContent li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
  color: #555;
}

.emailLink {
  color: #4ECDC4;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.emailLink:hover {
  color: #FF6B6B;
  text-decoration: underline;
}

.policyNote {
  font-style: italic;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
  font-size: 0.9rem;
  color: #777;
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroSubtitle {
    font-size: 1.2rem;
  }
  
  .policyContainer {
    padding: 2rem;
  }
  
  .policyNav {
    flex-direction: column;
    align-items: center;
  }
  
  .policyNavLink {
    margin: 0.5rem 0;
  }
  
  .policyTitle {
    font-size: 1.8rem;
  }
}

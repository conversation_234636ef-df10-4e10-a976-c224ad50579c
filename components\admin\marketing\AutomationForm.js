import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import TriggerBuilder from '@/components/admin/marketing/TriggerBuilder'
import styles from '@/styles/admin/marketing/AutomationForm.module.css'

export default function AutomationForm({ initialAutomation = null, onSubmit, onCancel }) {
  const router = useRouter()
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [triggerType, setTriggerType] = useState('event')
  const [triggerConfig, setTriggerConfig] = useState({})
  const [templateId, setTemplateId] = useState('')
  const [messageType, setMessageType] = useState('email')
  const [subject, setSubject] = useState('')
  const [content, setContent] = useState('')
  const [segmentId, setSegmentId] = useState('')
  const [isActive, setIsActive] = useState(false)
  const [templates, setTemplates] = useState([])
  const [segments, setSegments] = useState([])
  const [loading, setLoading] = useState(false)
  const [fetchLoading, setFetchLoading] = useState(true)
  const [error, setError] = useState(null)
  const [showPersonalizationMenu, setShowPersonalizationMenu] = useState(false)

  // Initialize form with initial automation data
  useEffect(() => {
    if (initialAutomation) {
      setName(initialAutomation.name || '')
      setDescription(initialAutomation.description || '')
      setTriggerType(initialAutomation.trigger_type || 'event')
      setTriggerConfig(initialAutomation.trigger_config || {})
      setTemplateId(initialAutomation.template_id || '')
      setMessageType(initialAutomation.message_type || 'email')
      setSubject(initialAutomation.subject || '')
      setContent(initialAutomation.content || '')
      setSegmentId(initialAutomation.segment_id || '')
      setIsActive(initialAutomation.is_active !== undefined ? initialAutomation.is_active : false)
    } else if (router.query.segment) {
      setSegmentId(router.query.segment)
    }
  }, [initialAutomation, router.query.segment])

  // Fetch templates and segments
  useEffect(() => {
    const fetchData = async () => {
      setFetchLoading(true)
      setError(null)

      try {
        // Fetch templates
        const templatesResponse = await fetch('/api/marketing/templates?is_active=true')
        
        if (!templatesResponse.ok) {
          const errorData = await templatesResponse.json()
          throw new Error(errorData.error || 'Failed to fetch templates')
        }

        const templatesData = await templatesResponse.json()
        setTemplates(templatesData.templates || [])

        // Fetch segments
        const segmentsResponse = await fetch('/api/marketing/segments')
        
        if (!segmentsResponse.ok) {
          const errorData = await segmentsResponse.json()
          throw new Error(errorData.error || 'Failed to fetch segments')
        }

        const segmentsData = await segmentsResponse.json()
        setSegments(segmentsData.segments || [])
      } catch (error) {
        console.error('Error fetching data:', error)
        setError('Failed to load templates or segments. Please try again.')
      } finally {
        setFetchLoading(false)
      }
    }

    fetchData()
  }, [])

  // Handle trigger change
  const handleTriggerChange = (triggerData) => {
    setTriggerType(triggerData.trigger_type)
    setTriggerConfig(triggerData.trigger_config)
  }

  // Handle template change
  const handleTemplateChange = async (e) => {
    const selectedTemplateId = e.target.value
    setTemplateId(selectedTemplateId)
    
    if (selectedTemplateId) {
      try {
        const response = await fetch(`/api/marketing/templates/${selectedTemplateId}`)
        
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch template')
        }

        const data = await response.json()
        const template = data.template
        
        if (template) {
          setMessageType(template.template_type)
          setSubject(template.subject || '')
          setContent(template.content || '')
        }
      } catch (error) {
        console.error('Error fetching template:', error)
      }
    }
  }

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // Validate form
      if (!name) {
        throw new Error('Automation name is required')
      }

      if (!triggerType || !triggerConfig) {
        throw new Error('Trigger configuration is required')
      }

      if (!messageType) {
        throw new Error('Message type is required')
      }

      if (messageType === 'email' && !subject) {
        throw new Error('Subject is required for email messages')
      }

      if (!content) {
        throw new Error('Message content is required')
      }

      if (!segmentId) {
        throw new Error('Target segment is required')
      }

      // Call onSubmit callback
      await onSubmit({
        name,
        description,
        trigger_type: triggerType,
        trigger_config: triggerConfig,
        template_id: templateId || null,
        message_type: messageType,
        subject,
        content,
        segment_id: segmentId,
        is_active: isActive
      })
    } catch (error) {
      console.error('Error saving automation:', error)
      setError(error.message)
      setLoading(false)
    }
  }

  // Insert personalization token at cursor position
  const insertPersonalizationToken = (token) => {
    const textarea = document.getElementById('automation-content')
    if (!textarea) return

    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd
    const textBefore = content.substring(0, startPos)
    const textAfter = content.substring(endPos, content.length)
    
    setContent(textBefore + token + textAfter)
    setShowPersonalizationMenu(false)
    
    // Focus back on textarea and set cursor position after the inserted token
    setTimeout(() => {
      textarea.focus()
      textarea.selectionStart = startPos + token.length
      textarea.selectionEnd = startPos + token.length
    }, 0)
  }

  return (
    <div className={styles.automationForm}>
      {error && (
        <div className={styles.error}>
          Error: {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className={styles.formSection}>
          <h3>Automation Details</h3>
          
          <div className={styles.formGroup}>
            <label htmlFor="name">Automation Name *</label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="e.g., Welcome Email for New Customers"
              className={styles.input}
              disabled={loading}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe the purpose of this automation"
              className={styles.textarea}
              disabled={loading}
              rows={2}
            />
          </div>

          <div className={styles.formGroup}>
            <div className={styles.checkboxGroup}>
              <input
                type="checkbox"
                id="is-active"
                checked={isActive}
                onChange={(e) => setIsActive(e.target.checked)}
                disabled={loading}
                className={styles.checkbox}
              />
              <label htmlFor="is-active">Active</label>
            </div>
            <div className={styles.helpText}>
              Inactive automations will not be triggered automatically
            </div>
          </div>
        </div>

        <div className={styles.formSection}>
          <h3>Trigger Configuration</h3>
          <TriggerBuilder
            initialTriggerType={triggerType}
            initialTriggerConfig={triggerConfig}
            onChange={handleTriggerChange}
          />
        </div>

        <div className={styles.formSection}>
          <h3>Target Audience</h3>
          
          <div className={styles.formGroup}>
            <label htmlFor="segment-id">Target Segment *</label>
            {fetchLoading ? (
              <div className={styles.loading}>Loading segments...</div>
            ) : segments.length === 0 ? (
              <div className={styles.noSegments}>
                <p>No segments found. Create a segment first.</p>
                <button
                  type="button"
                  onClick={() => router.push('/admin/marketing/segments/new')}
                  className={styles.createSegmentButton}
                >
                  Create Segment
                </button>
              </div>
            ) : (
              <select
                id="segment-id"
                value={segmentId}
                onChange={(e) => setSegmentId(e.target.value)}
                className={styles.select}
                disabled={loading}
                required
              >
                <option value="">Select a segment</option>
                {segments.map((segment) => (
                  <option key={segment.id} value={segment.id}>
                    {segment.name} ({segment.customer_count || 0} customers)
                  </option>
                ))}
              </select>
            )}
          </div>
        </div>

        <div className={styles.formSection}>
          <h3>Message Content</h3>
          
          <div className={styles.formGroup}>
            <label htmlFor="template-id">Use Template (optional)</label>
            {fetchLoading ? (
              <div className={styles.loading}>Loading templates...</div>
            ) : (
              <select
                id="template-id"
                value={templateId}
                onChange={handleTemplateChange}
                className={styles.select}
                disabled={loading}
              >
                <option value="">Select a template or create from scratch</option>
                {templates
                  .filter(template => !messageType || template.template_type === messageType)
                  .map((template) => (
                    <option key={template.id} value={template.id}>
                      {template.name} ({template.template_type})
                    </option>
                  ))}
              </select>
            )}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="message-type">Message Type *</label>
            <select
              id="message-type"
              value={messageType}
              onChange={(e) => setMessageType(e.target.value)}
              className={styles.select}
              disabled={loading || templateId}
            >
              <option value="email">Email</option>
              <option value="sms">SMS</option>
              <option value="push">Push Notification</option>
            </select>
          </div>

          {messageType === 'email' && (
            <div className={styles.formGroup}>
              <label htmlFor="subject">Subject *</label>
              <input
                type="text"
                id="subject"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Enter email subject"
                className={styles.input}
                disabled={loading}
                required={messageType === 'email'}
              />
            </div>
          )}

          <div className={styles.formGroup}>
            <div className={styles.contentHeader}>
              <label htmlFor="automation-content">Content *</label>
              <button
                type="button"
                onClick={() => setShowPersonalizationMenu(!showPersonalizationMenu)}
                disabled={loading}
                className={styles.personalizationButton}
              >
                Add Personalization
              </button>
            </div>
            {showPersonalizationMenu && (
              <div className={styles.personalizationMenu}>
                <div className={styles.personalizationHeader}>
                  <h4>Insert Personalization Token</h4>
                  <button
                    type="button"
                    onClick={() => setShowPersonalizationMenu(false)}
                    className={styles.closeButton}
                  >
                    ×
                  </button>
                </div>
                <div className={styles.personalizationTokens}>
                  <button type="button" onClick={() => insertPersonalizationToken('{name}')}>
                    Full Name
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{first_name}')}>
                    First Name
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{email}')}>
                    Email
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{phone}')}>
                    Phone
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{city}')}>
                    City
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{state}')}>
                    State
                  </button>
                </div>
              </div>
            )}
            <textarea
              id="automation-content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder={`Enter message content${messageType === 'email' ? ' (HTML supported)' : ''}`}
              className={styles.textarea}
              disabled={loading}
              rows={10}
              required
            />
            <div className={styles.helpText}>
              {messageType === 'email' 
                ? 'HTML is supported. Use personalization tokens to make your emails more personal.' 
                : messageType === 'sms'
                ? 'Keep SMS messages concise. Standard SMS messages are limited to 160 characters.'
                : 'Keep push notifications concise for better readability.'}
            </div>
          </div>
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={onCancel}
            disabled={loading}
            className={styles.cancelButton}
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading || fetchLoading || segments.length === 0}
            className={styles.submitButton}
          >
            {loading ? 'Saving...' : initialAutomation ? 'Update Automation' : 'Create Automation'}
          </button>
        </div>
      </form>
    </div>
  )
}

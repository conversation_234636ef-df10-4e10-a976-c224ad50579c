import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import AdminLayout from '@/components/admin/AdminLayout'
import CampaignForm from '@/components/admin/marketing/CampaignForm'
import styles from '@/styles/admin/marketing/CampaignCreate.module.css'

export default function EditCampaign() {
  const router = useRouter()
  const { id } = router.query
  const [campaign, setCampaign] = useState(null)
  const [loading, setLoading] = useState(false)
  const [fetchLoading, setFetchLoading] = useState(true)
  const [error, setError] = useState(null)
  const [successMessage, setSuccessMessage] = useState(null)

  // Fetch campaign data
  useEffect(() => {
    if (!id) return

    const fetchCampaign = async () => {
      setFetchLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/marketing/campaigns/${id}`)
        
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch campaign')
        }

        const data = await response.json()
        setCampaign(data.campaign || null)
      } catch (error) {
        console.error('Error fetching campaign:', error)
        setError(error.message)
      } finally {
        setFetchLoading(false)
      }
    }

    fetchCampaign()
  }, [id])

  // Handle form submission
  const handleSubmit = async (campaignData) => {
    setLoading(true)
    setError(null)
    setSuccessMessage(null)

    try {
      // Update campaign
      const response = await fetch(`/api/marketing/campaigns/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(campaignData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update campaign')
      }

      const data = await response.json()
      setSuccessMessage('Campaign updated successfully')

      // Redirect to campaign detail page after a short delay
      setTimeout(() => {
        router.push(`/admin/marketing/campaigns/${id}`)
      }, 1500)
    } catch (error) {
      console.error('Error updating campaign:', error)
      setError(error.message)
      setLoading(false)
    }
  }

  if (fetchLoading) {
    return (
      <AdminLayout>
        <div className={styles.campaignCreate}>
          <div className={styles.loading}>Loading campaign data...</div>
        </div>
      </AdminLayout>
    )
  }

  if (!campaign && !fetchLoading) {
    return (
      <AdminLayout>
        <div className={styles.campaignCreate}>
          <div className={styles.error}>
            Campaign not found or you don't have permission to edit it.
          </div>
          <button
            className={styles.backButton}
            onClick={() => router.push('/admin/marketing/campaigns')}
          >
            Back to Campaigns
          </button>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className={styles.campaignCreate}>
        <div className={styles.header}>
          <h2>Edit Marketing Campaign</h2>
          <button
            className={styles.cancelButton}
            onClick={() => router.push(`/admin/marketing/campaigns/${id}`)}
            disabled={loading}
          >
            Cancel
          </button>
        </div>

        {error && (
          <div className={styles.error}>
            Error: {error}
          </div>
        )}

        {successMessage && (
          <div className={styles.success}>
            {successMessage}
          </div>
        )}

        <CampaignForm
          initialCampaign={campaign}
          onSubmit={handleSubmit}
          onCancel={() => router.push(`/admin/marketing/campaigns/${id}`)}
        />
      </div>
    </AdminLayout>
  )
}

import React from 'react';

const OrganizationSchema = () => {
  const organizationData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "OceanSoulSparkles",
    "url": "https://www.oceansoulsparkles.com.au",
    "logo": "https://www.oceansoulsparkles.com.au/images/bannerlogo.PNG",
    "sameAs": [
      "https://www.instagram.com/oceansoulsparkles",
      "https://www.facebook.com/OceanSoulSparkles/"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+61-XXX-XXX-XXX",
      "contactType": "customer service",
      "email": "<EMAIL>"
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationData) }}
    />
  );
};

export default OrganizationSchema;

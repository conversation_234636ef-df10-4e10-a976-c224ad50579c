/**
 * API endpoint for testing authentication
 * This endpoint helps diagnose authentication issues by providing detailed information
 * about the authentication token and headers.
 *
 * Uses the new authentication middleware for consistent authentication.
 */

import { withAdminAuth } from '@/lib/admin-auth';

// Handler function that will be wrapped with authentication
async function authCheckHandler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Auth check request received`);

  try {
    // Log headers for debugging
    console.log(`[${requestId}] Request headers:`, Object.keys(req.headers));

    // Check for authentication headers
    const authHeaders = {
      authorization: req.headers.authorization ? true : false,
      xAuthToken: req.headers['x-auth-token'] ? true : false,
      cookie: req.headers.cookie ? true : false
    };

    console.log(`[${requestId}] Auth headers present:`, authHeaders);

    // Extract token from headers for diagnostics
    let token = null;
    if (req.headers.authorization) {
      if (req.headers.authorization.startsWith('Bearer ')) {
        token = req.headers.authorization.substring(7);
      } else {
        token = req.headers.authorization;
      }
      console.log(`[${requestId}] Token found in Authorization header`);
    } else if (req.headers['x-auth-token']) {
      token = req.headers['x-auth-token'];
      console.log(`[${requestId}] Token found in x-auth-token header`);
    }

    // Authentication successful - user and role are attached to req by the middleware
    console.log(`[${requestId}] Authentication successful for user: ${req.user.email}`);

    return res.status(200).json({
      status: 'success',
      message: 'Authentication successful',
      user: {
        id: req.user.id,
        email: req.user.email,
        role: req.role
      },
      authorized: true,
      authHeaders,
      tokenFound: !!token,
      tokenPreview: token ? `${token.substring(0, 10)}...` : null,
      requestId
    });
  } catch (error) {
    console.error(`[${requestId}] Auth check error:`, error);

    return res.status(500).json({
      status: 'error',
      message: 'An error occurred while checking authentication',
      error: error.message,
      requestId
    });
  }
}

// Export the handler wrapped with authentication middleware
export default withAdminAuth(authCheckHandler);

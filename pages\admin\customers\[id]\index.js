import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import CustomerDetails from '@/components/admin/CustomerDetails'
import { useAuth } from '@/contexts/AuthContext'
import supabase from '@/lib/supabase'
import styles from '@/styles/admin/CustomersPage.module.css'

export default function CustomerDetailsPage() {
  const router = useRouter()
  const { id } = router.query
  const { user, isAuthenticated } = useAuth()
  const [customer, setCustomer] = useState(null)
  const [bookings, setBookings] = useState([])
  const [preferences, setPreferences] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (id && isAuthenticated) {
      fetchCustomerDetails()
    }
  }, [id, isAuthenticated])

  const fetchCustomerDetails = async () => {
    setLoading(true)
    setError(null)

    try {
      // Get the current session token
      const { data: sessionData } = await supabase.auth.getSession();
      const token = sessionData?.session?.access_token;

      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Make the request with authentication headers
      const response = await fetch(`/api/customers/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch customer details');
      }

      const data = await response.json();
      setCustomer(data.customer);
      setBookings(data.bookings);
      setPreferences(data.preferences);
    } catch (error) {
      console.error('Error fetching customer details:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }

  return (
    <ProtectedRoute>
      <AdminLayout title={customer ? `Customer: ${customer.name}` : 'Customer Details'}>
        <div className={styles.customersPage}>
          {error && <div className={styles.error}>{error}</div>}

          {loading ? (
            <div className={styles.loading}>Loading customer details...</div>
          ) : (
            <CustomerDetails
              customer={customer}
              bookings={bookings}
              preferences={preferences}
            />
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}

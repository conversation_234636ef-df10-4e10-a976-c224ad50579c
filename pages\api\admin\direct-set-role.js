import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client with service role key for admin access
console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
console.log('Service Role Key exists:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);

// Create a client with detailed logging
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Get userId and role from request body
    const { userId, role } = req.body

    if (!userId || !role) {
      return res.status(400).json({ error: 'User ID and role are required' })
    }

    // Validate role
    const validRoles = ['admin', 'staff', 'user']
    if (!validRoles.includes(role)) {
      return res.status(400).json({ error: 'Invalid role. Must be admin, staff, or user' })
    }

    console.log(`Direct role set attempt for user ID: ${userId} to role: ${role}`)

    // Check if we can connect to Supabase
    try {
      const { data: healthCheck, error: healthError } = await supabaseAdmin.from('user_roles').select('count').limit(1);
      console.log('Supabase health check:', { data: healthCheck, error: healthError });

      if (healthError) {
        console.error('Supabase connection error:', healthError);
        return res.status(500).json({
          error: 'Failed to connect to Supabase',
          details: healthError.message,
          code: healthError.code
        });
      }
    } catch (healthCheckError) {
      console.error('Unexpected error during health check:', healthCheckError);
      return res.status(500).json({
        error: 'Failed to connect to Supabase',
        details: healthCheckError.message
      });
    }

    // First try to delete any existing role for this user
    try {
      const { error: deleteError } = await supabaseAdmin
        .from('user_roles')
        .delete()
        .eq('id', userId);

      // Log the delete operation result
      if (deleteError) {
        console.log(`Delete operation result: Error - ${deleteError.message}`);
        console.log('Delete error details:', deleteError);
      } else {
        console.log('Delete operation result: Success (or no existing role)');
      }
    } catch (deleteErr) {
      console.error('Unexpected error during delete operation:', deleteErr);
      // Continue anyway, as this might just be because the record doesn't exist
    }

    // Now insert the new role
    try {
      const { data: insertData, error: insertError } = await supabaseAdmin
        .from('user_roles')
        .insert([{ id: userId, role }])
        .select();

      // Log the insert operation result
      if (insertError) {
        console.error('Insert operation error:', insertError);
        console.error('Insert error details:', {
          code: insertError.code,
          message: insertError.message,
          details: insertError.details,
          hint: insertError.hint
        });

        return res.status(500).json({
          error: 'Failed to set user role',
          details: insertError.message,
          code: insertError.code,
          hint: 'This might be due to RLS policies or constraints on the user_roles table'
        });
      }

      console.log('Insert operation result:', insertData);

      return res.status(200).json({
        success: true,
        message: `Role set to ${role} for user ${userId}`,
        data: insertData
      });
    } catch (insertErr) {
      console.error('Unexpected error during insert operation:', insertErr);
      return res.status(500).json({
        error: 'Unexpected error during insert operation',
        details: insertErr.message
      });
    }
  } catch (error) {
    console.error('Unexpected error:', error)
    return res.status(500).json({
      error: 'An unexpected error occurred',
      details: error.message
    })
  }
}

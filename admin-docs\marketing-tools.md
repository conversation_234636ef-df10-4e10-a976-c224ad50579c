# Marketing Tools

This document provides detailed implementation instructions for the OceanSoulSparkles admin panel marketing tools.

## Overview

The marketing tools module enables administrators to create and manage marketing campaigns, segment customers, track campaign performance, and send targeted communications. It integrates with the customer database and provides analytics on marketing effectiveness.

## Features

- Campaign creation and management
- Customer segmentation
- Email marketing integration
- Campaign performance tracking
- Discount code generation
- Automated marketing workflows
- Social media integration

## Database Schema

Create the following tables in Supabase:

```sql
-- Marketing campaigns table
CREATE TABLE public.marketing_campaigns (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ,
  status TEXT NOT NULL,
  campaign_type TEXT NOT NULL,
  target_segment UUID REFERENCES public.customer_segments(id),
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Customer segments table
CREATE TABLE public.customer_segments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  segment_query JSONB NOT NULL,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Campaign messages table
CREATE TABLE public.campaign_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  campaign_id UUID REFERENCES public.marketing_campaigns(id) ON DELETE CASCADE,
  subject TEXT NOT NULL,
  content TEXT NOT NULL,
  message_type TEXT NOT NULL,
  scheduled_date TIMESTAMPTZ,
  sent_date TIMESTAMPTZ,
  status TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Campaign metrics table
CREATE TABLE public.campaign_metrics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  campaign_id UUID REFERENCES public.marketing_campaigns(id) ON DELETE CASCADE,
  metric_type TEXT NOT NULL,
  metric_value INTEGER NOT NULL,
  recorded_at TIMESTAMPTZ DEFAULT NOW()
);

-- Discount codes table
CREATE TABLE public.discount_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  campaign_id UUID REFERENCES public.marketing_campaigns(id),
  code TEXT NOT NULL UNIQUE,
  discount_type TEXT NOT NULL,
  discount_value DECIMAL(10, 2) NOT NULL,
  min_purchase_amount DECIMAL(10, 2),
  max_uses INTEGER,
  current_uses INTEGER DEFAULT 0,
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.marketing_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.discount_codes ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Staff can view all marketing campaigns" ON public.marketing_campaigns
  FOR SELECT USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can insert marketing campaigns" ON public.marketing_campaigns
  FOR INSERT WITH CHECK (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can update marketing campaigns" ON public.marketing_campaigns
  FOR UPDATE USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

-- Similar policies for other tables
```

## Implementation Steps

### 1. Create Customer Segmentation API Endpoints

Create the following API endpoints:

```javascript
// pages/api/marketing/segments/index.js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin } from '@/lib/auth'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getSegments(req, res)
    case 'POST':
      return createSegment(req, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get customer segments
async function getSegments(req, res) {
  const { data, error } = await supabase
    .from('customer_segments')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  return res.status(200).json(data)
}

// Create a new customer segment
async function createSegment(req, res) {
  const { name, description, segment_query } = req.body
  const { user } = await isStaffOrAdmin()

  // Validate required fields
  if (!name || !segment_query) {
    return res.status(400).json({ error: 'Name and segment query are required' })
  }

  // Create segment
  const { data, error } = await supabase
    .from('customer_segments')
    .insert([
      {
        name,
        description,
        segment_query,
        created_by: user.id
      }
    ])
    .select()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  return res.status(201).json(data[0])
}
```

Create additional API endpoints for individual segment operations:

```javascript
// pages/api/marketing/segments/[id].js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin, isAdmin } from '@/lib/auth'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  const { id } = req.query

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getSegment(id, res)
    case 'PUT':
      return updateSegment(id, req, res)
    case 'DELETE':
      // Only admins can delete segments
      const isAdminUser = await isAdmin()
      if (!isAdminUser) {
        return res.status(403).json({ error: 'Forbidden' })
      }
      return deleteSegment(id, res)
    case 'POST':
      if (req.query.action === 'preview') {
        return previewSegment(id, res)
      }
      return res.status(400).json({ error: 'Invalid action' })
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get a single segment
async function getSegment(id, res) {
  const { data, error } = await supabase
    .from('customer_segments')
    .select('*')
    .eq('id', id)
    .single()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  if (!data) {
    return res.status(404).json({ error: 'Segment not found' })
  }

  return res.status(200).json(data)
}

// Update a segment
async function updateSegment(id, req, res) {
  const { name, description, segment_query } = req.body

  // Validate required fields
  if (!name || !segment_query) {
    return res.status(400).json({ error: 'Name and segment query are required' })
  }

  // Update segment
  const { data, error } = await supabase
    .from('customer_segments')
    .update({
      name,
      description,
      segment_query,
      updated_at: new Date()
    })
    .eq('id', id)
    .select()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  if (!data || data.length === 0) {
    return res.status(404).json({ error: 'Segment not found' })
  }

  return res.status(200).json(data[0])
}

// Delete a segment
async function deleteSegment(id, res) {
  const { data, error } = await supabase
    .from('customer_segments')
    .delete()
    .eq('id', id)
    .select()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  if (!data || data.length === 0) {
    return res.status(404).json({ error: 'Segment not found' })
  }

  return res.status(200).json({ message: 'Segment deleted successfully' })
}

// Preview segment (get customers in segment)
async function previewSegment(id, res) {
  // First get the segment
  const { data: segment, error: segmentError } = await supabase
    .from('customer_segments')
    .select('*')
    .eq('id', id)
    .single()

  if (segmentError) {
    return res.status(500).json({ error: segmentError.message })
  }

  if (!segment) {
    return res.status(404).json({ error: 'Segment not found' })
  }

  // Build query based on segment_query
  let query = supabase.from('customers').select('id, name, email, phone')

  // Apply filters from segment_query
  const filters = segment.segment_query.filters || []

  filters.forEach(filter => {
    const { field, operator, value } = filter

    if (operator === 'eq') {
      query = query.eq(field, value)
    } else if (operator === 'neq') {
      query = query.neq(field, value)
    } else if (operator === 'gt') {
      query = query.gt(field, value)
    } else if (operator === 'gte') {
      query = query.gte(field, value)
    } else if (operator === 'lt') {
      query = query.lt(field, value)
    } else if (operator === 'lte') {
      query = query.lte(field, value)
    } else if (operator === 'like') {
      query = query.ilike(field, `%${value}%`)
    } else if (operator === 'in') {
      query = query.in(field, value)
    }
  })

  // Execute query
  const { data: customers, error, count } = await query.select('*', { count: 'exact' })

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  return res.status(200).json({
    segment,
    customers,
    total: count
  })
}
```

### 2. Create Marketing Campaign API Endpoints

Create API endpoints for campaign management:

```javascript
// pages/api/marketing/campaigns/index.js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin } from '@/lib/auth'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getCampaigns(req, res)
    case 'POST':
      return createCampaign(req, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get marketing campaigns
async function getCampaigns(req, res) {
  const { status, campaign_type, limit, offset } = req.query

  let query = supabase
    .from('marketing_campaigns')
    .select(`
      *,
      target_segment:customer_segments (name)
    `)

  // Apply filters
  if (status) {
    query = query.eq('status', status)
  }
  if (campaign_type) {
    query = query.eq('campaign_type', campaign_type)
  }

  // Apply pagination
  if (limit) {
    query = query.limit(limit)
  }
  if (offset) {
    query = query.offset(offset)
  }

  // Order by created_at
  query = query.order('created_at', { ascending: false })

  const { data, error, count } = await query.select('*', { count: 'exact' })

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  return res.status(200).json({
    campaigns: data,
    total: count
  })
}

// Create a new marketing campaign
async function createCampaign(req, res) {
  const {
    name,
    description,
    start_date,
    end_date,
    status,
    campaign_type,
    target_segment
  } = req.body
  const { user } = await isStaffOrAdmin()

  // Validate required fields
  if (!name || !start_date || !status || !campaign_type) {
    return res.status(400).json({ error: 'Missing required fields' })
  }

  // Create campaign
  const { data, error } = await supabase
    .from('marketing_campaigns')
    .insert([
      {
        name,
        description,
        start_date,
        end_date,
        status,
        campaign_type,
        target_segment,
        created_by: user.id
      }
    ])
    .select()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  return res.status(201).json(data[0])
}
```

### 3. Create Notification Integration with OneSignal

Integrate with OneSignal for email and push notifications:

```javascript
// lib/notification-service.js
import { supabase } from './supabase'

// Initialize OneSignal
const initOneSignal = () => {
  if (typeof window !== 'undefined' && !window.OneSignal) {
    window.OneSignal = window.OneSignal || [];
    window.OneSignal.push(function() {
      window.OneSignal.init({
        appId: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID,
        safari_web_id: process.env.NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID,
        notifyButton: {
          enable: true,
        },
      });
    });
  }
};

// Send campaign notifications to a segment of customers
export async function sendCampaignNotification(campaignId, messageId) {
  try {
    // Get campaign message
    const { data: message, error: messageError } = await supabase
      .from('campaign_messages')
      .select('*')
      .eq('id', messageId)
      .single()

    if (messageError) {
      throw new Error(`Error fetching message: ${messageError.message}`)
    }

    // Get campaign
    const { data: campaign, error: campaignError } = await supabase
      .from('marketing_campaigns')
      .select('*, target_segment:customer_segments (*)')
      .eq('id', campaignId)
      .single()

    if (campaignError) {
      throw new Error(`Error fetching campaign: ${campaignError.message}`)
    }

    // Get customers in segment
    const { data: segmentPreview, error: previewError } = await fetch(
      `/api/marketing/segments/${campaign.target_segment.id}?action=preview`,
      { method: 'POST' }
    ).then(res => res.json())

    if (previewError) {
      throw new Error(`Error previewing segment: ${previewError}`)
    }

    const customers = segmentPreview.customers

    // Prepare customer external IDs for OneSignal
    const externalIds = customers.map(customer => customer.id);

    // Send notification via OneSignal REST API
    const response = await fetch('https://onesignal.com/api/v1/notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${process.env.ONESIGNAL_REST_API_KEY}`
      },
      body: JSON.stringify({
        app_id: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID,
        include_external_user_ids: externalIds,
        headings: { en: message.subject },
        contents: { en: message.content },
        data: {
          campaign_id: campaignId,
          message_id: messageId
        },
        channel_for_external_user_ids: message.message_type === 'email' ? 'email' : 'push',
        email_subject: message.message_type === 'email' ? message.subject : undefined,
        web_url: message.web_url || undefined
      })
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`OneSignal API error: ${JSON.stringify(result)}`);
    }

    // Update message status
    await supabase
      .from('campaign_messages')
      .update({
        status: 'sent',
        sent_date: new Date(),
        updated_at: new Date()
      })
      .eq('id', messageId)

    // Record metrics
    await supabase
      .from('campaign_metrics')
      .insert([
        {
          campaign_id: campaignId,
          metric_type: 'sent',
          metric_value: customers.length
        }
      ])

    return {
      success: true,
      sent: customers.length,
      notification_id: result.id
    }
  } catch (error) {
    console.error('Error sending campaign notifications:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Handle OneSignal webhook events
export async function handleOneSignalWebhook(events) {
  try {
    for (const event of events) {
      const { event: eventType, notification_id, data } = event;
      const { campaign_id } = data || {};

      if (!campaign_id) continue;

      // Record metric based on event type
      if (['opened', 'clicked', 'bounced', 'unsubscribed'].includes(eventType)) {
        const metricType = eventType === 'opened' ? 'open' :
                          eventType === 'clicked' ? 'click' : eventType;

        await supabase
          .from('campaign_metrics')
          .insert([
            {
              campaign_id,
              metric_type: metricType,
              metric_value: 1
            }
          ])
      }
    }

    return { success: true }
  } catch (error) {
    console.error('Error handling OneSignal webhook:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Subscribe a user to notifications
export async function subscribeUserToNotifications(userId, email, phone) {
  try {
    // Set external user ID in OneSignal
    if (typeof window !== 'undefined' && window.OneSignal) {
      await window.OneSignal.setExternalUserId(userId);

      // Set email if provided
      if (email) {
        await window.OneSignal.setEmail(email);
      }

      // Set SMS if provided (requires additional OneSignal setup)
      if (phone) {
        await window.OneSignal.setSMSNumber(phone);
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error subscribing user to notifications:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 4. Create Discount Code Generator

Create functionality for generating discount codes:

```javascript
// lib/discount-codes.js
import { supabase } from './supabase'
import { v4 as uuidv4 } from 'uuid'

// Generate a random discount code
function generateCode(prefix = 'OSS', length = 8) {
  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'
  let result = prefix

  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length))
  }

  return result
}

// Create a new discount code
export async function createDiscountCode(data) {
  const {
    campaign_id,
    discount_type,
    discount_value,
    min_purchase_amount,
    max_uses,
    start_date,
    end_date,
    is_active,
    code_prefix
  } = data

  // Generate a unique code
  let code
  let isUnique = false

  while (!isUnique) {
    code = generateCode(code_prefix)

    // Check if code already exists
    const { data: existingCode, error } = await supabase
      .from('discount_codes')
      .select('id')
      .eq('code', code)
      .single()

    if (error && error.code === 'PGRST116') {
      // Code doesn't exist
      isUnique = true
    }
  }

  // Create the discount code
  const { data: discountCode, error } = await supabase
    .from('discount_codes')
    .insert([
      {
        campaign_id,
        code,
        discount_type,
        discount_value,
        min_purchase_amount,
        max_uses,
        start_date,
        end_date,
        is_active: is_active !== undefined ? is_active : true
      }
    ])
    .select()

  if (error) {
    throw new Error(`Error creating discount code: ${error.message}`)
  }

  return discountCode[0]
}

// Validate a discount code
export async function validateDiscountCode(code) {
  // Get the discount code
  const { data, error } = await supabase
    .from('discount_codes')
    .select('*')
    .eq('code', code)
    .eq('is_active', true)
    .single()

  if (error) {
    return {
      valid: false,
      error: 'Invalid discount code'
    }
  }

  const now = new Date()

  // Check if code is expired
  if (data.end_date && new Date(data.end_date) < now) {
    return {
      valid: false,
      error: 'Discount code has expired'
    }
  }

  // Check if code is not yet active
  if (new Date(data.start_date) > now) {
    return {
      valid: false,
      error: 'Discount code is not yet active'
    }
  }

  // Check if code has reached max uses
  if (data.max_uses && data.current_uses >= data.max_uses) {
    return {
      valid: false,
      error: 'Discount code has reached maximum uses'
    }
  }

  return {
    valid: true,
    discount: data
  }
}

// Apply a discount code to an order
export async function applyDiscountCode(code, orderAmount) {
  const { valid, discount, error } = await validateDiscountCode(code)

  if (!valid) {
    return {
      valid: false,
      error
    }
  }

  // Check minimum purchase amount
  if (discount.min_purchase_amount && orderAmount < discount.min_purchase_amount) {
    return {
      valid: false,
      error: `Minimum purchase amount of $${discount.min_purchase_amount} required`
    }
  }

  // Calculate discount amount
  let discountAmount = 0

  if (discount.discount_type === 'percentage') {
    discountAmount = orderAmount * (discount.discount_value / 100)
  } else if (discount.discount_type === 'fixed') {
    discountAmount = discount.discount_value
  }

  // Ensure discount doesn't exceed order amount
  discountAmount = Math.min(discountAmount, orderAmount)

  return {
    valid: true,
    discount,
    discountAmount,
    finalAmount: orderAmount - discountAmount
  }
}
```

## Testing

1. Test customer segmentation
2. Test campaign creation and management
3. Test email sending functionality
4. Test discount code generation and validation
5. Test campaign performance tracking

## Security Considerations

- Implement proper authentication and authorization
- Validate all input data
- Protect against SQL injection
- Implement rate limiting for API endpoints
- Ensure compliance with email marketing regulations (CAN-SPAM, GDPR, etc.)
- Secure handling of customer data

.inventoryDashboard {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #4a90e2;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.errorMessage {
  color: #d32f2f;
  margin-bottom: 16px;
  text-align: center;
}

.retryButton {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.retryButton:hover {
  background-color: #3a7bc8;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

@media (min-width: 576px) {
  .metricsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 992px) {
  .metricsGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.metricCard {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.metricValue {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.metricLabel {
  font-size: 14px;
  color: #666;
}

.chartsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

@media (min-width: 768px) {
  .chartsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.chartCard {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.chartContainer {
  height: 300px;
  position: relative;
}

.movementsCard,
.alertsCard {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.movementsCard h3,
.alertsCard h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.movementsTable {
  width: 100%;
  overflow-x: auto;
}

.movementsTable table {
  width: 100%;
  border-collapse: collapse;
}

.movementsTable th,
.movementsTable td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.movementsTable th {
  font-weight: 600;
  color: #555;
  font-size: 14px;
}

.movementsTable td {
  font-size: 14px;
  color: #333;
}

.movementType {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.restock {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.sale {
  background-color: #e3f2fd;
  color: #1565c0;
}

.adjustment {
  background-color: #fff8e1;
  color: #f57f17;
}

.return {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.damaged {
  background-color: #ffebee;
  color: #c62828;
}

.lost {
  background-color: #fafafa;
  color: #616161;
}

.positive {
  color: #2e7d32;
  font-weight: 500;
}

.negative {
  color: #c62828;
  font-weight: 500;
}

.viewAllLink {
  margin-top: 16px;
  text-align: right;
}

.viewAllLink a {
  color: #4a90e2;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.viewAllLink a:hover {
  text-decoration: underline;
}

.alertsList {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.alertItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-radius: 4px;
  background-color: #fff8e1;
  border-left: 4px solid #f57f17;
}

.alertProduct {
  display: flex;
  align-items: center;
}

.alertProductName {
  font-weight: 500;
  margin-right: 12px;
}

.stockBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.inStock {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.lowStock {
  background-color: #fff8e1;
  color: #f57f17;
}

.outOfStock {
  background-color: #ffebee;
  color: #c62828;
}

.viewProductButton {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  text-decoration: none;
  transition: background-color 0.2s;
}

.viewProductButton:hover {
  background-color: #3a7bc8;
}

.noData {
  padding: 24px;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #f9f9f9;
  border-radius: 4px;
}

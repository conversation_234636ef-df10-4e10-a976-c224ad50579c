# API Testing Guide for Customer Database Management System

This directory contains tests for the Customer Database Management System API endpoints. These tests verify that the API endpoints work correctly, handle errors appropriately, and enforce proper authentication and authorization.

## Test Structure

The tests are organized by API endpoint:

- `customers/index.test.js`: Tests for `/api/customers` (GET and POST)
- `customers/[id].test.js`: Tests for `/api/customers/[id]` (GET, PUT, DELETE)
- `customers/export.test.js`: Tests for `/api/customers/export` (GET)
- `customers/gdpr-delete.test.js`: Tests for `/api/customers/[id]/gdpr-delete` (POST)

## Running the Tests

To run all API tests:

```bash
npm run test:api
```

To run API tests in watch mode (useful during development):

```bash
npm run test:api:watch
```

To run a specific test file:

```bash
npm test -- __tests__/api/customers/index.test.js
```

## Test Coverage

The tests cover the following aspects of the API:

### Authentication and Authorization

- Verifies that endpoints require authentication
- Verifies that certain operations (DELETE, GDPR delete, export) require admin role
- Tests both admin and staff role access where appropriate

### CRUD Operations

- **Create**: Tests creating new customers with validation
- **Read**: Tests retrieving customer lists and individual customers
- **Update**: Tests updating customer information with validation
- **Delete**: Tests deleting customers (admin only)

### Filtering, Sorting, and Pagination

- Tests search functionality
- Tests filtering by location and booking status
- Tests sorting by different fields
- Tests pagination parameters

### Data Export

- Tests exporting customer data in CSV format
- Tests exporting customer data in JSON format
- Tests filtering export by marketing consent

### GDPR Compliance

- Tests anonymizing customer data
- Tests proper error handling during GDPR operations

## Mocking Strategy

The tests use mocks to isolate the API endpoints from external dependencies:

- **Supabase**: All Supabase database operations are mocked
- **Authentication**: Authentication and authorization checks are mocked
- **HTTP**: Request and response objects are mocked using `node-mocks-http`

## Adding New Tests

When adding new API endpoints or modifying existing ones, follow these guidelines:

1. Create a new test file if needed, following the naming pattern `__tests__/api/[path].test.js`
2. Import the API handler and test utilities
3. Structure tests with describe blocks for different aspects (auth, methods, error handling)
4. Mock authentication status appropriately for each test
5. Mock Supabase responses to test both success and error cases
6. Verify response status codes and JSON data

## Troubleshooting

If tests are failing, check:

1. That the API implementation matches the expected behavior in the tests
2. That mocks are set up correctly for each test case
3. That authentication and authorization checks are working as expected
4. That error handling is properly implemented in the API endpoints

import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import OneSignalTest from '@/components/admin/OneSignalTest';
import styles from '@/styles/admin/Dashboard.module.css';

export default function TestOneSignalPage() {
  return (
    <ProtectedRoute>
      <AdminLayout title="OneSignal Testing">
        <div className={styles.dashboard}>
          <OneSignalTest />
          
          <div className={styles.dashboardSection}>
            <h2 className={styles.sectionTitle}>OneSignal Integration Information</h2>
            <div className={styles.infoCard}>
              <p>
                This page allows you to test the OneSignal notification system implementation.
                The tests verify:
              </p>
              <ul>
                <li>OneSignal initialization status</li>
                <li>Notification permission status</li>
                <li>Push notification functionality</li>
                <li>User tagging for segmentation</li>
              </ul>
              <p>
                Use the controls above to test different aspects of the OneSignal integration.
                You can show notification prompts, set user tags, and check the current status.
              </p>
            </div>
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}

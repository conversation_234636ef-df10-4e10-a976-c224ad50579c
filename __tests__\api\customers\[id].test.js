import handler from '@/pages/api/customers/[id]';
import { 
  createApiMocks, 
  mockAuthenticatedUser, 
  mockUnauthenticatedUser,
  mockUnauthorizedUser,
  mockSupabaseResponse,
  resetMocks
} from '../../utils/api-test-utils';

describe('API: /api/customers/[id]', () => {
  beforeEach(() => {
    resetMocks();
  });

  describe('Authentication', () => {
    it('should return 401 if user is not authenticated', async () => {
      // Mock unauthenticated user
      mockUnauthenticatedUser();

      // Create mock request and response
      const { req, res } = createApiMocks({
        query: { id: '1' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(401);
      expect(res._getJSONData()).toEqual({ error: 'Authentication failed' });
    });

    it('should allow authenticated admin users', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock Supabase response
      mockSupabaseResponse({ data: { id: '1', name: 'Test Customer' } });

      // Create mock request and response
      const { req, res } = createApiMocks({
        query: { id: '1' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(200);
    });

    it('should allow authenticated staff users', async () => {
      // Mock authenticated staff user
      mockAuthenticatedUser({ id: 'staff-user' }, 'staff');

      // Mock Supabase response
      mockSupabaseResponse({ data: { id: '1', name: 'Test Customer' } });

      // Create mock request and response
      const { req, res } = createApiMocks({
        query: { id: '1' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(200);
    });

    it('should require admin role for DELETE operations', async () => {
      // Mock authenticated staff user (non-admin)
      mockAuthenticatedUser({ id: 'staff-user' }, 'staff');

      // Create mock request for DELETE
      const { req, res } = createApiMocks({
        method: 'DELETE',
        query: { id: '1' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(403);
      expect(res._getJSONData()).toEqual({ error: 'Forbidden' });
    });
  });

  describe('GET /api/customers/[id]', () => {
    it('should return a customer with booking history and preferences', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock customer data
      const mockCustomer = {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '0412345678',
        created_at: '2023-01-01T00:00:00Z'
      };

      // Mock bookings data
      const mockBookings = [
        {
          id: 'b1',
          start_time: '2023-02-01T10:00:00Z',
          end_time: '2023-02-01T11:00:00Z',
          status: 'confirmed',
          services: {
            name: 'Face Painting',
            price: '120.00'
          }
        }
      ];

      // Mock preferences data
      const mockPreferences = [
        {
          preference_key: 'Favorite Color',
          preference_value: 'Blue'
        }
      ];

      // Set up Supabase mock to return different data for different queries
      const supabase = require('@/lib/supabase').supabase;
      
      // First call for customer
      supabase.from.mockImplementationOnce(() => ({
        ...supabase,
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockCustomer, error: null })
      }));
      
      // Second call for bookings
      supabase.from.mockImplementationOnce(() => ({
        ...supabase,
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        mockResolvedValue: jest.fn().mockResolvedValue({ data: mockBookings, error: null })
      }));
      
      // Third call for preferences
      supabase.from.mockImplementationOnce(() => ({
        ...supabase,
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        mockResolvedValue: jest.fn().mockResolvedValue({ data: mockPreferences, error: null })
      }));

      // Create mock request and response
      const { req, res } = createApiMocks({
        query: { id: '1' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(200);
      expect(res._getJSONData()).toEqual({
        customer: mockCustomer,
        bookings: mockBookings,
        preferences: mockPreferences
      });
    });

    it('should return 404 if customer is not found', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock Supabase response for non-existent customer
      mockSupabaseResponse({ data: null, error: null });

      // Create mock request and response
      const { req, res } = createApiMocks({
        query: { id: 'non-existent' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(404);
      expect(res._getJSONData()).toEqual({ error: 'Customer not found' });
    });

    it('should handle database errors', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock Supabase error
      mockSupabaseResponse({ error: { message: 'Database error' } });

      // Create mock request and response
      const { req, res } = createApiMocks({
        query: { id: '1' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(500);
      expect(res._getJSONData()).toEqual({ error: 'Database error' });
    });
  });

  describe('PUT /api/customers/[id]', () => {
    it('should update an existing customer', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock updated customer data
      const updatedCustomer = {
        id: '1',
        name: 'Updated Name',
        email: '<EMAIL>',
        phone: '0499887766'
      };

      // Mock Supabase response
      mockSupabaseResponse({ data: [updatedCustomer] });

      // Create mock request with updated data
      const { req, res } = createApiMocks({
        method: 'PUT',
        query: { id: '1' },
        body: updatedCustomer
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(200);
      expect(res._getJSONData()).toEqual(updatedCustomer);
    });

    it('should validate required fields', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Create mock request with missing required fields
      const { req, res } = createApiMocks({
        method: 'PUT',
        query: { id: '1' },
        body: { phone: '0499887766' } // Missing name and email
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(400);
      expect(res._getJSONData()).toEqual({ error: 'Name and email are required' });
    });

    it('should check for duplicate email', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock existing customer with same email
      mockSupabaseResponse({ data: { id: 'different-id' } });

      // Create mock request with duplicate email
      const { req, res } = createApiMocks({
        method: 'PUT',
        query: { id: '1' },
        body: {
          name: 'Updated Name',
          email: '<EMAIL>',
          phone: '0499887766'
        }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(409);
      expect(res._getJSONData()).toEqual({ error: 'Another customer with this email already exists' });
    });

    it('should return 404 if customer to update is not found', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock Supabase response for update
      mockSupabaseResponse({ data: [] });

      // Create mock request with valid data
      const { req, res } = createApiMocks({
        method: 'PUT',
        query: { id: 'non-existent' },
        body: {
          name: 'Updated Name',
          email: '<EMAIL>',
          phone: '0499887766'
        }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(404);
      expect(res._getJSONData()).toEqual({ error: 'Customer not found' });
    });

    it('should handle database errors during update', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock Supabase error
      mockSupabaseResponse({ error: { message: 'Database error during update' } });

      // Create mock request with valid data
      const { req, res } = createApiMocks({
        method: 'PUT',
        query: { id: '1' },
        body: {
          name: 'Updated Name',
          email: '<EMAIL>',
          phone: '0499887766'
        }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(500);
      expect(res._getJSONData()).toEqual({ error: 'Database error during update' });
    });
  });

  describe('DELETE /api/customers/[id]', () => {
    it('should delete a customer (admin only)', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock Supabase response
      mockSupabaseResponse({ data: [{ id: '1', name: 'Deleted Customer' }] });

      // Create mock request for DELETE
      const { req, res } = createApiMocks({
        method: 'DELETE',
        query: { id: '1' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(200);
      expect(res._getJSONData()).toEqual({ message: 'Customer deleted successfully' });
    });

    it('should return 404 if customer to delete is not found', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock Supabase response for non-existent customer
      mockSupabaseResponse({ data: [] });

      // Create mock request for DELETE
      const { req, res } = createApiMocks({
        method: 'DELETE',
        query: { id: 'non-existent' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(404);
      expect(res._getJSONData()).toEqual({ error: 'Customer not found' });
    });

    it('should handle database errors during deletion', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock Supabase error
      mockSupabaseResponse({ error: { message: 'Database error during deletion' } });

      // Create mock request for DELETE
      const { req, res } = createApiMocks({
        method: 'DELETE',
        query: { id: '1' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(500);
      expect(res._getJSONData()).toEqual({ error: 'Database error during deletion' });
    });
  });

  describe('Method Not Allowed', () => {
    it('should return 405 for unsupported methods', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Create mock request with unsupported method
      const { req, res } = createApiMocks({
        method: 'PATCH',
        query: { id: '1' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(405);
      expect(res._getJSONData()).toEqual({ error: 'Method not allowed' });
    });
  });
});

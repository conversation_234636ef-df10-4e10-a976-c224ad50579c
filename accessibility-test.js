/**
 * OceanSoulSparkles Accessibility Testing Script
 * 
 * This script helps identify accessibility issues on the website.
 * It can be run in the browser console to check for common accessibility problems.
 */

// Configuration
const config = {
  // WCAG 2.1 AA contrast ratio requirements
  contrastRatios: {
    normalText: 4.5,
    largeText: 3.0
  },
  
  // Minimum touch target size (in pixels)
  minTouchTargetSize: {
    width: 44,
    height: 44
  },
  
  // Elements that should have alt text
  requiresAltText: ['img', 'area', 'input[type="image"]'],
  
  // Interactive elements that should be keyboard accessible
  interactiveElements: [
    'a', 'button', 'input', 'select', 'textarea', 
    '[role="button"]', '[role="link"]', '[role="checkbox"]', 
    '[role="radio"]', '[role="tab"]', '[role="menuitem"]'
  ],
  
  // Elements that should have labels
  requiresLabel: [
    'input:not([type="hidden"]):not([type="submit"]):not([type="reset"]):not([type="button"])', 
    'select', 
    'textarea'
  ]
};

// Results storage
const accessibilityResults = {
  altText: { pass: [], fail: [] },
  contrast: { pass: [], fail: [] },
  keyboardAccess: { pass: [], fail: [] },
  formLabels: { pass: [], fail: [] },
  touchTargets: { pass: [], fail: [] },
  headings: { issues: [] },
  ariaAttributes: { issues: [] }
};

/**
 * Calculate contrast ratio between two colors
 * @param {string} color1 - First color (any valid CSS color)
 * @param {string} color2 - Second color (any valid CSS color)
 * @returns {number} - Contrast ratio
 */
function calculateContrastRatio(color1, color2) {
  // Convert colors to RGB
  const getRGB = (color) => {
    const tempEl = document.createElement('div');
    tempEl.style.color = color;
    document.body.appendChild(tempEl);
    const computedColor = window.getComputedStyle(tempEl).color;
    document.body.removeChild(tempEl);
    
    const rgbMatch = computedColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (rgbMatch) {
      return [parseInt(rgbMatch[1]), parseInt(rgbMatch[2]), parseInt(rgbMatch[3])];
    }
    
    return [0, 0, 0]; // Default to black if conversion fails
  };
  
  // Calculate relative luminance
  const getLuminance = (rgb) => {
    const [r, g, b] = rgb.map(c => {
      const val = c / 255;
      return val <= 0.03928 ? val / 12.92 : Math.pow((val + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  };
  
  const rgb1 = getRGB(color1);
  const rgb2 = getRGB(color2);
  
  const luminance1 = getLuminance(rgb1);
  const luminance2 = getLuminance(rgb2);
  
  const lighter = Math.max(luminance1, luminance2);
  const darker = Math.min(luminance1, luminance2);
  
  return (lighter + 0.05) / (darker + 0.05);
}

/**
 * Check for missing alt text on images
 */
function checkAltText() {
  console.log('🔍 Checking for missing alt text...');
  
  config.requiresAltText.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    
    elements.forEach(el => {
      const hasAlt = el.hasAttribute('alt');
      const altText = el.getAttribute('alt');
      const isDecorative = altText === '';
      
      if (!hasAlt) {
        accessibilityResults.altText.fail.push({
          element: el,
          issue: 'Missing alt attribute'
        });
      } else if (!isDecorative && altText.length < 5) {
        accessibilityResults.altText.fail.push({
          element: el,
          issue: 'Alt text may be too short or uninformative',
          altText
        });
      } else {
        accessibilityResults.altText.pass.push({
          element: el,
          altText: isDecorative ? '[decorative]' : altText
        });
      }
    });
  });
  
  console.log(`✓ Alt text check complete. Issues found: ${accessibilityResults.altText.fail.length}`);
}

/**
 * Check for contrast issues
 */
function checkContrast() {
  console.log('🔍 Checking for contrast issues...');
  
  const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, a, button, label, input, span, div');
  
  textElements.forEach(el => {
    // Skip elements with no text content
    if (!el.textContent.trim()) return;
    
    // Skip hidden elements
    if (el.offsetParent === null) return;
    
    const style = window.getComputedStyle(el);
    const backgroundColor = style.backgroundColor;
    const textColor = style.color;
    const fontSize = parseInt(style.fontSize);
    const fontWeight = style.fontWeight;
    
    // Determine if text is "large" according to WCAG
    const isLargeText = fontSize >= 18 || (fontSize >= 14 && fontWeight >= 700);
    const requiredRatio = isLargeText ? config.contrastRatios.largeText : config.contrastRatios.normalText;
    
    // Calculate contrast ratio
    const ratio = calculateContrastRatio(textColor, backgroundColor);
    
    if (ratio < requiredRatio) {
      accessibilityResults.contrast.fail.push({
        element: el,
        textColor,
        backgroundColor,
        ratio: ratio.toFixed(2),
        required: requiredRatio,
        fontSize: `${fontSize}px`,
        fontWeight
      });
    } else {
      accessibilityResults.contrast.pass.push({
        element: el,
        ratio: ratio.toFixed(2)
      });
    }
  });
  
  console.log(`✓ Contrast check complete. Issues found: ${accessibilityResults.contrast.fail.length}`);
}

/**
 * Check for keyboard accessibility issues
 */
function checkKeyboardAccess() {
  console.log('🔍 Checking for keyboard accessibility issues...');
  
  config.interactiveElements.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    
    elements.forEach(el => {
      // Skip hidden elements
      if (el.offsetParent === null) return;
      
      const tabIndex = el.getAttribute('tabindex');
      const hasClickHandler = el.onclick || el.getAttribute('onclick');
      const isDisabled = el.disabled || el.getAttribute('aria-disabled') === 'true';
      
      if (tabIndex === '-1' && !isDisabled && hasClickHandler) {
        accessibilityResults.keyboardAccess.fail.push({
          element: el,
          issue: 'Interactive element not keyboard accessible (tabindex="-1")'
        });
      } else if (hasClickHandler && !el.getAttribute('role') && !['a', 'button', 'input', 'select', 'textarea'].includes(el.tagName.toLowerCase())) {
        accessibilityResults.keyboardAccess.fail.push({
          element: el,
          issue: 'Interactive element missing appropriate role attribute'
        });
      } else {
        accessibilityResults.keyboardAccess.pass.push({
          element: el
        });
      }
    });
  });
  
  console.log(`✓ Keyboard accessibility check complete. Issues found: ${accessibilityResults.keyboardAccess.fail.length}`);
}

/**
 * Check for form label issues
 */
function checkFormLabels() {
  console.log('🔍 Checking for form label issues...');
  
  config.requiresLabel.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    
    elements.forEach(el => {
      // Skip hidden elements
      if (el.offsetParent === null) return;
      
      const id = el.id;
      const hasExplicitLabel = id && document.querySelector(`label[for="${id}"]`);
      const hasAriaLabel = el.getAttribute('aria-label');
      const hasAriaLabelledBy = el.getAttribute('aria-labelledby');
      const hasPlaceholder = el.getAttribute('placeholder');
      
      if (!hasExplicitLabel && !hasAriaLabel && !hasAriaLabelledBy) {
        accessibilityResults.formLabels.fail.push({
          element: el,
          issue: 'Form control missing label',
          hasPlaceholder: !!hasPlaceholder
        });
      } else if (!hasExplicitLabel && !hasAriaLabel && !hasAriaLabelledBy && hasPlaceholder) {
        accessibilityResults.formLabels.fail.push({
          element: el,
          issue: 'Form control using placeholder as label',
          placeholder: hasPlaceholder
        });
      } else {
        accessibilityResults.formLabels.pass.push({
          element: el,
          labelType: hasExplicitLabel ? 'explicit' : (hasAriaLabel ? 'aria-label' : 'aria-labelledby')
        });
      }
    });
  });
  
  console.log(`✓ Form label check complete. Issues found: ${accessibilityResults.formLabels.fail.length}`);
}

/**
 * Check for touch target size issues
 */
function checkTouchTargets() {
  console.log('🔍 Checking for touch target size issues...');
  
  const interactiveElements = document.querySelectorAll('a, button, input[type="button"], input[type="submit"], input[type="reset"], [role="button"]');
  
  interactiveElements.forEach(el => {
    // Skip hidden elements
    if (el.offsetParent === null) return;
    
    const rect = el.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;
    
    if (width < config.minTouchTargetSize.width || height < config.minTouchTargetSize.height) {
      accessibilityResults.touchTargets.fail.push({
        element: el,
        width: Math.round(width),
        height: Math.round(height),
        required: `${config.minTouchTargetSize.width}x${config.minTouchTargetSize.height}`
      });
    } else {
      accessibilityResults.touchTargets.pass.push({
        element: el,
        size: `${Math.round(width)}x${Math.round(height)}`
      });
    }
  });
  
  console.log(`✓ Touch target size check complete. Issues found: ${accessibilityResults.touchTargets.fail.length}`);
}

/**
 * Check heading structure
 */
function checkHeadings() {
  console.log('🔍 Checking heading structure...');
  
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  const headingLevels = [];
  
  headings.forEach(heading => {
    const level = parseInt(heading.tagName.substring(1));
    headingLevels.push({ element: heading, level, text: heading.textContent.trim() });
  });
  
  // Check for skipped heading levels
  for (let i = 0; i < headingLevels.length - 1; i++) {
    const current = headingLevels[i];
    const next = headingLevels[i + 1];
    
    if (next.level > current.level && next.level - current.level > 1) {
      accessibilityResults.headings.issues.push({
        element: next.element,
        issue: `Skipped heading level: h${current.level} to h${next.level}`,
        text: next.text
      });
    }
  }
  
  // Check for multiple h1 elements
  const h1Elements = headingLevels.filter(h => h.level === 1);
  if (h1Elements.length > 1) {
    accessibilityResults.headings.issues.push({
      issue: 'Multiple h1 elements found',
      count: h1Elements.length,
      elements: h1Elements.map(h => h.element)
    });
  }
  
  // Check for empty headings
  headingLevels.forEach(heading => {
    if (!heading.text) {
      accessibilityResults.headings.issues.push({
        element: heading.element,
        issue: `Empty heading: h${heading.level}`
      });
    }
  });
  
  console.log(`✓ Heading structure check complete. Issues found: ${accessibilityResults.headings.issues.length}`);
}

/**
 * Run all accessibility checks
 */
function runAccessibilityTests() {
  console.log('🚀 Starting accessibility tests...');
  
  checkAltText();
  checkContrast();
  checkKeyboardAccess();
  checkFormLabels();
  checkTouchTargets();
  checkHeadings();
  
  console.log('🏁 All accessibility tests completed!');
  console.log('📊 Test results:', accessibilityResults);
  
  // Format results as markdown for easy copying
  const markdown = formatResultsAsMarkdown();
  console.log('📋 Markdown results:\n', markdown);
  
  return accessibilityResults;
}

/**
 * Format results as markdown
 */
function formatResultsAsMarkdown() {
  let markdown = '## Accessibility Test Results\n\n';
  
  // Alt text issues
  markdown += '### Alt Text Issues\n\n';
  if (accessibilityResults.altText.fail.length === 0) {
    markdown += 'No alt text issues found.\n\n';
  } else {
    markdown += '| Element | Issue |\n';
    markdown += '|---------|-------|\n';
    
    accessibilityResults.altText.fail.forEach(issue => {
      const elementDesc = `${issue.element.tagName.toLowerCase()}${issue.element.id ? `#${issue.element.id}` : ''}`;
      markdown += `| ${elementDesc} | ${issue.issue} |\n`;
    });
    markdown += '\n';
  }
  
  // Contrast issues
  markdown += '### Contrast Issues\n\n';
  if (accessibilityResults.contrast.fail.length === 0) {
    markdown += 'No contrast issues found.\n\n';
  } else {
    markdown += '| Element | Text Color | Background | Ratio | Required |\n';
    markdown += '|---------|------------|------------|-------|----------|\n';
    
    accessibilityResults.contrast.fail.forEach(issue => {
      const elementDesc = `${issue.element.tagName.toLowerCase()}${issue.element.id ? `#${issue.element.id}` : ''}`;
      markdown += `| ${elementDesc} | ${issue.textColor} | ${issue.backgroundColor} | ${issue.ratio} | ${issue.required} |\n`;
    });
    markdown += '\n';
  }
  
  // Keyboard access issues
  markdown += '### Keyboard Accessibility Issues\n\n';
  if (accessibilityResults.keyboardAccess.fail.length === 0) {
    markdown += 'No keyboard accessibility issues found.\n\n';
  } else {
    markdown += '| Element | Issue |\n';
    markdown += '|---------|-------|\n';
    
    accessibilityResults.keyboardAccess.fail.forEach(issue => {
      const elementDesc = `${issue.element.tagName.toLowerCase()}${issue.element.id ? `#${issue.element.id}` : ''}`;
      markdown += `| ${elementDesc} | ${issue.issue} |\n`;
    });
    markdown += '\n';
  }
  
  // Form label issues
  markdown += '### Form Label Issues\n\n';
  if (accessibilityResults.formLabels.fail.length === 0) {
    markdown += 'No form label issues found.\n\n';
  } else {
    markdown += '| Element | Issue |\n';
    markdown += '|---------|-------|\n';
    
    accessibilityResults.formLabels.fail.forEach(issue => {
      const elementDesc = `${issue.element.tagName.toLowerCase()}${issue.element.id ? `#${issue.element.id}` : ''}`;
      markdown += `| ${elementDesc} | ${issue.issue} |\n`;
    });
    markdown += '\n';
  }
  
  // Touch target issues
  markdown += '### Touch Target Size Issues\n\n';
  if (accessibilityResults.touchTargets.fail.length === 0) {
    markdown += 'No touch target size issues found.\n\n';
  } else {
    markdown += '| Element | Size | Required |\n';
    markdown += '|---------|------|----------|\n';
    
    accessibilityResults.touchTargets.fail.forEach(issue => {
      const elementDesc = `${issue.element.tagName.toLowerCase()}${issue.element.id ? `#${issue.element.id}` : ''}`;
      markdown += `| ${elementDesc} | ${issue.width}x${issue.height} | ${issue.required} |\n`;
    });
    markdown += '\n';
  }
  
  // Heading structure issues
  markdown += '### Heading Structure Issues\n\n';
  if (accessibilityResults.headings.issues.length === 0) {
    markdown += 'No heading structure issues found.\n\n';
  } else {
    markdown += '| Issue | Details |\n';
    markdown += '|-------|--------|\n';
    
    accessibilityResults.headings.issues.forEach(issue => {
      let details = issue.text || '';
      if (issue.count) details = `Count: ${issue.count}`;
      markdown += `| ${issue.issue} | ${details} |\n`;
    });
    markdown += '\n';
  }
  
  return markdown;
}

// Export functions for use in browser console
window.accessibilityTests = {
  runAll: runAccessibilityTests,
  checkAltText,
  checkContrast,
  checkKeyboardAccess,
  checkFormLabels,
  checkTouchTargets,
  checkHeadings,
  getResults: () => accessibilityResults
};

console.log('♿ Accessibility test script loaded. Run tests with: accessibilityTests.runAll()');

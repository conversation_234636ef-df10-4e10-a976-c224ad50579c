# Authentication Architecture

## Overview

The Ocean Soul Sparkles authentication system is built on Supabase and follows a unified approach to authentication across the application. This document provides an overview of the authentication architecture.

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Client         │     │  Next.js        │     │  Supabase       │
│  (Browser)      │◄────┤  Server         │◄────┤  Auth           │
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         │                       │                       │
         │                       │                       │
         │                       │                       │
┌────────▼────────┐     ┌────────▼────────┐     ┌────────▼────────┐
│                 │     │                 │     │                 │
│  AuthProvider   │     │  API Routes     │     │  Database       │
│  (React)        │     │  (Next.js)      │     │  (PostgreSQL)   │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## Key Components

### 1. Supabase Client (`lib/supabase.js`)

The Supabase client is the single source of truth for authentication. It provides:

- A consistent interface to Supabase authentication
- Token storage and management
- Session persistence
- Automatic token refresh

```javascript
// lib/supabase.js
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    storageKey: 'oss_auth_token',
    cookieOptions: {
      path: '/',
      sameSite: 'Lax',
      secure: process.env.NODE_ENV === 'production'
    }
  }
})

export const getAdminClient = () => {
  if (typeof window !== 'undefined') {
    throw new Error('Admin client can only be used server-side')
  }

  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  if (!serviceRoleKey) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is not defined')
  }

  return createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}
```

### 2. Authentication Helpers (`lib/auth.js`)

The authentication helpers provide utility functions for common authentication tasks:

- User sign-in and sign-out
- Password reset
- User role management
- Session management

```javascript
// lib/auth.js
import { supabase } from './supabase'

export const signIn = async (email, password) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) throw error

    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut()
    return { error }
  } catch (error) {
    return { error }
  }
}

// More helper functions...
```

### 3. Server-Side Authentication (`lib/admin-auth.js`)

The server-side authentication middleware provides:

- API route protection
- Token validation
- Role-based access control

```javascript
// lib/admin-auth.js
import { getAdminClient } from './supabase'

export const authenticateAdmin = async (req, res, next) => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Missing or invalid Authorization header'
      })
    }

    const token = authHeader.substring(7)

    // Verify token with admin client
    const adminClient = getAdminClient()
    const { data, error } = await adminClient.auth.getUser(token)

    if (error || !data.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid authentication token'
      })
    }

    // Check user role
    const { data: roleData, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', data.user.id)
      .single()

    if (roleError || !roleData || roleData.role !== 'admin') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Insufficient permissions'
      })
    }

    // Add user and role to request object
    req.user = data.user
    req.role = roleData.role

    // Continue to the next middleware or route handler
    next()
  } catch (error) {
    console.error('Authentication error:', error)
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication process failed'
    })
  }
}

// Higher-order function to create authenticated API handlers
export const withAdminAuth = (handler) => {
  return (req, res) => {
    return authenticateAdmin(req, res, () => handler(req, res))
  }
}
```

### 4. AuthProvider Component (`components/admin/AuthProvider.js`)

The AuthProvider component manages authentication state in the React application:

- User authentication state
- Role information
- Session persistence
- Token refresh
- Route protection

```javascript
// components/admin/AuthProvider.js
import { createContext, useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { supabase } from '@/lib/supabase'

const AuthContext = createContext()

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [role, setRole] = useState(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  // Initialize auth state and subscribe to changes
  useEffect(() => {
    // Implementation details...
  }, [router])

  return (
    <AuthContext.Provider value={{ user, role, loading }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
```

## Database Schema

The authentication system relies on the following database tables:

### User Roles Table

```sql
CREATE TABLE public.user_roles (
  id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL PRIMARY KEY,
  role TEXT NOT NULL CHECK (role IN ('admin', 'staff')) DEFAULT 'staff',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Authentication Flow

For a detailed explanation of the authentication flow, see [Authentication Flow](./flow.md).

# Ocean Soul Sparkles Admin Authentication Test Script

This document provides detailed test procedures for validating the authentication implementation according to the testing matrix in the admin-auth-audit.md document.

## Prerequisites

Before beginning testing, ensure you have:

1. Access to the Ocean Soul Sparkles admin panel with admin credentials
2. Access to a staff user account (if available)
3. Chrome, Firefox, Safari, and Edge browsers installed
4. Mobile and tablet devices or emulators for device testing
5. Postman or similar API testing tool
6. Access to browser developer tools

## 1. API Authentication Tests

### API-01: Valid Admin Token Test

**Procedure:**
1. Login to the admin panel with admin credentials
2. Open browser developer tools and go to the Network tab
3. Navigate to the admin dashboard
4. Find a request to `/api/admin/diagnostics/auth-check` in the Network tab
5. Copy the Authorization header value
6. Use Postman to send a GET request to `/api/admin/diagnostics/auth-check` with the copied token in the Authorization header

**Expected Result:**
- Status code: 200 OK
- Response contains user information including email and role

**Validation Method:**
- Verify the status code is 200
- Verify the response contains the expected user information
- Verify the role is "admin"

### API-02: Expired Token Test

**Procedure:**
1. Create an expired JWT token (you can use a previously valid token and modify its expiration)
2. Use Postman to send a GET request to `/api/admin/diagnostics/auth-check` with the expired token in the Authorization header

**Expected Result:**
- Status code: 401 Unauthorized
- Response contains an error message about token expiration

**Validation Method:**
- Verify the status code is 401
- Verify the response contains an appropriate error message

### API-03: No Token Test

**Procedure:**
1. Use Postman to send a GET request to `/api/admin/diagnostics/auth-check` without any Authorization header

**Expected Result:**
- Status code: 401 Unauthorized
- Response contains an error message about missing token

**Validation Method:**
- Verify the status code is 401
- Verify the response contains an appropriate error message

### API-04: Valid Admin Token for Customer Data

**Procedure:**
1. Login to the admin panel with admin credentials
2. Open browser developer tools and go to the Network tab
3. Navigate to the Customers section
4. Find a request to `/api/admin/customers` in the Network tab
5. Copy the Authorization header value
6. Use Postman to send a GET request to `/api/admin/customers` with the copied token in the Authorization header

**Expected Result:**
- Status code: 200 OK
- Response contains customer list data

**Validation Method:**
- Verify the status code is 200
- Verify the response contains customer data in the expected format

### API-05: Staff Token for Admin-Only Endpoint

**Procedure:**
1. Login to the admin panel with staff credentials (if available)
2. Open browser developer tools and go to the Network tab
3. Copy the Authorization header value from any request
4. Use Postman to send a GET request to `/api/admin/customers` with the copied token in the Authorization header

**Expected Result:**
- Status code: 403 Forbidden
- Response contains an error message about insufficient permissions

**Validation Method:**
- Verify the status code is 403
- Verify the response contains an appropriate error message

### API-06: Valid Admin Token for Settings

**Procedure:**
1. Login to the admin panel with admin credentials
2. Open browser developer tools and go to the Network tab
3. Navigate to the Settings section
4. Find a request to `/api/admin/settings` in the Network tab
5. Copy the Authorization header value
6. Use Postman to send a GET request to `/api/admin/settings` with the copied token in the Authorization header

**Expected Result:**
- Status code: 200 OK
- Response contains settings data

**Validation Method:**
- Verify the status code is 200
- Verify the response contains settings data in the expected format

## 2. Authentication Flow Tests

### AUTH-01: Admin Login

**Procedure:**
1. Navigate to `/admin/login`
2. Enter valid <NAME_EMAIL>
3. Click the Login button

**Expected Result:**
- Successful login
- Redirect to admin dashboard
- User information displayed in the UI

**Validation Method:**
- Verify URL changes to admin dashboard
- Verify user information is displayed correctly
- Check browser storage (localStorage) for auth token

### AUTH-02: Invalid Login

**Procedure:**
1. Navigate to `/admin/login`
2. Enter incorrect credentials
3. Click the Login button

**Expected Result:**
- Error message displayed
- User remains on login page
- No auth token stored

**Validation Method:**
- Verify error message is displayed
- Verify URL remains at login page
- Check browser storage for absence of auth token

### AUTH-03: Session Persistence

**Procedure:**
1. Login to the admin panel with admin credentials
2. Refresh the page

**Expected Result:**
- User remains logged in
- Admin dashboard still accessible
- No login prompt

**Validation Method:**
- Verify user information is still displayed
- Verify admin functionality is still accessible
- Check browser console for any auth errors

### AUTH-04: Token Refresh

**Procedure:**
1. Login to the admin panel with admin credentials
2. Open browser developer tools and go to the Network tab
3. Wait for 5 minutes
4. Monitor network requests

**Expected Result:**
- Token refresh request is made automatically
- User remains logged in
- No interruption to user experience

**Validation Method:**
- Verify refresh token request in Network tab
- Verify no authentication errors in console
- Verify admin functionality remains accessible

### AUTH-05: Logout

**Procedure:**
1. Login to the admin panel with admin credentials
2. Click the Logout button or option

**Expected Result:**
- User is logged out
- Redirect to login page
- Auth token removed from storage

**Validation Method:**
- Verify redirect to login page
- Check browser storage for removal of auth token
- Attempt to access admin page and verify redirect to login

### AUTH-06: Session Timeout

**Procedure:**
1. Login to the admin panel with admin credentials
2. Modify the token expiration time to a short period (for testing purposes)
3. Wait for the token to expire
4. Attempt to navigate to a different admin section

**Expected Result:**
- User is notified about session expiration
- User is redirected to login page
- Auth token is removed from storage

**Validation Method:**
- Verify notification about session expiration
- Verify redirect to login page
- Check browser storage for removal of auth token

## 3. Cross-Browser Testing

For each browser (Chrome, Firefox, Safari, Edge), perform the following tests:

### BROWSER-01 to BROWSER-04

**Procedure:**
1. Complete all Authentication Flow Tests (AUTH-01 through AUTH-06) in each browser
2. Pay special attention to:
   - Cookie handling differences
   - localStorage/sessionStorage behavior
   - JWT token processing
   - Redirect handling

**Expected Result:**
- All tests pass consistently across browsers
- No browser-specific authentication issues

**Validation Method:**
- Visual verification of expected behavior
- Console monitoring for browser-specific errors
- Network request inspection for authentication headers

## 4. Admin Panel Section Tests

### SECTION-01: Customer Management

**Procedure:**
1. Login to the admin panel with admin credentials
2. Navigate to the Customers section
3. Perform the following operations:
   - View customer list
   - View customer details
   - Edit customer information
   - Create a new customer (if applicable)
   - Delete a customer (if applicable)

**Expected Result:**
- Authentication persists across all operations
- All operations complete successfully
- No authentication errors

**Validation Method:**
- Monitor network requests for auth errors
- Check browser console for authentication issues
- Verify operations complete without auth prompts

### SECTION-02: Booking Management

**Procedure:**
1. Login to the admin panel with admin credentials
2. Navigate to the Bookings section
3. Perform the following operations:
   - View booking list
   - View booking details
   - Edit booking information
   - Create a new booking (if applicable)
   - Cancel a booking (if applicable)

**Expected Result:**
- Authentication persists across all operations
- All operations complete successfully
- No authentication errors

**Validation Method:**
- Monitor network requests for auth errors
- Check browser console for authentication issues
- Verify operations complete without auth prompts

### SECTION-03: Marketing

**Procedure:**
1. Login to the admin panel with admin credentials
2. Navigate to the Marketing section
3. Perform the following operations:
   - View campaigns
   - Create/edit campaign
   - View analytics
   - Manage customer segments

**Expected Result:**
- Authentication persists across all operations
- All operations complete successfully
- No authentication errors

**Validation Method:**
- Monitor network requests for auth errors
- Check browser console for authentication issues
- Verify operations complete without auth prompts

### SECTION-04: Settings

**Procedure:**
1. Login to the admin panel with admin credentials
2. Navigate to the Settings section
3. Perform the following operations:
   - View current settings
   - Update settings
   - Save changes

**Expected Result:**
- Authentication persists across all operations
- All operations complete successfully
- No authentication errors

**Validation Method:**
- Monitor network requests for auth errors
- Check browser console for authentication issues
- Verify operations complete without auth prompts

## 5. Device Compatibility Tests

### DEVICE-01: Desktop

**Procedure:**
1. Complete all Authentication Flow Tests (AUTH-01 through AUTH-06) on a desktop computer
2. Test with different screen resolutions

**Expected Result:**
- All tests pass consistently
- Authentication works properly regardless of screen size

**Validation Method:**
- Visual verification of expected behavior
- Console monitoring for errors
- Verify responsive design elements related to auth

### DEVICE-02: Mobile

**Procedure:**
1. Complete all Authentication Flow Tests (AUTH-01 through AUTH-06) on a mobile device or emulator
2. Test in both portrait and landscape orientations

**Expected Result:**
- All tests pass consistently
- Authentication works properly on mobile devices

**Validation Method:**
- Visual verification of expected behavior
- Console monitoring for mobile-specific errors
- Verify touch interactions work for auth elements

### DEVICE-03: Tablet

**Procedure:**
1. Complete all Authentication Flow Tests (AUTH-01 through AUTH-06) on a tablet device or emulator
2. Test in both portrait and landscape orientations

**Expected Result:**
- All tests pass consistently
- Authentication works properly on tablet devices

**Validation Method:**
- Visual verification of expected behavior
- Console monitoring for tablet-specific errors
- Verify touch interactions work for auth elements

## 6. Implementation Verification Tests

### IMPL-01: Supabase Client

**Procedure:**
1. Review the implementation of `lib/supabase.js`
2. Verify it follows the recommended pattern from the remediation plan
3. Check for single client instance with correct configuration

**Expected Result:**
- Implementation matches recommended pattern
- Single client instance with correct configuration
- Proper error handling and timeout configuration

**Validation Method:**
- Code review against remediation plan
- Verify configuration matches recommended settings

### IMPL-02: AuthProvider

**Procedure:**
1. Review the implementation of `components/admin/AuthProvider.js`
2. Verify it follows the recommended pattern from the remediation plan
3. Test authentication state management in the browser

**Expected Result:**
- Implementation matches recommended pattern
- Simplified component with proper state management
- Correct handling of auth state changes

**Validation Method:**
- Code review against remediation plan
- Runtime verification of auth state management
- Check React DevTools for component state

### IMPL-03: API Authentication

**Procedure:**
1. Review the implementation of `lib/admin-auth.js`
2. Verify it follows the recommended pattern from the remediation plan
3. Test API authentication with various scenarios

**Expected Result:**
- Implementation matches recommended pattern
- Consistent token extraction and validation
- Proper error handling for authentication failures

**Validation Method:**
- Code review against remediation plan
- API testing with various authentication scenarios
- Verify error responses match expected format

### IMPL-04: Error Handling

**Procedure:**
1. Review error handling in authentication-related code
2. Force various error conditions:
   - Network errors (disconnect internet)
   - Invalid tokens
   - Expired tokens
   - Server errors (if possible)

**Expected Result:**
- User-friendly error messages for all error conditions
- Proper recovery from authentication errors
- Consistent error handling across the application

**Validation Method:**
- Manual testing with forced errors
- Verify error messages are user-friendly
- Check recovery paths work as expected

## Test Results Documentation

For each test case, document the following:

1. Test ID and name
2. Date and time of test
3. Tester name
4. Environment details (browser, device, etc.)
5. Test result (Pass/Fail)
6. Observations or issues encountered
7. Screenshots or logs (if applicable)

Use the following format for test results:

```
Test ID: [ID]
Name: [Test Name]
Date: [Date]
Tester: [Name]
Environment: [Browser/Device/OS]
Result: [Pass/Fail]
Observations: [Any notes or issues]
```

## Test Result Summary

Create a summary table of all test results:

| Test ID | Name | Result | Issues |
|---------|------|--------|--------|
| API-01 | Valid Admin Token Test | Pass/Fail | [Description] |
| API-02 | Expired Token Test | Pass/Fail | [Description] |
| ... | ... | ... | ... |

## Next Steps

After completing all tests:

1. Document any issues discovered during testing
2. Prioritize issues based on severity
3. Create tickets for any required fixes
4. Update the remediation plan as needed
5. Schedule follow-up testing for any fixes

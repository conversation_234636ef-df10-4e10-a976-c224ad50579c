/**
 * Browser Token Fix Script
 * 
 * This script generates code to fix authentication token storage in the browser.
 * It ensures tokens are stored in the correct format and location for the admin panel.
 * 
 * Usage:
 * 1. Run with: node scripts/fix-browser-tokens.js
 * 2. Copy the generated code and paste it into your browser console
 * 3. Refresh the page and try accessing the admin panel again
 */

console.log(`
// ============================================================
// AUTHENTICATION TOKEN FIX
// ============================================================
// Copy and paste this code into your browser console
// when on the Ocean Soul Sparkles website to fix token storage
// ============================================================

(function fixAuthTokens() {
  console.log('Starting authentication token fix...');
  
  // Step 1: Extract tokens from all possible sources
  let accessToken = null;
  let refreshToken = null;
  let userId = null;
  let tokenExpiry = null;
  
  // Check localStorage for Supabase token
  try {
    console.log('Checking localStorage for tokens...');
    const supabaseKey = 'sb-ndlgbcsbidyhxbpqzgqp-auth-token';
    const supabaseToken = localStorage.getItem(supabaseKey);
    
    if (supabaseToken) {
      try {
        const parsedToken = JSON.parse(supabaseToken);
        if (parsedToken.access_token) {
          accessToken = parsedToken.access_token;
          refreshToken = parsedToken.refresh_token || '';
          userId = parsedToken.user?.id;
          
          // Calculate expiry if available
          if (parsedToken.expires_at) {
            tokenExpiry = parsedToken.expires_at * 1000; // Convert to milliseconds
          } else if (parsedToken.expires_in) {
            tokenExpiry = Date.now() + (parsedToken.expires_in * 1000);
          } else {
            // Default to 1 hour from now
            tokenExpiry = Date.now() + 3600000;
          }
          
          console.log('Found token in Supabase localStorage');
        }
      } catch (e) {
        console.error('Error parsing Supabase token:', e);
      }
    }
    
    // Check other localStorage keys
    if (!accessToken) {
      const ossToken = localStorage.getItem('oss_auth_token');
      if (ossToken) {
        try {
          // Try parsing as JSON first
          try {
            const parsedToken = JSON.parse(ossToken);
            if (parsedToken.access_token) {
              accessToken = parsedToken.access_token;
              console.log('Found JSON token in oss_auth_token');
            }
          } catch {
            // If not JSON, use as is
            accessToken = ossToken;
            console.log('Found string token in oss_auth_token');
          }
        } catch (e) {
          console.error('Error processing oss_auth_token:', e);
        }
      }
    }
  } catch (e) {
    console.error('Error checking localStorage:', e);
  }
  
  // Check sessionStorage if no token found yet
  if (!accessToken) {
    try {
      console.log('Checking sessionStorage for tokens...');
      const cacheToken = sessionStorage.getItem('oss_auth_token_cache');
      
      if (cacheToken) {
        try {
          const parsedCache = JSON.parse(cacheToken);
          if (parsedCache.token) {
            accessToken = parsedCache.token;
            tokenExpiry = parsedCache.expiry || (Date.now() + 3600000);
            console.log('Found token in oss_auth_token_cache');
          }
        } catch (e) {
          console.error('Error parsing cache token:', e);
        }
      }
      
      // Check other sessionStorage keys
      if (!accessToken) {
        const ossSession = sessionStorage.getItem('oss_session');
        if (ossSession) {
          try {
            const parsedSession = JSON.parse(ossSession);
            if (parsedSession.access_token) {
              accessToken = parsedSession.access_token;
              refreshToken = parsedSession.refresh_token || '';
              userId = parsedSession.user?.id;
              console.log('Found token in oss_session');
            }
          } catch (e) {
            console.error('Error parsing oss_session:', e);
          }
        }
      }
    } catch (e) {
      console.error('Error checking sessionStorage:', e);
    }
  }
  
  // If we found a token, store it in all the right places
  if (accessToken) {
    console.log('Token found! Storing in all required locations...');
    
    // Set expiry if not already set
    if (!tokenExpiry) {
      tokenExpiry = Date.now() + 3600000; // 1 hour from now
    }
    
    // Store in localStorage
    try {
      // Store as string
      localStorage.setItem('oss_auth_token', accessToken);
      localStorage.setItem('sb_auth_token', accessToken);
      
      console.log('Stored token in localStorage');
    } catch (e) {
      console.error('Error storing in localStorage:', e);
    }
    
    // Store in sessionStorage
    try {
      // Store cache format
      sessionStorage.setItem('oss_auth_token_cache', JSON.stringify({
        token: accessToken,
        expiry: tokenExpiry,
        refreshed: Date.now()
      }));
      
      // Store session format
      const sessionData = {
        access_token: accessToken,
        refresh_token: refreshToken || '',
        user: { id: userId || 'unknown' },
        timestamp: Date.now()
      };
      
      sessionStorage.setItem('oss_session', JSON.stringify(sessionData));
      
      console.log('Stored token in sessionStorage');
    } catch (e) {
      console.error('Error storing in sessionStorage:', e);
    }
    
    // Store in cookies
    try {
      document.cookie = \`oss_auth_token=\${accessToken}; path=/; max-age=3600\`;
      document.cookie = \`sb_auth_token=\${accessToken}; path=/; max-age=3600\`;
      
      console.log('Stored token in cookies');
    } catch (e) {
      console.error('Error storing in cookies:', e);
    }
    
    console.log('Authentication token fix complete!');
    console.log('Please refresh the page and try accessing the admin panel again.');
  } else {
    console.log('No valid token found. Please log in again.');
  }
})();
`);

console.log('\nCopy the code above and paste it into your browser console when on the Ocean Soul Sparkles website.');
console.log('Then refresh the page and try accessing the admin panel again.');

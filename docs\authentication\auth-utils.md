# Authentication Utilities

This document describes the authentication utilities available in the Ocean Soul Sparkles admin panel.

## Overview

The authentication system in the Ocean Soul Sparkles admin panel has been designed to provide a consistent, reliable, and secure way to make authenticated API requests. The system includes:

1. A reusable `authenticatedFetch` utility function
2. Automatic token retrieval and refresh
3. Consistent error handling
4. Loading state management

## Using authenticatedFetch

The `authenticatedFetch` utility function is the recommended way to make authenticated API requests in the Ocean Soul Sparkles admin panel. It handles token retrieval, error handling, and token refresh automatically.

### Basic Usage

```javascript
import { authenticatedFetch } from '@/lib/auth-utils';

// GET request
const data = await authenticatedFetch('/api/customers');

// POST request
const newCustomer = await authenticatedFetch('/api/customers', {
  method: 'POST',
  body: JSON.stringify({
    name: '<PERSON>',
    email: '<EMAIL>'
  })
});

// PUT request
const updatedCustomer = await authenticatedFetch(`/api/customers/${id}`, {
  method: 'PUT',
  body: JSON.stringify({
    name: '<PERSON>'
  })
});

// DELETE request
await authenticatedFetch(`/api/customers/${id}`, {
  method: 'DELETE'
});
```

### Advanced Options

The `authenticatedFetch` function accepts additional options to customize its behavior:

```javascript
const data = await authenticatedFetch(
  '/api/customers',
  {
    // Standard fetch options
    method: 'GET',
    headers: {
      // Additional headers (Authorization will be added automatically)
      'X-Custom-Header': 'value'
    }
  },
  {
    // Authentication options
    refresh: true,    // Whether to attempt token refresh on 401 (default: true)
    redirect: true,   // Whether to redirect on auth failure (default: true)
    notify: true      // Whether to show notifications (default: true)
  }
);
```

## Error Handling

The authentication system provides consistent error handling for authentication failures:

- **Token Expired**: "Your session has expired. Please log in again."
- **Token Missing**: "Authentication required. Please log in to continue."
- **Insufficient Permissions**: "You don't have permission to perform this action."
- **Network Error**: "Network error. Please check your connection and try again."
- **Server Error**: "Server error. Please try again later."

When an authentication error occurs, the system will:

1. Attempt to refresh the token if it's expired
2. Show a notification with the appropriate error message
3. Redirect to the login page if necessary

## LoadingButton Component

The `LoadingButton` component is a reusable button component that shows a loading spinner when in loading state. It's designed to provide consistent loading state feedback across the admin panel.

### Basic Usage

```jsx
import LoadingButton from '@/components/admin/LoadingButton';

// In your component
const [loading, setLoading] = useState(false);

const handleClick = async () => {
  setLoading(true);
  try {
    await authenticatedFetch('/api/some-endpoint');
    // Handle success
  } catch (error) {
    // Handle error
  } finally {
    setLoading(false);
  }
};

return (
  <LoadingButton
    loading={loading}
    onClick={handleClick}
    loadingText="Processing..."
  >
    Submit
  </LoadingButton>
);
```

### Available Props

- `loading`: Whether the button is in loading state
- `loadingText`: Text to display when loading (default: "Loading...")
- `type`: Button type (default: "button")
- `variant`: Button variant (default: "primary")
- `fullWidth`: Whether the button should take full width
- `onClick`: Click handler
- `className`: Additional CSS classes
- `disabled`: Whether the button is disabled

## Best Practices

1. **Always use authenticatedFetch**: Use the `authenticatedFetch` utility function for all API requests that require authentication.

2. **Handle loading states**: Use the `LoadingButton` component to provide consistent loading state feedback.

3. **Disable forms during submission**: Disable all form inputs and buttons during API requests to prevent duplicate submissions.

4. **Provide visual feedback**: Add visual feedback (like a subtle background color change) to forms that are in a submitting state.

5. **Handle errors gracefully**: Display error messages in a user-friendly way and provide clear instructions on how to resolve the issue.

## Implementation Details

The authentication system is implemented in the following files:

- `lib/auth-utils.js`: Contains the `authenticatedFetch` utility function and related utilities
- `components/admin/LoadingButton.js`: Contains the `LoadingButton` component
- `styles/admin/LoadingButton.module.css`: Contains the styles for the `LoadingButton` component

The system uses Supabase for authentication and token management. It automatically handles token retrieval, refresh, and storage.

## Troubleshooting

If you encounter authentication issues:

1. Check the browser console for error messages
2. Verify that the user is logged in
3. Check that the API endpoint is correctly configured to require authentication
4. Try refreshing the page to get a new token
5. If all else fails, log out and log back in to get a fresh token

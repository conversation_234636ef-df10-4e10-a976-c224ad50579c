# Supabase Client

This document describes the Supabase client implementation in the Ocean Soul Sparkles application.

## Overview

The `lib/supabase.js` file provides a centralized interface for interacting with Supabase services. It includes:

1. **Client Initialization**: Creates and configures the Supabase client
2. **Authentication Helpers**: Functions for managing user authentication
3. **Admin Client**: Service role client for privileged operations
4. **Session Management**: Functions for managing user sessions
5. **User Management**: Functions for managing user accounts and roles

## Client Initialization

The Supabase client is initialized with the project URL and anonymous key:

```javascript
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

export default supabase;
```

## Key Functions

### getSession

Retrieves the current user session:

```javascript
/**
 * Get the current session
 *
 * @returns {Promise<Object|null>} Session data or null
 */
export const getSession = async () => {
  try {
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Error getting session:', error);
      return null;
    }
    
    return data.session;
  } catch (error) {
    console.error('Exception in getSession:', error);
    return null;
  }
};
```

### getCurrentUser

Retrieves the current user with role information:

```javascript
/**
 * Get the current user
 * Convenience method with role information
 *
 * @returns {Promise<Object>} User data with role or null
 */
export const getCurrentUser = async () => {
  try {
    const session = await getSession();

    if (!session || !session.user) {
      return { user: null, role: null };
    }

    // Get user role from user_roles table
    const { data: roleData, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (roleError) {
      console.error('Error fetching user role:', roleError);

      // Fallback for known admin users
      const knownAdminIds = [
        '8c59a3bc-a96b-4555-bdc4-6abe905ae761', // <EMAIL>
        'c6080246-db51-485e-8e29-69be7cc86cdb'  // <EMAIL>
      ];

      if (knownAdminIds.includes(session.user.id)) {
        return {
          user: session.user,
          role: 'admin'
        };
      }

      return {
        user: session.user,
        role: null
      };
    }

    return {
      user: session.user,
      role: roleData.role
    };
  } catch (error) {
    console.error('Error in getCurrentUser:', error);
    return { user: null, role: null };
  }
};
```

### getCurrentUserWithToken

Retrieves user information from a provided token:

```javascript
/**
 * Get the current user with a specific token
 * Used for admin authentication with a provided token
 *
 * @param {string} token - JWT token
 * @returns {Promise<Object>} User data with role or error
 */
export const getCurrentUserWithToken = async (token) => {
  try {
    if (!token) {
      return { user: null, role: null, error: new Error('No token provided') };
    }

    // Get admin client to verify the token
    const adminClient = getAdminClient();
    
    // Verify token
    const { data, error } = await adminClient.auth.getUser(token);

    if (error) {
      return { user: null, role: null, error };
    }

    if (!data.user) {
      return { user: null, role: null, error: new Error('Invalid token') };
    }

    // Get user role from user_roles table
    const { data: roleData, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', data.user.id)
      .single();

    if (roleError) {
      console.error('Error fetching user role with token:', roleError);

      // Fallback for known admin users
      const knownAdminIds = [
        '8c59a3bc-a96b-4555-bdc4-6abe905ae761', // <EMAIL>
        'c6080246-db51-485e-8e29-69be7cc86cdb'  // <EMAIL>
      ];

      if (knownAdminIds.includes(data.user.id)) {
        return {
          user: data.user,
          role: 'admin',
          error: null
        };
      }

      return {
        user: data.user,
        role: null,
        error: roleError
      };
    }

    return {
      user: data.user,
      role: roleData.role,
      error: null
    };
  } catch (error) {
    console.error('Error in getCurrentUserWithToken:', error);
    return { user: null, role: null, error };
  }
};
```

### getAdminClient

Creates a Supabase client with service role privileges for admin operations:

```javascript
/**
 * Get a Supabase client with service role privileges
 * This should only be used server-side for admin operations
 *
 * @returns {Object} Supabase client with service role
 */
export const getAdminClient = () => {
  if (typeof window !== 'undefined') {
    throw new Error('Admin client can only be used server-side');
  }

  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  if (!serviceRoleKey) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is not defined');
  }

  return createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};
```

### refreshAuthToken

Refreshes the authentication token:

```javascript
/**
 * Refresh the authentication token
 *
 * @returns {Promise<string|null>} New token or null
 */
export const refreshAuthToken = async () => {
  try {
    console.log('Starting token refresh');
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      console.error('Token refresh failed:', error);
      return null;
    }

    if (data?.session?.access_token) {
      const token = data.session.access_token;
      console.log('Token refreshed successfully');
      return token;
    } else {
      console.error('No token in refresh response');
      return null;
    }
  } catch (error) {
    console.error('Error refreshing token:', error);
    return null;
  }
};
```

### signIn

Signs in a user with email and password:

```javascript
/**
 * Sign in with email and password
 *
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise<Object>} Authentication result
 */
export const signIn = async (email, password) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) throw error;

    return { data, error: null };
  } catch (error) {
    return { data: null, error };
  }
};
```

### signOut

Signs out the current user:

```javascript
/**
 * Sign out the current user
 *
 * @returns {Promise<Object>} Sign out result
 */
export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    return { error };
  } catch (error) {
    console.error('Sign out error:', error);
    return { error };
  }
};
```

## Usage Examples

### Authentication

```javascript
import supabase, { signIn, signOut, getCurrentUser } from '@/lib/supabase';

// Sign in
const handleSignIn = async (email, password) => {
  const { data, error } = await signIn(email, password);
  
  if (error) {
    console.error('Sign in error:', error.message);
    return;
  }
  
  console.log('Signed in successfully:', data.user);
};

// Get current user
const checkAuth = async () => {
  const { user, role } = await getCurrentUser();
  
  if (user) {
    console.log('User is authenticated:', user.email);
    console.log('User role:', role);
  } else {
    console.log('User is not authenticated');
  }
};

// Sign out
const handleSignOut = async () => {
  const { error } = await signOut();
  
  if (error) {
    console.error('Sign out error:', error.message);
    return;
  }
  
  console.log('Signed out successfully');
};
```

### Database Operations

```javascript
import supabase from '@/lib/supabase';

// Fetch data
const fetchCustomers = async () => {
  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .order('created_at', { ascending: false });
  
  if (error) {
    console.error('Error fetching customers:', error);
    return [];
  }
  
  return data;
};

// Insert data
const createCustomer = async (customer) => {
  const { data, error } = await supabase
    .from('customers')
    .insert(customer)
    .select();
  
  if (error) {
    console.error('Error creating customer:', error);
    return null;
  }
  
  return data[0];
};

// Update data
const updateCustomer = async (id, updates) => {
  const { data, error } = await supabase
    .from('customers')
    .update(updates)
    .eq('id', id)
    .select();
  
  if (error) {
    console.error('Error updating customer:', error);
    return null;
  }
  
  return data[0];
};

// Delete data
const deleteCustomer = async (id) => {
  const { error } = await supabase
    .from('customers')
    .delete()
    .eq('id', id);
  
  if (error) {
    console.error('Error deleting customer:', error);
    return false;
  }
  
  return true;
};
```

### Admin Operations

```javascript
import { getAdminClient } from '@/lib/supabase';

// This function must be called server-side
export const createUserWithRole = async (email, password, role) => {
  const adminClient = getAdminClient();
  
  // Create user
  const { data: userData, error: userError } = await adminClient.auth.admin.createUser({
    email,
    password,
    email_confirm: true
  });
  
  if (userError) {
    console.error('Error creating user:', userError);
    return { user: null, error: userError };
  }
  
  // Assign role
  const { error: roleError } = await adminClient
    .from('user_roles')
    .insert({
      id: userData.user.id,
      role
    });
  
  if (roleError) {
    console.error('Error assigning role:', roleError);
    return { user: userData.user, error: roleError };
  }
  
  return { user: userData.user, error: null };
};
```

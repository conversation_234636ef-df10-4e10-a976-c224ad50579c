import { getAdminClient, getCurrentUserFromRequest, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUserFromRequest(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  const { id } = req.query

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getCampaign(id, res)
    case 'PUT':
      return updateCampaign(id, req, res)
    case 'DELETE':
      // Only admins can delete campaigns
      try {
        const { role } = await getCurrentUserFromRequest(req)
        if (role !== 'admin') {
          return res.status(403).json({ error: 'Forbidden' })
        }
      } catch (error) {
        return res.status(403).json({ error: 'Authorization failed' })
      }
      return deleteCampaign(id, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get a single campaign with messages and metrics
async function getCampaign(id, res) {
  try {
    const client = getClient();
    if (!client) {
      console.error("Client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    // Get campaign details
    const { data: campaign, error } = await client
      .from('marketing_campaigns')
      .select(`
        *,
        target_segment:customer_segments (id, name, description)
      `)
      .eq('id', id)
      .single()

    if (error) {
      throw error
    }

    if (!campaign) {
      return res.status(404).json({ error: 'Campaign not found' })
    }

    // Get campaign messages
    const { data: messages, error: messagesError } = await supabase
      .from('campaign_messages')
      .select('*')
      .eq('campaign_id', id)
      .order('created_at', { ascending: false })

    if (messagesError) {
      throw messagesError
    }

    // Get campaign metrics
    const { data: metricsData, error: metricsError } = await supabase
      .from('campaign_metrics')
      .select('metric_type, metric_value')
      .eq('campaign_id', id)

    if (metricsError) {
      throw metricsError
    }

    // Group metrics by type
    const metrics = {}
    metricsData.forEach(metric => {
      metrics[metric.metric_type] = (metrics[metric.metric_type] || 0) + metric.metric_value
    })

    // Get segment customer count
    let customerCount = 0
    if (campaign.target_segment) {
      const segmentResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/marketing/segments/${campaign.target_segment.id}`)
      if (segmentResponse.ok) {
        const segmentData = await segmentResponse.json()
        customerCount = segmentData.segment.customer_count || 0
      }
    }

    // Return campaign with messages and metrics
    return res.status(200).json({
      campaign,
      messages: messages || [],
      metrics,
      customer_count: customerCount
    })
  } catch (error) {
    console.error('Error fetching campaign:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Update a campaign
async function updateCampaign(id, req, res) {
  const {
    name,
    description,
    start_date,
    end_date,
    status,
    campaign_type,
    target_segment
  } = req.body

  try {
    // Validate required fields
    if (!name || !start_date || !status || !campaign_type) {
      return res.status(400).json({ error: 'Name, start date, status, and campaign type are required' })
    }

    // Update campaign
    const { data, error } = await supabase
      .from('marketing_campaigns')
      .update({
        name,
        description,
        start_date,
        end_date,
        status,
        campaign_type,
        target_segment,
        updated_at: new Date()
      })
      .eq('id', id)
      .select()

    if (error) {
      throw error
    }

    if (!data || data.length === 0) {
      return res.status(404).json({ error: 'Campaign not found' })
    }

    return res.status(200).json(data[0])
  } catch (error) {
    console.error('Error updating campaign:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Delete a campaign
async function deleteCampaign(id, res) {
  try {
    // Check if campaign has sent messages
    const { data: messages, error: messagesError } = await supabase
      .from('campaign_messages')
      .select('id, status')
      .eq('campaign_id', id)
      .eq('status', 'sent')
      .limit(1)

    if (messagesError) {
      throw messagesError
    }

    if (messages && messages.length > 0) {
      return res.status(400).json({
        error: 'Cannot delete campaign with sent messages',
        messages: messages
      })
    }

    // Delete campaign messages
    const { error: deleteMessagesError } = await supabase
      .from('campaign_messages')
      .delete()
      .eq('campaign_id', id)

    if (deleteMessagesError) {
      throw deleteMessagesError
    }

    // Delete campaign metrics
    const { error: deleteMetricsError } = await supabase
      .from('campaign_metrics')
      .delete()
      .eq('campaign_id', id)

    if (deleteMetricsError) {
      throw deleteMetricsError
    }

    // Delete campaign
    const { error: deleteCampaignError } = await supabase
      .from('marketing_campaigns')
      .delete()
      .eq('id', id)

    if (deleteCampaignError) {
      throw deleteCampaignError
    }

    return res.status(200).json({ success: true })
  } catch (error) {
    console.error('Error deleting campaign:', error)
    return res.status(500).json({ error: error.message })
  }
}

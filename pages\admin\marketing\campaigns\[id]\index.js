import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import MessageEditor from '@/components/admin/marketing/MessageEditor'
import Modal from '@/components/admin/Modal'
import styles from '@/styles/admin/marketing/CampaignDetail.module.css'

export default function CampaignDetail() {
  const router = useRouter()
  const { id } = router.query
  const [campaign, setCampaign] = useState(null)
  const [messages, setMessages] = useState([])
  const [metrics, setMetrics] = useState({})
  const [customerCount, setCustomerCount] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [deleteError, setDeleteError] = useState(null)
  const [showNewMessageModal, setShowNewMessageModal] = useState(false)
  const [showEditMessageModal, setShowEditMessageModal] = useState(false)
  const [currentMessage, setCurrentMessage] = useState(null)
  const [messageLoading, setMessageLoading] = useState(false)
  const [messageError, setMessageError] = useState(null)
  const [messageSuccess, setMessageSuccess] = useState(null)

  // Fetch campaign data
  useEffect(() => {
    if (!id) return

    const fetchCampaign = async () => {
      setLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/marketing/campaigns/${id}`)
        
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch campaign')
        }

        const data = await response.json()
        setCampaign(data.campaign || null)
        setMessages(data.messages || [])
        setMetrics(data.metrics || {})
        setCustomerCount(data.customer_count || 0)
      } catch (error) {
        console.error('Error fetching campaign:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchCampaign()
  }, [id])

  // Handle delete campaign
  const handleDeleteCampaign = async () => {
    setDeleteLoading(true)
    setDeleteError(null)

    try {
      const response = await fetch(`/api/marketing/campaigns/${id}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete campaign')
      }

      // Redirect to campaign list
      router.push('/admin/marketing/campaigns')
    } catch (error) {
      console.error('Error deleting campaign:', error)
      setDeleteError(error.message)
      setDeleteLoading(false)
    }
  }

  // Handle create message
  const handleCreateMessage = async (messageData) => {
    setMessageLoading(true)
    setMessageError(null)
    setMessageSuccess(null)

    try {
      const response = await fetch(`/api/marketing/campaigns/${id}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(messageData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create message')
      }

      const data = await response.json()
      
      // Add new message to list
      setMessages([data, ...messages])
      setMessageSuccess('Message created successfully')
      
      // Close modal after a short delay
      setTimeout(() => {
        setShowNewMessageModal(false)
        setMessageSuccess(null)
      }, 1500)
    } catch (error) {
      console.error('Error creating message:', error)
      setMessageError(error.message)
    } finally {
      setMessageLoading(false)
    }
  }

  // Handle update message
  const handleUpdateMessage = async (messageData) => {
    if (!currentMessage) return
    
    setMessageLoading(true)
    setMessageError(null)
    setMessageSuccess(null)

    try {
      const response = await fetch(`/api/marketing/campaigns/${id}/messages/${currentMessage.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(messageData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update message')
      }

      const data = await response.json()
      
      // Update message in list
      setMessages(messages.map(msg => msg.id === data.id ? data : msg))
      setMessageSuccess('Message updated successfully')
      
      // Close modal after a short delay
      setTimeout(() => {
        setShowEditMessageModal(false)
        setCurrentMessage(null)
        setMessageSuccess(null)
      }, 1500)
    } catch (error) {
      console.error('Error updating message:', error)
      setMessageError(error.message)
    } finally {
      setMessageLoading(false)
    }
  }

  // Handle delete message
  const handleDeleteMessage = async () => {
    if (!currentMessage) return
    
    setMessageLoading(true)
    setMessageError(null)

    try {
      const response = await fetch(`/api/marketing/campaigns/${id}/messages/${currentMessage.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete message')
      }
      
      // Remove message from list
      setMessages(messages.filter(msg => msg.id !== currentMessage.id))
      
      // Close modal
      setShowEditMessageModal(false)
      setCurrentMessage(null)
    } catch (error) {
      console.error('Error deleting message:', error)
      setMessageError(error.message)
    } finally {
      setMessageLoading(false)
    }
  }

  // Handle send message
  const handleSendMessage = async (testMode = false) => {
    if (!currentMessage) return
    
    setMessageLoading(true)
    setMessageError(null)
    setMessageSuccess(null)

    try {
      const response = await fetch(`/api/marketing/campaigns/${id}/messages/${currentMessage.id}/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ test_mode: testMode })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to send message')
      }

      const data = await response.json()
      
      // Show success message
      setMessageSuccess(
        testMode
          ? `Test message sent to ${data.sent} customers`
          : `Message sent to ${data.sent} customers`
      )
      
      // Refresh campaign data after sending
      if (!testMode) {
        setTimeout(() => {
          router.reload()
        }, 1500)
      }
    } catch (error) {
      console.error('Error sending message:', error)
      setMessageError(error.message)
    } finally {
      setMessageLoading(false)
    }
  }

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Get status class
  const getStatusClass = (status) => {
    switch (status) {
      case 'active':
        return styles.statusActive
      case 'scheduled':
        return styles.statusScheduled
      case 'completed':
        return styles.statusCompleted
      case 'draft':
        return styles.statusDraft
      case 'paused':
        return styles.statusPaused
      case 'canceled':
        return styles.statusCanceled
      case 'sent':
        return styles.statusSent
      default:
        return ''
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className={styles.campaignDetail}>
          <div className={styles.loading}>Loading campaign data...</div>
        </div>
      </AdminLayout>
    )
  }

  if (error) {
    return (
      <AdminLayout>
        <div className={styles.campaignDetail}>
          <div className={styles.error}>
            Error: {error}
            <button
              className={styles.backButton}
              onClick={() => router.push('/admin/marketing/campaigns')}
            >
              Back to Campaigns
            </button>
          </div>
        </div>
      </AdminLayout>
    )
  }

  if (!campaign) {
    return (
      <AdminLayout>
        <div className={styles.campaignDetail}>
          <div className={styles.notFound}>
            Campaign not found
            <button
              className={styles.backButton}
              onClick={() => router.push('/admin/marketing/campaigns')}
            >
              Back to Campaigns
            </button>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className={styles.campaignDetail}>
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <h2>{campaign.name}</h2>
            <div className={styles.meta}>
              <span className={`${styles.status} ${getStatusClass(campaign.status)}`}>
                {campaign.status}
              </span>
              <span className={styles.type}>
                {campaign.campaign_type} Campaign
              </span>
              <span className={styles.dates}>
                {formatDate(campaign.start_date)} - {campaign.end_date ? formatDate(campaign.end_date) : 'Ongoing'}
              </span>
            </div>
          </div>
          <div className={styles.headerActions}>
            <Link href={`/admin/marketing/campaigns/${id}/edit`}>
              <a className={styles.editButton}>Edit Campaign</a>
            </Link>
            <button
              className={styles.deleteButton}
              onClick={() => setShowDeleteModal(true)}
            >
              Delete
            </button>
          </div>
        </div>

        <div className={styles.campaignInfo}>
          <div className={styles.infoSection}>
            <h3>Campaign Details</h3>
            <div className={styles.infoGrid}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Description</span>
                <span className={styles.infoValue}>
                  {campaign.description || 'No description provided'}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Target Segment</span>
                <span className={styles.infoValue}>
                  {campaign.target_segment ? (
                    <Link href={`/admin/marketing/segments/${campaign.target_segment.id}`}>
                      <a className={styles.segmentLink}>
                        {campaign.target_segment.name}
                      </a>
                    </Link>
                  ) : (
                    'No segment selected'
                  )}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Target Audience</span>
                <span className={styles.infoValue}>
                  {customerCount} customers
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Created</span>
                <span className={styles.infoValue}>
                  {formatDate(campaign.created_at)}
                </span>
              </div>
            </div>
          </div>

          <div className={styles.infoSection}>
            <div className={styles.sectionHeader}>
              <h3>Campaign Messages</h3>
              <button
                className={styles.addMessageButton}
                onClick={() => setShowNewMessageModal(true)}
                disabled={campaign.status === 'completed' || campaign.status === 'canceled'}
              >
                Add Message
              </button>
            </div>

            {messages.length === 0 ? (
              <div className={styles.noMessages}>
                No messages created yet. Click "Add Message" to create your first message.
              </div>
            ) : (
              <div className={styles.messageList}>
                {messages.map((message) => (
                  <div key={message.id} className={styles.messageCard}>
                    <div className={styles.messageHeader}>
                      <div className={styles.messageSubject}>{message.subject}</div>
                      <div className={`${styles.messageStatus} ${getStatusClass(message.status)}`}>
                        {message.status}
                      </div>
                    </div>
                    <div className={styles.messageType}>
                      {message.message_type === 'email' ? 'Email' : 
                       message.message_type === 'sms' ? 'SMS' : 'Push Notification'}
                    </div>
                    <div className={styles.messageContent}>
                      {message.content.length > 100
                        ? `${message.content.substring(0, 100)}...`
                        : message.content}
                    </div>
                    <div className={styles.messageMeta}>
                      {message.status === 'scheduled' && (
                        <div className={styles.messageSchedule}>
                          Scheduled: {formatDate(message.scheduled_date)}
                        </div>
                      )}
                      {message.status === 'sent' && (
                        <div className={styles.messageSent}>
                          Sent: {formatDate(message.sent_date)}
                        </div>
                      )}
                    </div>
                    <div className={styles.messageActions}>
                      <button
                        className={styles.viewMessageButton}
                        onClick={() => {
                          setCurrentMessage(message)
                          setShowEditMessageModal(true)
                        }}
                      >
                        {message.status === 'sent' ? 'View' : 'Edit'}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className={styles.infoSection}>
            <h3>Campaign Metrics</h3>
            {Object.keys(metrics).length === 0 ? (
              <div className={styles.noMetrics}>
                No metrics available yet. Metrics will be displayed after messages are sent.
              </div>
            ) : (
              <div className={styles.metricsGrid}>
                <div className={styles.metricCard}>
                  <div className={styles.metricValue}>{metrics.sent || 0}</div>
                  <div className={styles.metricLabel}>Messages Sent</div>
                </div>
                {metrics.opened !== undefined && (
                  <div className={styles.metricCard}>
                    <div className={styles.metricValue}>
                      {metrics.opened || 0}
                      {metrics.sent > 0 && (
                        <span className={styles.metricPercent}>
                          ({Math.round((metrics.opened / metrics.sent) * 100)}%)
                        </span>
                      )}
                    </div>
                    <div className={styles.metricLabel}>Opens</div>
                  </div>
                )}
                {metrics.clicked !== undefined && (
                  <div className={styles.metricCard}>
                    <div className={styles.metricValue}>
                      {metrics.clicked || 0}
                      {metrics.opened > 0 && (
                        <span className={styles.metricPercent}>
                          ({Math.round((metrics.clicked / metrics.opened) * 100)}%)
                        </span>
                      )}
                    </div>
                    <div className={styles.metricLabel}>Clicks</div>
                  </div>
                )}
                {metrics.converted !== undefined && (
                  <div className={styles.metricCard}>
                    <div className={styles.metricValue}>
                      {metrics.converted || 0}
                      {metrics.clicked > 0 && (
                        <span className={styles.metricPercent}>
                          ({Math.round((metrics.converted / metrics.clicked) * 100)}%)
                        </span>
                      )}
                    </div>
                    <div className={styles.metricLabel}>Conversions</div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <Modal onClose={() => setShowDeleteModal(false)}>
            <div className={styles.deleteModal}>
              <h3>Delete Campaign</h3>
              <p>
                Are you sure you want to delete the campaign "{campaign.name}"?
                This action cannot be undone.
              </p>
              {deleteError && (
                <div className={styles.deleteError}>
                  Error: {deleteError}
                </div>
              )}
              <div className={styles.deleteActions}>
                <button
                  className={styles.cancelDeleteButton}
                  onClick={() => setShowDeleteModal(false)}
                  disabled={deleteLoading}
                >
                  Cancel
                </button>
                <button
                  className={styles.confirmDeleteButton}
                  onClick={handleDeleteCampaign}
                  disabled={deleteLoading}
                >
                  {deleteLoading ? 'Deleting...' : 'Delete Campaign'}
                </button>
              </div>
            </div>
          </Modal>
        )}

        {/* New Message Modal */}
        {showNewMessageModal && (
          <Modal onClose={() => !messageLoading && setShowNewMessageModal(false)}>
            <div className={styles.messageModal}>
              <h3>Create New Message</h3>
              {messageError && (
                <div className={styles.messageError}>
                  Error: {messageError}
                </div>
              )}
              {messageSuccess && (
                <div className={styles.messageSuccess}>
                  {messageSuccess}
                </div>
              )}
              <MessageEditor
                campaignId={id}
                onSave={handleCreateMessage}
                onCancel={() => setShowNewMessageModal(false)}
                onDelete={() => {}}
                onSend={() => {}}
              />
            </div>
          </Modal>
        )}

        {/* Edit Message Modal */}
        {showEditMessageModal && currentMessage && (
          <Modal onClose={() => !messageLoading && setShowEditMessageModal(false)}>
            <div className={styles.messageModal}>
              <h3>{currentMessage.status === 'sent' ? 'View Message' : 'Edit Message'}</h3>
              {messageError && (
                <div className={styles.messageError}>
                  Error: {messageError}
                </div>
              )}
              {messageSuccess && (
                <div className={styles.messageSuccess}>
                  {messageSuccess}
                </div>
              )}
              <MessageEditor
                initialMessage={currentMessage}
                campaignId={id}
                onSave={handleUpdateMessage}
                onCancel={() => {
                  setShowEditMessageModal(false)
                  setCurrentMessage(null)
                }}
                onDelete={handleDeleteMessage}
                onSend={handleSendMessage}
                readOnly={currentMessage.status === 'sent'}
              />
            </div>
          </Modal>
        )}
      </div>
    </AdminLayout>
  )
}

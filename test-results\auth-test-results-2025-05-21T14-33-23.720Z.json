{"timestamp": "2025-05-21T14:33:22.538Z", "tests": {"API-01": {"name": "<PERSON><PERSON>", "status": "Pass", "details": {"statusCode": 200, "response": {"status": "success", "message": "Authentication successful", "user": {"id": "8c59a3bc-a96b-4555-bdc4-6abe905ae761", "email": "<EMAIL>", "role": "admin"}, "authorized": true, "authHeaders": {"authorization": true, "xAuthToken": false, "cookie": false}, "tokenFound": true, "tokenPreview": "eyJhbGciOi...", "requestId": "wqai05"}}}, "API-02": {"name": "Expired Token Test", "status": "Pass", "details": {"statusCode": 401, "response": {"error": "Unauthorized", "message": "Invalid authentication token"}}}, "API-03": {"name": "No Token Test", "status": "Pass", "details": {"statusCode": 401, "response": {"error": "Unauthorized", "message": "Invalid authentication token"}}}, "API-04": {"name": "<PERSON><PERSON> for Customer Data", "status": "Fail", "details": {"statusCode": 200, "responseType": "object"}}, "API-06": {"name": "<PERSON><PERSON> for Settings", "status": "Pass", "details": {"statusCode": 200, "responseType": "object"}}, "X-Auth-Token": {"name": "<PERSON>-<PERSON><PERSON>-<PERSON><PERSON> Test", "status": "Pass", "details": {"statusCode": 200, "response": {"status": "success", "message": "Authentication successful", "user": {"id": "8c59a3bc-a96b-4555-bdc4-6abe905ae761", "email": "<EMAIL>", "role": "admin"}, "authorized": true, "authHeaders": {"authorization": false, "xAuthToken": true, "cookie": false}, "tokenFound": true, "tokenPreview": "eyJhbGciOi...", "requestId": "5glwfu"}}}}}
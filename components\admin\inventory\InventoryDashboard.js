import { useState, useEffect } from 'react';
import { <PERSON>, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import Link from 'next/link';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/inventory/InventoryDashboard.module.css';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

/**
 * InventoryDashboard component for displaying inventory metrics and stock levels
 * 
 * @returns {JSX.Element}
 */
export default function InventoryDashboard() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [inventoryData, setInventoryData] = useState({
    totalProducts: 0,
    totalValue: 0,
    lowStockCount: 0,
    outOfStockCount: 0,
    categoryDistribution: [],
    stockLevels: [],
    recentMovements: []
  });

  // Fetch inventory data
  useEffect(() => {
    async function fetchInventoryData() {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('/api/admin/inventory/dashboard');
        
        if (!response.ok) {
          throw new Error('Failed to fetch inventory data');
        }
        
        const data = await response.json();
        setInventoryData(data);
      } catch (err) {
        console.error('Error fetching inventory data:', err);
        setError(err.message || 'Failed to load inventory data');
        toast.error('Failed to load inventory dashboard');
      } finally {
        setLoading(false);
      }
    }
    
    fetchInventoryData();
  }, []);

  // Prepare category distribution chart data
  const categoryChartData = {
    labels: inventoryData.categoryDistribution.map(item => item.name),
    datasets: [
      {
        data: inventoryData.categoryDistribution.map(item => item.count),
        backgroundColor: [
          '#4a90e2',
          '#50e3c2',
          '#f5a623',
          '#d0021b',
          '#9013fe',
          '#417505',
          '#bd10e0',
          '#8b572a'
        ],
        borderWidth: 1
      }
    ]
  };

  // Prepare stock levels chart data
  const stockLevelsChartData = {
    labels: inventoryData.stockLevels.map(item => item.name),
    datasets: [
      {
        label: 'Current Stock',
        data: inventoryData.stockLevels.map(item => item.stock),
        backgroundColor: inventoryData.stockLevels.map(item => 
          item.stock <= 0 ? '#d0021b' : 
          item.stock <= item.threshold ? '#f5a623' : 
          '#4a90e2'
        ),
        borderColor: 'rgba(0, 0, 0, 0.1)',
        borderWidth: 1
      }
    ]
  };

  // Chart options
  const doughnutOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15,
          font: {
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: 'Product Categories',
        font: {
          size: 16
        }
      }
    }
  };

  const barOptions = {
    responsive: true,
    indexAxis: 'y',
    plugins: {
      legend: {
        display: false
      },
      title: {
        display: true,
        text: 'Top Products by Stock Level',
        font: {
          size: 16
        }
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Stock Quantity'
        }
      }
    }
  };

  // Format currency
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };

  // Get stock level class
  const getStockLevelClass = (stock, threshold) => {
    if (stock <= 0) return styles.outOfStock;
    if (stock <= threshold) return styles.lowStock;
    return styles.inStock;
  };

  // Render loading state
  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading inventory data...</p>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={styles.errorContainer}>
        <p className={styles.errorMessage}>{error}</p>
        <button 
          className={styles.retryButton}
          onClick={() => window.location.reload()}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={styles.inventoryDashboard}>
      {/* Key Metrics */}
      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{inventoryData.totalProducts}</div>
          <div className={styles.metricLabel}>Total Products</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{formatCurrency(inventoryData.totalValue)}</div>
          <div className={styles.metricLabel}>Inventory Value</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{inventoryData.lowStockCount}</div>
          <div className={styles.metricLabel}>Low Stock Items</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{inventoryData.outOfStockCount}</div>
          <div className={styles.metricLabel}>Out of Stock</div>
        </div>
      </div>

      {/* Charts */}
      <div className={styles.chartsGrid}>
        <div className={styles.chartCard}>
          <div className={styles.chartContainer}>
            <Doughnut data={categoryChartData} options={doughnutOptions} />
          </div>
        </div>
        <div className={styles.chartCard}>
          <div className={styles.chartContainer}>
            <Bar data={stockLevelsChartData} options={barOptions} />
          </div>
        </div>
      </div>

      {/* Recent Stock Movements */}
      <div className={styles.movementsCard}>
        <h3>Recent Stock Movements</h3>
        {inventoryData.recentMovements.length > 0 ? (
          <div className={styles.movementsTable}>
            <table>
              <thead>
                <tr>
                  <th>Product</th>
                  <th>Type</th>
                  <th>Quantity</th>
                  <th>Date</th>
                  <th>User</th>
                </tr>
              </thead>
              <tbody>
                {inventoryData.recentMovements.map((movement) => (
                  <tr key={movement.id}>
                    <td>{movement.product_name}</td>
                    <td>
                      <span className={`${styles.movementType} ${styles[movement.type]}`}>
                        {movement.type}
                      </span>
                    </td>
                    <td className={movement.quantity > 0 ? styles.positive : styles.negative}>
                      {movement.quantity > 0 ? `+${movement.quantity}` : movement.quantity}
                    </td>
                    <td>{new Date(movement.created_at).toLocaleDateString()}</td>
                    <td>{movement.user_name}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className={styles.noData}>No recent stock movements</div>
        )}
        <div className={styles.viewAllLink}>
          <Link href="/admin/inventory/movements">
            View All Stock Movements
          </Link>
        </div>
      </div>

      {/* Low Stock Alerts */}
      <div className={styles.alertsCard}>
        <h3>Low Stock Alerts</h3>
        {inventoryData.stockLevels.filter(item => item.stock <= item.threshold && item.stock > 0).length > 0 ? (
          <div className={styles.alertsList}>
            {inventoryData.stockLevels
              .filter(item => item.stock <= item.threshold && item.stock > 0)
              .map((item) => (
                <div key={item.id} className={styles.alertItem}>
                  <div className={styles.alertProduct}>
                    <span className={styles.alertProductName}>{item.name}</span>
                    <span className={`${styles.stockBadge} ${styles.lowStock}`}>
                      {item.stock} left
                    </span>
                  </div>
                  <Link href={`/admin/inventory/products/${item.id}`}>
                    <a className={styles.viewProductButton}>View Product</a>
                  </Link>
                </div>
              ))}
          </div>
        ) : (
          <div className={styles.noData}>No low stock alerts</div>
        )}
      </div>
    </div>
  );
}

import handler from '@/pages/api/customers/export';
import { 
  createApiMocks, 
  mockAuthenticatedUser, 
  mockUnauthenticatedUser,
  mockUnauthorizedUser,
  mockSupabaseResponse,
  resetMocks
} from '../../utils/api-test-utils';

describe('API: /api/customers/export', () => {
  beforeEach(() => {
    resetMocks();
  });

  describe('Authentication', () => {
    it('should return 403 if user is not authenticated', async () => {
      // Mock unauthenticated user
      mockUnauthenticatedUser();

      // Create mock request and response
      const { req, res } = createApiMocks();

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(403);
      expect(res._getJSONData()).toEqual({ error: 'Authorization failed' });
    });

    it('should return 403 if user is not an admin', async () => {
      // Mock authenticated staff user (non-admin)
      mockAuthenticatedUser({ id: 'staff-user' }, 'staff');

      // Create mock request and response
      const { req, res } = createApiMocks();

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(403);
      expect(res._getJSONData()).toEqual({ error: 'Forbidden' });
    });

    it('should allow authenticated admin users', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock Supabase response
      mockSupabaseResponse({ data: [] });

      // Create mock request and response
      const { req, res } = createApiMocks();

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(200);
    });
  });

  describe('GET /api/customers/export', () => {
    it('should export customers as CSV by default', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock customer data
      const mockCustomers = [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '0412345678',
          city: 'Sydney',
          state: 'NSW',
          country: 'Australia',
          created_at: '2023-01-01T00:00:00Z'
        },
        {
          name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '0487654321',
          city: 'Melbourne',
          state: 'VIC',
          country: 'Australia',
          created_at: '2023-01-02T00:00:00Z'
        }
      ];

      // Mock Supabase response
      mockSupabaseResponse({ data: mockCustomers });

      // Create mock request and response
      const { req, res } = createApiMocks();

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(200);
      expect(res._getHeaders()['content-type']).toBe('text/csv');
      expect(res._getHeaders()['content-disposition']).toBe('attachment; filename=customers.csv');
      
      // Check CSV content
      const csvContent = res._getData();
      expect(csvContent).toContain('name,email,phone,city,state,country,created_at');
      expect(csvContent).toContain('John Doe,<EMAIL>,0412345678,Sydney,NSW,Australia,2023-01-01T00:00:00Z');
      expect(csvContent).toContain('Jane Smith,<EMAIL>,0487654321,Melbourne,VIC,Australia,2023-01-02T00:00:00Z');
    });

    it('should export customers as JSON when format=json', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock customer data
      const mockCustomers = [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '0412345678',
          city: 'Sydney',
          state: 'NSW',
          country: 'Australia',
          created_at: '2023-01-01T00:00:00Z'
        },
        {
          name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '0487654321',
          city: 'Melbourne',
          state: 'VIC',
          country: 'Australia',
          created_at: '2023-01-02T00:00:00Z'
        }
      ];

      // Mock Supabase response
      mockSupabaseResponse({ data: mockCustomers });

      // Create mock request with format parameter
      const { req, res } = createApiMocks({
        query: { format: 'json' }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(200);
      expect(res._getJSONData()).toEqual(mockCustomers);
    });

    it('should filter by marketing consent when marketing_only=true', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock Supabase response
      const supabase = mockSupabaseResponse({ data: [] });

      // Create mock request with marketing_only parameter
      const { req, res } = createApiMocks({
        query: { marketing_only: 'true' }
      });

      // Call the API handler
      await handler(req, res);

      // Check that the eq method was called with marketing_consent=true
      expect(supabase.eq).toHaveBeenCalledWith('marketing_consent', true);
    });

    it('should handle database errors', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock Supabase error
      mockSupabaseResponse({ error: { message: 'Database error' } });

      // Create mock request and response
      const { req, res } = createApiMocks();

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(500);
      expect(res._getJSONData()).toEqual({ error: 'Failed to export customers' });
    });
  });

  describe('Method Not Allowed', () => {
    it('should return 405 for unsupported methods', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Create mock request with unsupported method
      const { req, res } = createApiMocks({
        method: 'POST'
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(405);
      expect(res._getJSONData()).toEqual({ error: 'Method not allowed' });
    });
  });
});

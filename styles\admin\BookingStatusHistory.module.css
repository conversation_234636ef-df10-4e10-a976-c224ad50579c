.historyContainer {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
}

.historyTitle {
  font-size: 1.1rem;
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.emptyState {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
}

.historyList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.historyItem {
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 8px;
}

.historyItem:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.historyHeader {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.85rem;
}

.historyDate {
  color: #6c757d;
}

.historyUser {
  color: #495057;
  font-weight: 500;
}

.statusChange {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
}

.arrow {
  margin: 0 8px;
  color: #6c757d;
}

.notes {
  font-size: 0.9rem;
  color: #495057;
  margin-top: 8px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  border-left: 3px solid #6c757d;
}

/* Status colors */
.pending {
  background-color: #fff3cd;
  color: #856404;
}

.confirmed {
  background-color: #d4edda;
  color: #155724;
}

.in_progress {
  background-color: #cce5ff;
  color: #004085;
}

.completed {
  background-color: #d1e7dd;
  color: #0f5132;
}

.canceled {
  background-color: #f8d7da;
  color: #721c24;
}

.no_show {
  background-color: #e2e3e5;
  color: #383d41;
}

.rescheduled {
  background-color: #e0cffc;
  color: #5a2c82;
}

.none {
  background-color: #e9ecef;
  color: #495057;
}

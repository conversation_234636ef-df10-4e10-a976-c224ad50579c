/**
 * Integration tests for Customer Export API
 * 
 * These tests use a real Supabase test database instead of mocks.
 * They require proper environment variables in .env.test
 */

import {
  createTestUser,
  createTestCustomer,
  signInTestUser,
  cleanupTestData,
  makeAuthenticatedRequest
} from '../utils/api-test-helpers';

// Test data to clean up
const testData = {
  customerIds: [],
  userIds: []
};

// Base URL for API requests
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

describe('Customer Export API Integration Tests', () => {
  let adminUser;
  let adminSession;
  let staffUser;
  let staffSession;
  
  // Set up test users and customers before all tests
  beforeAll(async () => {
    // Create admin user
    adminUser = await createTestUser('admin');
    testData.userIds.push(adminUser.id);
    
    // Create staff user
    staffUser = await createTestUser('staff');
    testData.userIds.push(staffUser.id);
    
    // Sign in as admin
    const adminAuth = await signInTestUser({
      email: adminUser.email,
      password: adminUser.password
    });
    adminSession = adminAuth.session;
    
    // Sign in as staff
    const staffAuth = await signInTestUser({
      email: staffUser.email,
      password: staffUser.password
    });
    staffSession = staffAuth.session;
    
    // Create test customers
    // Customer with marketing consent
    const customer1 = await createTestCustomer({
      name: 'Export Test Customer 1',
      email: `export-test-1-${Date.now()}@example.com`,
      marketing_consent: true
    });
    testData.customerIds.push(customer1.id);
    
    // Customer without marketing consent
    const customer2 = await createTestCustomer({
      name: 'Export Test Customer 2',
      email: `export-test-2-${Date.now()}@example.com`,
      marketing_consent: false
    });
    testData.customerIds.push(customer2.id);
  }, 30000); // Increase timeout for setup
  
  // Clean up test data after all tests
  afterAll(async () => {
    await cleanupTestData(testData);
  }, 10000);
  
  describe('GET /api/customers/export', () => {
    it('should allow admin users to export customers as CSV', async () => {
      // Make request to export customers
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/export`,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toContain('text/csv');
      expect(response.headers['content-disposition']).toContain('attachment; filename=customers.csv');
      
      // Check CSV content
      const csvContent = response.data;
      expect(typeof csvContent).toBe('string');
      expect(csvContent).toContain('name,email,phone,city,state,postal_code,country,created_at');
      expect(csvContent).toContain('Export Test Customer 1');
    });
    
    it('should allow admin users to export customers as JSON', async () => {
      // Make request to export customers as JSON
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/export?format=json`,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data.length).toBeGreaterThan(0);
      
      // Check that exported data contains our test customers
      const testCustomer = response.data.find(c => c.name === 'Export Test Customer 1');
      expect(testCustomer).toBeDefined();
      expect(testCustomer.email).toContain('export-test-1');
    });
    
    it('should filter by marketing consent when requested', async () => {
      // Make request to export only customers with marketing consent
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/export?marketing_only=true&format=json`,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      
      // Check that all exported customers have marketing consent
      const consentingCustomer = response.data.find(c => c.name === 'Export Test Customer 1');
      const nonConsentingCustomer = response.data.find(c => c.name === 'Export Test Customer 2');
      
      expect(consentingCustomer).toBeDefined();
      expect(nonConsentingCustomer).toBeUndefined();
    });
    
    it('should prevent staff users from exporting customers', async () => {
      // Make request as staff user
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/export`,
        session: staffSession
      });
      
      // Check response
      expect(response.status).toBe(403);
      expect(response.data.error).toBeDefined();
    });
    
    it('should reject unsupported HTTP methods', async () => {
      // Make POST request
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/export`,
        method: 'POST',
        body: {},
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(405);
      expect(response.data.error).toBeDefined();
    });
  });
});

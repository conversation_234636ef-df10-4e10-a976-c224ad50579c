/**
 * OneSignal Safe Initialization Script
 * 
 * This script provides a safer way to initialize OneSignal with proper error handling
 * and fallbacks to prevent errors from breaking the application.
 */

(function() {
  // Only run in browser environment
  if (typeof window === 'undefined') return;

  // Configuration
  const ONESIGNAL_APP_ID = window.__ONESIGNAL_APP_ID__ || "************************************";
  const ONESIGNAL_SAFARI_WEB_ID = window.__ONESIGNAL_SAFARI_WEB_ID__ || "web.onesignal.auto.************************************";
  const DEBUG = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  // Create a safe version of OneSignal without modifying prototypes
  function createSafeOneSignal() {
    try {
      // Initialize OneSignal array if it doesn't exist
      if (typeof window.OneSignal === 'undefined') {
        window.OneSignal = [];
      }
      
      // If OneSignal is already an array with push method, enhance it safely
      if (Array.isArray(window.OneSignal) && !window.OneSignal._safePushEnabled) {
        const originalPush = window.OneSignal.push;
        
        // Create a safer push method that won't throw errors
        window.OneSignal.push = function() {
          try {
            // Call the original push method with proper context
            return originalPush.apply(window.OneSignal, arguments);
          } catch (error) {
            console.error('[OneSignal Safe] Error in OneSignal.push:', error);
            // Return a resolved promise to prevent errors from propagating
            return Promise.resolve();
          }
        };
        
        // Mark as enhanced to avoid double-enhancement
        window.OneSignal._safePushEnabled = true;
      }
      
      console.log('[OneSignal Safe] Created safe OneSignal object');
      return true;
    } catch (error) {
      console.error('[OneSignal Safe] Failed to create safe OneSignal object:', error);
      return false;
    }
  }

  // Initialize OneSignal safely
  function initializeOneSignal() {
    try {
      // Check if OneSignal is already initialized
      if (window.__ONESIGNAL_INITIALIZED__) {
        console.log('[OneSignal Safe] Already initialized, skipping');
        return;
      }
      
      // Create safe OneSignal object
      if (!createSafeOneSignal()) {
        console.error('[OneSignal Safe] Aborting initialization due to errors');
        return;
      }
      
      // Set initialization flag
      window.__ONESIGNAL_INITIALIZING__ = true;
      
      // Initialize OneSignal with safe push and error boundaries
      window.OneSignal.push(function() {
        try {
          // Add additional safety check before initialization
          if (typeof window.OneSignal.init !== 'function') {
            console.error('[OneSignal Safe] OneSignal.init is not available');
            return;
          }
          
          const initPromise = window.OneSignal.init({
            appId: ONESIGNAL_APP_ID,
            safari_web_id: ONESIGNAL_SAFARI_WEB_ID,
            allowLocalhostAsSecureOrigin: DEBUG,
            notifyButton: {
              enable: true,
              size: 'medium',
              theme: 'default',
              position: 'bottom-right',
              offset: {
                bottom: '20px',
                right: '20px',
              },
              prenotify: true,
              showCredit: false,
              text: {
                'tip.state.unsubscribed': 'Subscribe to notifications',
                'tip.state.subscribed': 'You\'re subscribed to notifications',
                'tip.state.blocked': 'You\'ve blocked notifications',
                'message.prenotify': 'Click to subscribe to notifications',
                'message.action.subscribed': 'Thanks for subscribing!',
                'message.action.resubscribed': 'You\'re subscribed to notifications',
                'message.action.unsubscribed': 'You won\'t receive notifications again',
                'dialog.main.title': 'Manage Site Notifications',
                'dialog.main.button.subscribe': 'SUBSCRIBE',
                'dialog.main.button.unsubscribe': 'UNSUBSCRIBE',
                'dialog.blocked.title': 'Unblock Notifications',
                'dialog.blocked.message': 'Follow these instructions to allow notifications:'
              }
            },
            promptOptions: {
              slidedown: {
                prompts: [
                  {
                    type: "push",
                    autoPrompt: false,
                    text: {
                      actionMessage: "Would you like to receive notifications about your bookings and special offers?",
                      acceptButton: "Yes",
                      cancelButton: "No Thanks"
                    },
                    delay: {
                      pageViews: 1,
                      timeDelay: 20
                    }
                  }
                ]
              }
            }
          });
          
          // Handle both promise-based and callback-based initialization
          if (initPromise && typeof initPromise.then === 'function') {
            initPromise.then(function() {
              console.log('[OneSignal Safe] Initialization successful');
              window.__ONESIGNAL_INITIALIZED__ = true;
              window.__ONESIGNAL_INITIALIZING__ = false;
              
              // Dispatch a custom event that React components can listen for
              try {
                const event = new CustomEvent('onesignal:initialized');
                document.dispatchEvent(event);
              } catch (eventError) {
                console.error('[OneSignal Safe] Error dispatching initialization event:', eventError);
              }
            }).catch(function(error) {
              console.error('[OneSignal Safe] Initialization promise rejected:', error);
              window.__ONESIGNAL_INITIALIZING__ = false;
            });
          } else {
            // Fallback for non-promise initialization
            setTimeout(function() {
              console.log('[OneSignal Safe] Initialization completed (fallback)');
              window.__ONESIGNAL_INITIALIZED__ = true;
              window.__ONESIGNAL_INITIALIZING__ = false;
              
              try {
                const event = new CustomEvent('onesignal:initialized');
                document.dispatchEvent(event);
              } catch (eventError) {
                console.error('[OneSignal Safe] Error dispatching initialization event:', eventError);
              }
            }, 1000);
          }
        } catch (initError) {
          console.error('[OneSignal Safe] Error during init call:', initError);
          window.__ONESIGNAL_INITIALIZING__ = false;
        }
      });
    } catch (error) {
      console.error('[OneSignal Safe] Critical error in initialization:', error);
      window.__ONESIGNAL_INITIALIZING__ = false;
    }
  }

  // Check if OneSignal SDK is loaded
  function isOneSignalSDKLoaded() {
    return typeof window !== 'undefined' && 
           window.OneSignal && 
           typeof window.OneSignal === 'object';
  }

  // Wait for OneSignal SDK to be loaded
  function waitForOneSignalSDK(callback, maxAttempts = 10) {
    let attempts = 0;
    
    function checkSDK() {
      attempts++;
      
      if (isOneSignalSDKLoaded()) {
        callback();
      } else if (attempts < maxAttempts) {
        setTimeout(checkSDK, 500);
      } else {
        console.error('[OneSignal Safe] OneSignal SDK failed to load after', maxAttempts, 'attempts');
      }
    }
    
    checkSDK();
  }

  // Start initialization with a slight delay to ensure DOM is ready
  setTimeout(function() {
    waitForOneSignalSDK(initializeOneSignal);
  }, 1000);
})();

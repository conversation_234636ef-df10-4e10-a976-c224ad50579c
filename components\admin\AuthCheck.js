import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import apiClient from '@/lib/api-client';
import styles from '@/styles/admin/AuthTest.module.css';

export default function AuthCheck() {
  const { user, role, loading, isAdmin, isAuthenticated } = useAuth();
  const [testResults, setTestResults] = useState({
    tokenCheck: null,
    apiCheck: null,
    sessionCheck: null
  });
  const [isRunning, setIsRunning] = useState(false);
  const [error, setError] = useState(null);
  const [tokenInfo, setTokenInfo] = useState(null);

  // Check for token in sessionStorage on component mount
  useEffect(() => {
    checkTokenInfo();
  }, []);

  // Function to check token info
  const checkTokenInfo = () => {
    if (typeof window !== 'undefined' && window.sessionStorage) {
      try {
        const cachedToken = sessionStorage.getItem('oss_auth_token_cache');
        if (cachedToken) {
          try {
            const tokenData = JSON.parse(cachedToken);
            if (tokenData && tokenData.token) {
              // Extract token info
              const tokenParts = tokenData.token.split('.');
              if (tokenParts.length === 3) {
                try {
                  const payload = JSON.parse(atob(tokenParts[1]));
                  setTokenInfo({
                    token: tokenData.token.substring(0, 10) + '...',
                    expiry: new Date(tokenData.expiry).toLocaleString(),
                    expiryTimestamp: tokenData.expiry,
                    isExpired: tokenData.expiry < Date.now(),
                    refreshed: new Date(tokenData.refreshed).toLocaleString(),
                    payload: payload
                  });
                } catch (e) {
                  setTokenInfo({
                    token: tokenData.token.substring(0, 10) + '...',
                    expiry: new Date(tokenData.expiry).toLocaleString(),
                    expiryTimestamp: tokenData.expiry,
                    isExpired: tokenData.expiry < Date.now(),
                    refreshed: new Date(tokenData.refreshed).toLocaleString(),
                    error: 'Failed to parse token payload'
                  });
                }
              } else {
                setTokenInfo({
                  token: tokenData.token.substring(0, 10) + '...',
                  expiry: new Date(tokenData.expiry).toLocaleString(),
                  expiryTimestamp: tokenData.expiry,
                  isExpired: tokenData.expiry < Date.now(),
                  refreshed: new Date(tokenData.refreshed).toLocaleString(),
                  error: 'Invalid token format'
                });
              }
            }
          } catch (parseError) {
            setTokenInfo({
              error: `Error parsing token from sessionStorage: ${parseError.message}`
            });
          }
        } else {
          setTokenInfo({
            error: 'No token found in sessionStorage'
          });
        }
      } catch (storageError) {
        setTokenInfo({
          error: `Error accessing sessionStorage: ${storageError.message}`
        });
      }
    }
  };

  // Function to refresh the token
  const refreshToken = async () => {
    setIsRunning(true);
    setError(null);

    try {
      const token = await apiClient.refreshAuthToken();
      if (token) {
        // Update token info
        checkTokenInfo();
        setError(null);
      } else {
        setError('Failed to refresh token');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsRunning(false);
    }
  };

  const runAuthCheck = async () => {
    setIsRunning(true);
    setError(null);
    setTestResults({
      tokenCheck: null,
      apiCheck: null,
      sessionCheck: null
    });

    try {
      // Update token info
      checkTokenInfo();

      // Test 1: Check if we can get a token
      let token = null;
      try {
        token = await apiClient.getAuthToken();
        setTestResults(prev => ({
          ...prev,
          tokenCheck: {
            success: !!token,
            message: token ? 'Successfully retrieved token' : 'No token available'
          }
        }));
      } catch (tokenError) {
        setTestResults(prev => ({
          ...prev,
          tokenCheck: {
            success: false,
            error: tokenError.message,
            message: 'Failed to retrieve token'
          }
        }));
      }

      // Test 2: Check if we can make an authenticated API request
      try {
        const response = await fetch('/api/admin/diagnostics/auth-check', {
          headers: {
            'Authorization': token ? `Bearer ${token}` : ''
          }
        });

        const data = await response.json();

        setTestResults(prev => ({
          ...prev,
          apiCheck: {
            success: response.ok,
            status: response.status,
            data,
            message: response.ok ? 'API authentication successful' : 'API authentication failed'
          }
        }));
      } catch (apiError) {
        setTestResults(prev => ({
          ...prev,
          apiCheck: {
            success: false,
            error: apiError.message,
            message: 'API request failed'
          }
        }));
      }

      // Test 3: Check session state
      setTestResults(prev => ({
        ...prev,
        sessionCheck: {
          success: isAuthenticated,
          user,
          role,
          message: isAuthenticated ? 'User is authenticated' : 'User is not authenticated'
        }
      }));
    } catch (error) {
      setError(error.message);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className={styles.authTest}>
      <h2>Authentication Diagnostics</h2>

      <div className={styles.testResults}>
        <h3>Current Auth Status</h3>
        <div className={styles.testResult}>
          <h4>Authentication State</h4>
          <div className={isAuthenticated ? styles.success : styles.error}>
            <p className={styles.resultMessage}>
              <strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}
              {loading && ' (Loading...)'}
            </p>
          </div>

          {user && (
            <div style={{marginTop: '10px'}}>
              <p>
                <strong>User:</strong> {user.email}
                <br />
                <strong>Role:</strong> {role || 'Unknown'}
              </p>
            </div>
          )}
        </div>
      </div>

      <div className={styles.testControls}>
        <button
          onClick={runAuthCheck}
          disabled={isRunning}
          className={styles.testButton}
        >
          {isRunning ? 'Running Tests...' : 'Run Authentication Tests'}
        </button>

        <button
          onClick={refreshToken}
          disabled={isRunning}
          className={styles.testButton}
          style={{ marginLeft: '10px' }}
        >
          {isRunning ? 'Refreshing...' : 'Refresh Token'}
        </button>
      </div>

      {tokenInfo && (
        <div className={styles.testResults} style={{ marginTop: '20px' }}>
          <h3>Token Information</h3>
          <div className={styles.testResult}>
            {tokenInfo.error ? (
              <div className={styles.error}>
                <p className={styles.resultMessage}>{tokenInfo.error}</p>
              </div>
            ) : (
              <>
                <h4>Token Details</h4>
                <div className={tokenInfo.isExpired ? styles.error : styles.success}>
                  <p className={styles.resultMessage}>
                    <strong>Status:</strong> {tokenInfo.isExpired ? 'Expired' : 'Valid'}
                  </p>
                </div>

                <div style={{marginTop: '10px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px', overflow: 'auto'}}>
                  <p><strong>Token:</strong> {tokenInfo.token}</p>
                  <p><strong>Expiry:</strong> {tokenInfo.expiry}</p>
                  <p><strong>Last Refreshed:</strong> {tokenInfo.refreshed}</p>
                  {tokenInfo.payload && (
                    <>
                      <p><strong>User ID:</strong> {tokenInfo.payload.sub}</p>
                      <p><strong>Issued At:</strong> {new Date(tokenInfo.payload.iat * 1000).toLocaleString()}</p>
                      <p><strong>Expires At:</strong> {new Date(tokenInfo.payload.exp * 1000).toLocaleString()}</p>
                    </>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {error && (
        <div className={styles.testResults}>
          <h3>Error</h3>
          <div className={styles.testResult}>
            <div className={styles.error}>
              <p className={styles.resultMessage}>{error}</p>
            </div>
          </div>
        </div>
      )}

      {(testResults.tokenCheck || testResults.apiCheck || testResults.sessionCheck) && (
        <div className={styles.testResults}>
          <h3>Test Results</h3>

          {testResults.tokenCheck && (
            <div className={styles.testResult}>
              <h4>Token Check</h4>
              <div className={testResults.tokenCheck.success ? styles.success : styles.error}>
                <p className={styles.resultMessage}>
                  {testResults.tokenCheck.message}
                </p>
              </div>
              {testResults.tokenCheck.error && (
                <div style={{marginTop: '10px'}} className={styles.error}>
                  <p className={styles.resultMessage}>{testResults.tokenCheck.error}</p>
                </div>
              )}
            </div>
          )}

          {testResults.apiCheck && (
            <div className={styles.testResult}>
              <h4>API Authentication Check</h4>
              <div className={testResults.apiCheck.success ? styles.success : styles.error}>
                <p className={styles.resultMessage}>
                  {testResults.apiCheck.message}
                  {testResults.apiCheck.status && ` (Status: ${testResults.apiCheck.status})`}
                </p>
              </div>

              {testResults.apiCheck.error && (
                <div style={{marginTop: '10px'}} className={styles.error}>
                  <p className={styles.resultMessage}>{testResults.apiCheck.error}</p>
                </div>
              )}

              {testResults.apiCheck.data && (
                <div style={{marginTop: '10px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px', overflow: 'auto'}}>
                  <pre style={{margin: 0, fontSize: '0.85rem'}}>
                    {JSON.stringify(testResults.apiCheck.data, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          )}

          {testResults.sessionCheck && (
            <div className={styles.testResult}>
              <h4>Session Check</h4>
              <div className={testResults.sessionCheck.success ? styles.success : styles.error}>
                <p className={styles.resultMessage}>
                  {testResults.sessionCheck.message}
                </p>
              </div>

              {testResults.sessionCheck.user && (
                <div style={{marginTop: '10px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px', overflow: 'auto'}}>
                  <pre style={{margin: 0, fontSize: '0.85rem'}}>
                    {JSON.stringify({
                      id: testResults.sessionCheck.user.id,
                      email: testResults.sessionCheck.user.email,
                      role: testResults.sessionCheck.role
                    }, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

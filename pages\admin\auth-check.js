import { useState } from 'react';
import Head from 'next/head';
import AdminLayout from '@/components/admin/AdminLayout';
import AuthCheck from '@/components/admin/AuthCheck';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import styles from '@/styles/admin/AdminPage.module.css';

export default function AuthCheckPage() {
  return (
    <ProtectedRoute>
      <AdminLayout title="Authentication Check">
        <Head>
          <title>Authentication Check | OceanSoulSparkles Admin</title>
        </Head>
        <div className={styles.container}>
          <h1 className={styles.title}>Authentication Check</h1>
          <p className={styles.description}>
            Use this page to diagnose authentication issues with the admin panel.
          </p>
          
          <AuthCheck />
          
          <div className={styles.infoBox}>
            <h2>Authentication Troubleshooting</h2>
            <p>
              If you're experiencing authentication issues, try the following:
            </p>
            <ol>
              <li>Sign out and sign back in</li>
              <li>Clear your browser cache and cookies</li>
              <li>Try using a different browser</li>
              <li>Check if your account has the correct admin role</li>
            </ol>
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}

-- Fix customers table missing columns
-- Use this script in the Supabase SQL Editor

-- Step 1: Check if customers table exists and create it if not
CREATE TABLE IF NOT EXISTS customers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone TEXT NOT NULL,
  address TEXT,
  city TEXT,
  state TEXT,
  postal_code TEXT,
  country TEXT DEFAULT 'Australia',
  marketing_consent BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  auth_id UUID,
  is_guest BOOLEAN DEFAULT TRUE
);

-- Step 2: Add auth_id column if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT FROM information_schema.columns 
               WHERE table_name='customers' AND column_name='auth_id') THEN
    ALTER TABLE customers ADD COLUMN auth_id UUID;
  END IF;
END $$;

-- Step 3: Add is_guest column if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT FROM information_schema.columns 
               WHERE table_name='customers' AND column_name='is_guest') THEN
    ALTER TABLE customers ADD COLUMN is_guest BOOLEAN DEFAULT TRUE;
  END IF;
END $$;

-- Step 4: Create indexes for performance
CREATE INDEX IF NOT EXISTS customers_email_idx ON customers(email);
CREATE INDEX IF NOT EXISTS customers_auth_id_idx ON customers(auth_id);

-- Step 5: Set up Row Level Security (RLS)
-- First, enable RLS on the customers table
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;

-- Clear any existing policies to start fresh
DROP POLICY IF EXISTS customer_select_own ON customers;
DROP POLICY IF EXISTS customer_update_own ON customers;
DROP POLICY IF EXISTS customer_all_service_role ON customers;
DROP POLICY IF EXISTS customer_anon_select ON customers;
DROP POLICY IF EXISTS customer_public_select ON customers;
DROP POLICY IF EXISTS customer_public_update ON customers;
DROP POLICY IF EXISTS customer_public_insert ON customers;
DROP POLICY IF EXISTS customer_anon_access ON customers;

-- Create simplified policies that don't depend on auth_id
-- Allow authenticated users to read all customers
CREATE POLICY customer_read_all ON customers 
  FOR SELECT TO authenticated 
  USING (true);

-- Allow authenticated users to update all customers
CREATE POLICY customer_update_all ON customers 
  FOR UPDATE TO authenticated 
  USING (true);

-- Allow anonymous users to read customers
CREATE POLICY customer_anon_read ON customers 
  FOR SELECT TO anon 
  USING (true);

-- Allow anyone to insert customers
CREATE POLICY customer_insert_all ON customers 
  FOR INSERT TO authenticated, anon 
  WITH CHECK (true);

-- Step 6: Check that everything worked
SELECT 
  column_name, 
  data_type 
FROM 
  information_schema.columns 
WHERE 
  table_name = 'customers';

SELECT * FROM pg_policies WHERE tablename = 'customers';

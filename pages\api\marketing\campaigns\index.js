import { getAdminClient, getCurrentUserFromRequest, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUserFromRequest(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getCampaigns(req, res)
    case 'POST':
      return createCampaign(req, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get marketing campaigns with optional filters
async function getCampaigns(req, res) {
  const {
    search,
    status,
    campaign_type,
    sort_by = 'created_at',
    sort_order = 'desc',
    limit = 10,
    offset = 0
  } = req.query

  try {
    const client = getClient();
    if (!client) {
      console.error("Client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    let query = client
      .from('marketing_campaigns')
      .select(`
        *,
        target_segment:customer_segments (id, name)
      `, { count: 'exact' })

    // Apply search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
    }

    // Apply status filter
    if (status) {
      query = query.eq('status', status)
    }

    // Apply campaign type filter
    if (campaign_type) {
      query = query.eq('campaign_type', campaign_type)
    }

    // Apply sorting
    query = query.order(sort_by, { ascending: sort_order === 'asc' })

    // Apply pagination
    if (limit) {
      query = query.limit(limit)
    }
    if (offset) {
      query = query.offset(offset)
    }

    // Execute query
    const { data, error, count } = await query

    if (error) {
      throw error
    }

    // Get metrics for each campaign
    const campaignsWithMetrics = await Promise.all(
      data.map(async (campaign) => {
        const metrics = await getCampaignMetrics(campaign.id)
        return {
          ...campaign,
          metrics
        }
      })
    )

    return res.status(200).json({
      campaigns: campaignsWithMetrics,
      total: count
    })
  } catch (error) {
    console.error('Error fetching campaigns:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Create a new marketing campaign
async function createCampaign(req, res) {
  const {
    name,
    description,
    start_date,
    end_date,
    status,
    campaign_type,
    target_segment
  } = req.body

  try {
    // Get current user
    const { user } = await getCurrentUserFromRequest(req)

    // Validate required fields
    if (!name || !start_date || !status || !campaign_type) {
      return res.status(400).json({ error: 'Name, start date, status, and campaign type are required' })
    }

    // Create campaign
    const { data, error } = await supabase
      .from('marketing_campaigns')
      .insert([
        {
          name,
          description,
          start_date,
          end_date,
          status,
          campaign_type,
          target_segment,
          created_by: user.id
        }
      ])
      .select()

    if (error) {
      throw error
    }

    return res.status(201).json(data[0])
  } catch (error) {
    console.error('Error creating campaign:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Helper function to get campaign metrics
async function getCampaignMetrics(campaignId) {
  try {
    const { data, error } = await supabase
      .from('campaign_metrics')
      .select('metric_type, metric_value')
      .eq('campaign_id', campaignId)

    if (error) {
      console.error(`Error fetching metrics for campaign ${campaignId}:`, error)
      return {}
    }

    // Group metrics by type
    const metrics = {}
    data.forEach(metric => {
      metrics[metric.metric_type] = (metrics[metric.metric_type] || 0) + metric.metric_value
    })

    return metrics
  } catch (error) {
    console.error(`Error processing metrics for campaign ${campaignId}:`, error)
    return {}
  }
}

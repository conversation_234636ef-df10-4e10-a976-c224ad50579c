.main {
  width: 100%;
  margin: 0 auto;
  background-image: url('/background.png'); /* Added pink cloud background */
  background-size: cover; /* Ensure background covers the area */
  background-position: center; /* Center the background image */
  background-attachment: fixed; /* Optional: Keep background fixed during scroll */
}

/* Hero section */
.hero {
  text-align: center;
  padding: 5rem 2rem;
  background-color: var(--background-color);
  background-image: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)), url('/images/shop-hero.jpg');
  background-size: cover;
  background-position: center;
  margin-bottom: 3rem;
  border-bottom: 1px solid var(--border-color);
}

.title {
  font-size: 3rem;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.description {
  font-size: 1.2rem;
  color: var(--light-text-color);
  max-width: 700px;
  margin: 0 auto;
}

/* Shop section */
.shopSection {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  max-width: var(--max-width);
  margin: 0 auto 4rem;
  padding: 0 2rem;
}

.categoryNav {
  flex: 1;
  min-width: 200px;
  max-width: 250px;
}

.categoryNav h2 {
  font-size: 1.5rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.categoryList {
  list-style: none;
  padding: 0;
}

.categoryButton {
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  background: none;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
}

.categoryButton:hover {
  background-color: #f0f0f0;
  color: var(--primary-color);
}

.categoryButton.active {
  background-color: var(--primary-color);
  color: white;
}

.productsGrid {
  flex: 3;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
}

.productCard {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.productCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.productImage {
  height: 200px;
  overflow: hidden;
}

.imagePlaceholder {
  width: 100%;
  height: 100%;
  background-color: var(--primary-color);
  opacity: 0.7;
}

.productImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.productImg:hover {
  transform: scale(1.05);
}

.productInfo {
  padding: 1.5rem;
}

.productInfo h3 {
  font-size: 1.2rem;
  color: var(--text-color);
  margin-bottom: 0.75rem;
  min-height: 2.8em;
}

.productDescription {
  color: var(--light-text-color);
  margin-bottom: 1rem;
  font-size: 0.9rem;
  line-height: 1.5;
  min-height: 4em;
}

.productPrice {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.addToCartButton {
  width: 100%;
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.addToCartButton:hover {
  background-color: var(--secondary-color);
}

/* Eco-friendly section */
.ecoFriendly {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 4rem 2rem;
  background-color: #f8f8f8;
  margin-bottom: 4rem;
}

.ecoContent {
  flex: 1;
  min-width: 300px;
  padding-right: 2rem;
}

.ecoContent h2 {
  font-size: 1.8rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.ecoContent p {
  color: var(--light-text-color);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.ecoImage {
  flex: 1;
  min-width: 300px;
  height: 400px;
  overflow: hidden;
  border-radius: 8px;
}

.ecoImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Shipping info section */
.shippingInfo {
  max-width: var(--max-width);
  margin: 0 auto 4rem;
  padding: 0 2rem;
}

.shippingInfo h2 {
  text-align: center;
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 2rem;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.infoCard {
  background-color: #f8f8f8;
  padding: 2rem;
  border-radius: 8px;
}

.infoCard h3 {
  font-size: 1.3rem;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.infoCard p {
  color: var(--light-text-color);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.infoCard ul {
  padding-left: 1.5rem;
  color: var(--light-text-color);
}

.infoCard li {
  margin-bottom: 0.5rem;
}

.policyLink {
  display: inline-block;
  margin-top: 1rem;
  color: var(--primary-color);
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

.policyLink:hover {
  color: var(--secondary-color);
}

.policyLink::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #4ECDC4, #FF6B6B);
  transition: width 0.3s ease;
}

.policyLink:hover::after {
  width: 100%;
}

/* Custom orders section */
.customOrders {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 4rem;
  padding: 0 2rem;
}

.customOrders h2 {
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.customOrders p {
  color: var(--light-text-color);
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.button {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 2rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.button:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

/* Responsive styles */
@media (max-width: 992px) {
  .shopSection {
    flex-direction: column;
  }

  .categoryNav {
    max-width: 100%;
  }

  .categoryList {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .categoryButton {
    width: auto;
    margin-bottom: 0;
  }

  .ecoFriendly {
    flex-direction: column;
  }

  .ecoContent {
    padding-right: 0;
    margin-bottom: 2rem;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }

  .hero {
    padding: 4rem 1rem;
  }

  .shopSection, .shippingInfo, .customOrders {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .ecoFriendly {
    padding: 3rem 1rem;
  }

  .productsGrid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
  }

  .productsGrid {
    grid-template-columns: 1fr;
  }

  .infoGrid {
    grid-template-columns: 1fr;
  }
}

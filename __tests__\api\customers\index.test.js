import handler from '@/pages/api/customers/index';
import { 
  createApiMocks, 
  mockAuthenticatedUser, 
  mockUnauthenticatedUser,
  mockSupabaseResponse,
  resetMocks
} from '../../utils/api-test-utils';

describe('API: /api/customers', () => {
  beforeEach(() => {
    resetMocks();
  });

  describe('Authentication', () => {
    it('should return 401 if user is not authenticated', async () => {
      // Mock unauthenticated user
      mockUnauthenticatedUser();

      // Create mock request and response
      const { req, res } = createApiMocks();

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(401);
      expect(res._getJSONData()).toEqual({ error: 'Authentication failed' });
    });

    it('should allow authenticated admin users', async () => {
      // Mock authenticated admin user
      mockAuthenticatedUser({ id: 'admin-user' }, 'admin');

      // Mock Supabase response
      mockSupabaseResponse({ data: [], count: 0 });

      // Create mock request and response
      const { req, res } = createApiMocks();

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(200);
    });

    it('should allow authenticated staff users', async () => {
      // Mock authenticated staff user
      mockAuthenticatedUser({ id: 'staff-user' }, 'staff');

      // Mock Supabase response
      mockSupabaseResponse({ data: [], count: 0 });

      // Create mock request and response
      const { req, res } = createApiMocks();

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(200);
    });
  });

  describe('GET /api/customers', () => {
    it('should return a list of customers', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock customer data
      const mockCustomers = [
        {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '0412345678',
          created_at: '2023-01-01T00:00:00Z'
        },
        {
          id: '2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '0487654321',
          created_at: '2023-01-02T00:00:00Z'
        }
      ];

      // Mock Supabase response
      mockSupabaseResponse({ data: mockCustomers, count: 2 });

      // Create mock request and response
      const { req, res } = createApiMocks();

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(200);
      expect(res._getJSONData()).toEqual({ customers: mockCustomers, total: 2 });
    });

    it('should handle search parameter', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock Supabase response
      const supabase = mockSupabaseResponse({ data: [], count: 0 });

      // Create mock request with search parameter
      const { req, res } = createApiMocks({
        query: { search: 'john' }
      });

      // Call the API handler
      await handler(req, res);

      // Check that the or method was called with the search pattern
      expect(supabase.or).toHaveBeenCalledWith(expect.stringContaining('john'));
    });

    it('should handle sorting parameters', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock Supabase response
      const supabase = mockSupabaseResponse({ data: [], count: 0 });

      // Create mock request with sort parameters
      const { req, res } = createApiMocks({
        query: { sort_by: 'name', sort_order: 'asc' }
      });

      // Call the API handler
      await handler(req, res);

      // Check that the order method was called with the sort parameters
      expect(supabase.order).toHaveBeenCalledWith('name', { ascending: true });
    });

    it('should handle pagination parameters', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock Supabase response
      const supabase = mockSupabaseResponse({ data: [], count: 0 });

      // Create mock request with pagination parameters
      const { req, res } = createApiMocks({
        query: { limit: '10', offset: '20' }
      });

      // Call the API handler
      await handler(req, res);

      // Check that the range method was called with the pagination parameters
      expect(supabase.range).toHaveBeenCalledWith(20, 29);
    });

    it('should handle location filters', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock Supabase response
      const supabase = mockSupabaseResponse({ data: [], count: 0 });

      // Create mock request with location filters
      const { req, res } = createApiMocks({
        query: { city: 'Sydney', state: 'NSW' }
      });

      // Call the API handler
      await handler(req, res);

      // Check that the ilike method was called with the location filters
      expect(supabase.ilike).toHaveBeenCalledWith('city', '%Sydney%');
      expect(supabase.ilike).toHaveBeenCalledWith('state', '%NSW%');
    });

    it('should handle database errors', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock Supabase error
      mockSupabaseResponse({ error: { message: 'Database error' } });

      // Create mock request and response
      const { req, res } = createApiMocks();

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(500);
      expect(res._getJSONData()).toEqual({ error: 'Database error' });
    });
  });

  describe('POST /api/customers', () => {
    it('should create a new customer', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock customer data
      const newCustomer = {
        name: 'New Customer',
        email: '<EMAIL>',
        phone: '0411223344'
      };

      // Mock Supabase response
      mockSupabaseResponse({ data: [{ id: '3', ...newCustomer }] });

      // Create mock request with customer data
      const { req, res } = createApiMocks({
        method: 'POST',
        body: newCustomer
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(201);
      expect(res._getJSONData()).toEqual({ id: '3', ...newCustomer });
    });

    it('should validate required fields', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Create mock request with missing required fields
      const { req, res } = createApiMocks({
        method: 'POST',
        body: { phone: '0411223344' } // Missing name and email
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(400);
      expect(res._getJSONData()).toEqual({ error: 'Name and email are required' });
    });

    it('should check for duplicate email', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock existing customer with same email
      mockSupabaseResponse({ data: { id: 'existing-id' } });

      // Create mock request with duplicate email
      const { req, res } = createApiMocks({
        method: 'POST',
        body: {
          name: 'Duplicate Customer',
          email: '<EMAIL>',
          phone: '0411223344'
        }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(409);
      expect(res._getJSONData()).toEqual({ error: 'A customer with this email already exists' });
    });

    it('should handle database errors during creation', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Mock Supabase error
      mockSupabaseResponse({ error: { message: 'Database error during creation' } });

      // Create mock request with customer data
      const { req, res } = createApiMocks({
        method: 'POST',
        body: {
          name: 'Error Customer',
          email: '<EMAIL>',
          phone: '0411223344'
        }
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(500);
      expect(res._getJSONData()).toEqual({ error: 'Database error during creation' });
    });
  });

  describe('Method Not Allowed', () => {
    it('should return 405 for unsupported methods', async () => {
      // Mock authenticated user
      mockAuthenticatedUser();

      // Create mock request with unsupported method
      const { req, res } = createApiMocks({
        method: 'PUT'
      });

      // Call the API handler
      await handler(req, res);

      // Check response
      expect(res.statusCode).toBe(405);
      expect(res._getJSONData()).toEqual({ error: 'Method not allowed' });
    });
  });
});

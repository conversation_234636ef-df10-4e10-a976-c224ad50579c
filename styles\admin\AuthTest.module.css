.authTest {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.authTest h2 {
  margin-top: 0;
  color: #333;
  font-size: 1.5rem;
  margin-bottom: 20px;
}

.testControls {
  margin-bottom: 20px;
}

.testButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.testButton:hover {
  background-color: #5a0b9d;
}

.testButton:disabled {
  background-color: #b78de0;
  cursor: not-allowed;
}

.testResults {
  background-color: white;
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.testResults h3 {
  margin-top: 0;
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 15px;
}

.testResult {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.testResult:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.testResult h4 {
  margin-top: 0;
  font-size: 1rem;
  color: #495057;
  margin-bottom: 8px;
}

.resultMessage {
  padding: 10px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

import { useState } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import styles from '@/styles/admin/Login.module.css'

export default function SetRole() {
  const [userId, setUserId] = useState('')
  const [role, setRole] = useState('admin')
  const [message, setMessage] = useState(null)
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setMessage(null)

    if (!userId) {
      setError('User ID is required')
      setLoading(false)
      return
    }

    try {
      console.log(`Attempting to set role for user ID: ${userId} to ${role}`);

      // First try the direct API endpoint
      try {
        console.log('Trying direct-set-role endpoint...');
        const response = await fetch('/api/admin/direct-set-role', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userId, role }),
        });

        // Log the raw response for debugging
        console.log('Raw response status:', response.status);
        console.log('Raw response statusText:', response.statusText);

        // Parse the response JSON
        const data = await response.json();
        console.log('API response data:', data);

        if (response.ok) {
          console.log('direct-set-role succeeded');
          return; // Success, exit the try block
        } else {
          console.error('direct-set-role failed, trying simple-set-role...');
          // Continue to the next approach
        }
      } catch (directError) {
        console.error('Error with direct-set-role:', directError);
        // Continue to the next approach
      }

      // If we get here, the first approach failed, try the simple API endpoint
      console.log('Trying simple-set-role endpoint...');
      const simpleResponse = await fetch('/api/admin/simple-set-role', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, role }),
      });

      // Log the raw response for debugging
      console.log('Simple raw response status:', simpleResponse.status);
      console.log('Simple raw response statusText:', simpleResponse.statusText);

      // Parse the response JSON
      let simpleData;
      try {
        simpleData = await simpleResponse.json();
        console.log('Simple API response data:', simpleData);
      } catch (jsonError) {
        console.error('Error parsing simple JSON response:', jsonError);
        throw new Error('Failed to parse API response');
      }

      if (!simpleResponse.ok) {
        console.error('Simple API error details:', {
          status: simpleResponse.status,
          statusText: simpleResponse.statusText,
          data: simpleData
        });

        const errorMessage = simpleData.error || simpleData.details || 'Failed to set user role';
        const errorDetails = simpleData.details ? ` (${simpleData.details})` : '';
        const errorCode = simpleData.code ? ` [Code: ${simpleData.code}]` : '';

        throw new Error(`${errorMessage}${errorDetails}${errorCode}`);
      }

    } catch (err) {
      console.error('Error setting role:', err);
      setError(err.message || 'Failed to set user role')
      setLoading(false)
      return
    }

    setMessage(`Successfully set role to '${role}' for user ${userId}`)
    setLoading(false)
  }

  return (
    <>
      <Head>
        <title>Set User Role | OceanSoulSparkles Admin</title>
        <meta name="description" content="Set user role for OceanSoulSparkles Admin" />
      </Head>

      <div className={styles.loginContainer}>
        <div className={styles.loginCard}>
          <div className={styles.logoContainer}>
            <img
              src="/images/logo.png"
              alt="OceanSoulSparkles Logo"
              className={styles.logo}
            />
          </div>

          <h1 className={styles.title}>Set User Role</h1>
          <p style={{ textAlign: 'center', marginBottom: '20px' }}>
            Use this utility to set a role for a new user
          </p>

          {error && <div className={styles.error}>{error}</div>}
          {message && <div className={styles.success}>{message}</div>}

          <form onSubmit={handleSubmit} className={styles.form}>
            <div className={styles.formGroup}>
              <label htmlFor="userId">User ID</label>
              <input
                id="userId"
                type="text"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                required
                className={styles.input}
                placeholder="e.g. 8c59a3bc-a96b-4555-bdc4-6abe905ae761"
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="role">Role</label>
              <select
                id="role"
                value={role}
                onChange={(e) => setRole(e.target.value)}
                className={styles.input}
              >
                <option value="admin">Admin</option>
                <option value="staff">Staff</option>
                <option value="user">User</option>
              </select>
            </div>

            <button
              type="submit"
              className={styles.loginButton}
              disabled={loading}
            >
              {loading ? 'Setting Role...' : 'Set Role'}
            </button>
          </form>

          <div className={styles.backToSite}>
            <Link href="/admin/login" className={styles.link}>
              ← Back to login
            </Link>
          </div>
        </div>
      </div>
    </>
  )
}

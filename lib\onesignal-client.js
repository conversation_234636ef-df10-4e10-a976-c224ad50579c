/**
 * OneSignal Client
 * 
 * A specialized client for interacting with OneSignal that doesn't add authentication headers
 * to prevent CORS issues. This client should be used for all OneSignal API calls.
 */

// Check if we're in a local/development environment
const isLocalEnvironment = () => {
  if (typeof window === 'undefined') return false;
  return window.location.hostname === 'localhost' || 
         window.location.hostname === '127.0.0.1' ||
         window.location.hostname.includes('.local');
};

// Check if we're on the client side
const isClientSide = () => typeof window !== 'undefined';

/**
 * Get the OneSignal instance safely
 * @returns {Object|null} The OneSignal instance or null if not available
 */
export const getOneSignalInstance = () => {
  if (!isClientSide()) return null;
  
  // In development, return a mock instance
  if (isLocalEnvironment()) {
    return oneSignalMock;
  }
  
  return window.OneSignal || null;
};

/**
 * Check if OneSignal is initialized
 * @returns {boolean} Whether OneSignal is initialized
 */
export const isOneSignalInitialized = () => {
  if (!isClientSide()) return false;
  
  // In development, always return true
  if (isLocalEnvironment()) return true;
  
  return !!window.OneSignal && 
         typeof window.OneSignal.getNotificationPermission === 'function';
};

/**
 * Make a fetch request to the OneSignal API without adding auth headers
 * @param {string} url - The URL to fetch
 * @param {Object} options - The fetch options
 * @returns {Promise<Response>} The fetch response
 */
export const oneSignalFetch = (url, options = {}) => {
  // Don't add any auth headers to OneSignal requests
  const newOptions = { ...options };
  newOptions.headers = {
    ...(newOptions.headers || {}),
    'Content-Type': 'application/json'
  };
  
  // Make the request without auth headers
  return fetch(url, newOptions);
};

/**
 * Mock implementation for development or when OneSignal fails to initialize
 */
const oneSignalMock = {
  getNotificationPermission: () => Promise.resolve('default'),
  isPushNotificationsEnabled: () => Promise.resolve(false),
  showNativePrompt: () => Promise.resolve(),
  showHttpPrompt: () => Promise.resolve(),
  showCategorySlidedown: () => Promise.resolve(),
  getUserId: () => Promise.resolve('mock-user-id'),
  setExternalUserId: () => Promise.resolve(),
  removeExternalUserId: () => Promise.resolve(),
  setEmail: () => Promise.resolve(),
  sendTag: () => Promise.resolve(),
  getTags: () => Promise.resolve({ role: 'customer', environment: 'development' }),
  on: () => {},
  once: () => {},
  off: () => {},
};

export default {
  getOneSignalInstance,
  isOneSignalInitialized,
  oneSignalFetch
};

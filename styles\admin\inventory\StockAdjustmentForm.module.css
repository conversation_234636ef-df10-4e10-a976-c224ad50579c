.adjustmentForm {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
}

.adjustmentForm h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 20px;
  color: #333;
  text-align: center;
}

.errorMessage {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 14px;
  border-left: 4px solid #d32f2f;
}

.formGroup {
  margin-bottom: 20px;
}

.formGroup label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.formGroup small {
  display: block;
  margin-top: 4px;
  color: #777;
  font-size: 12px;
}

.formControl {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.formControl:focus {
  border-color: #4a90e2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.currentStock {
  background-color: #f5f5f5;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stockLabel {
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.stockValue {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.cancelButton {
  padding: 10px 20px;
  background-color: #f5f5f5;
  color: #333;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.cancelButton:hover {
  background-color: #e0e0e0;
}

.saveButton {
  padding: 10px 20px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.saveButton:hover {
  background-color: #3a7bc8;
}

.saveButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

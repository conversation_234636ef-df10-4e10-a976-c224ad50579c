import { useState, useEffect } from 'react'
import { <PERSON>, Doughnut, Line } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'
import styles from '@/styles/admin/marketing/Analytics.module.css'

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

export default function MarketingAnalytics({ period = 'month' }) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [analytics, setAnalytics] = useState(null)

  // Fetch marketing analytics
  useEffect(() => {
    const fetchAnalytics = async () => {
      setLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/analytics/marketing?period=${period}`)
        
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch marketing analytics')
        }

        const data = await response.json()
        setAnalytics(data)
      } catch (error) {
        console.error('Error fetching marketing analytics:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchAnalytics()
  }, [period])

  // Prepare message type distribution chart data
  const prepareMessageTypeData = () => {
    if (!analytics || !analytics.message_types) return null

    const labels = Object.keys(analytics.message_types)
    const data = Object.values(analytics.message_types)

    return {
      labels,
      datasets: [
        {
          data,
          backgroundColor: [
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 99, 132, 0.6)',
            'rgba(255, 206, 86, 0.6)'
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 206, 86, 1)'
          ],
          borderWidth: 1
        }
      ]
    }
  }

  // Prepare time series chart data
  const prepareTimeSeriesData = () => {
    if (!analytics || !analytics.time_series || analytics.time_series.length === 0) return null

    return {
      labels: analytics.time_series.map(item => item.date),
      datasets: [
        {
          label: 'Total Messages',
          data: analytics.time_series.map(item => item.total),
          borderColor: 'rgba(54, 162, 235, 1)',
          backgroundColor: 'rgba(54, 162, 235, 0.1)',
          borderWidth: 2,
          tension: 0.4,
          fill: true
        },
        {
          label: 'Successful Messages',
          data: analytics.time_series.map(item => item.successful),
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.1)',
          borderWidth: 2,
          tension: 0.4,
          fill: true
        }
      ]
    }
  }

  // Chart options
  const doughnutOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          color: '#666',
          font: {
            size: 12
          }
        }
      },
      title: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || ''
            const value = context.raw || 0
            const total = context.dataset.data.reduce((a, b) => a + b, 0)
            const percentage = Math.round((value / total) * 100)
            return `${label}: ${value} (${percentage}%)`
          }
        }
      }
    }
  }

  const lineOptions = {
    responsive: true,
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          color: '#666'
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        }
      },
      x: {
        ticks: {
          color: '#666'
        },
        grid: {
          display: false
        }
      }
    },
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          color: '#666',
          font: {
            size: 12
          }
        }
      },
      title: {
        display: false
      }
    }
  }

  if (loading) {
    return <div className={styles.loading}>Loading marketing analytics...</div>
  }

  if (error) {
    return <div className={styles.error}>Error: {error}</div>
  }

  if (!analytics) {
    return <div className={styles.noData}>No marketing analytics data available</div>
  }

  const messageTypeData = prepareMessageTypeData()
  const timeSeriesData = prepareTimeSeriesData()

  return (
    <div className={styles.analyticsSection}>
      <h3>Marketing Overview</h3>
      
      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.summary.total_messages}</div>
          <div className={styles.metricLabel}>Total Messages</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.summary.successful_messages}</div>
          <div className={styles.metricLabel}>Successful Messages</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.summary.success_rate}%</div>
          <div className={styles.metricLabel}>Success Rate</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.summary.consent_rate}%</div>
          <div className={styles.metricLabel}>Consent Rate</div>
        </div>
      </div>
      
      <div className={styles.chartsGrid}>
        {messageTypeData && (
          <div className={styles.chartCard}>
            <h4>Message Type Distribution</h4>
            <div className={styles.chartContainer}>
              <Doughnut data={messageTypeData} options={doughnutOptions} />
            </div>
          </div>
        )}
        
        {timeSeriesData && (
          <div className={styles.chartCard}>
            <h4>Message Volume Over Time</h4>
            <div className={styles.chartContainer}>
              <Line data={timeSeriesData} options={lineOptions} />
            </div>
          </div>
        )}
      </div>
      
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <h4>Campaign Messages</h4>
          <div className={styles.statValue}>{analytics.summary.campaign_messages}</div>
          <div className={styles.statLabel}>
            {analytics.summary.total_messages > 0 
              ? `(${Math.round((analytics.summary.campaign_messages / analytics.summary.total_messages) * 100)}% of total)`
              : ''}
          </div>
        </div>
        <div className={styles.statCard}>
          <h4>Automation Messages</h4>
          <div className={styles.statValue}>{analytics.summary.automation_messages}</div>
          <div className={styles.statLabel}>
            {analytics.summary.total_messages > 0 
              ? `(${Math.round((analytics.summary.automation_messages / analytics.summary.total_messages) * 100)}% of total)`
              : ''}
          </div>
        </div>
        <div className={styles.statCard}>
          <h4>New Customers</h4>
          <div className={styles.statValue}>{analytics.summary.new_customers}</div>
          <div className={styles.statLabel}>
            {analytics.summary.customers_with_consent > 0 
              ? `${analytics.summary.customers_with_consent} with consent`
              : ''}
          </div>
        </div>
      </div>
    </div>
  )
}

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import BookingStatusHistory from '@/components/admin/BookingStatusHistory';
import supabase from '@/lib/supabase';
import styles from '@/styles/admin/BookingsPage.module.css';

/**
 * Test page for BookingStatusHistory component
 */
export default function TestStatusHistoryPage() {
  const [bookings, setBookings] = useState([]);
  const [selectedBookingId, setSelectedBookingId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch bookings
  useEffect(() => {
    const fetchBookings = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch bookings from Supabase
        const { data, error } = await supabase
          .from('bookings')
          .select(`
            id,
            customer_id,
            service_id,
            start_time,
            end_time,
            status,
            customers:customer_id (name, email)
          `)
          .order('start_time', { ascending: false })
          .limit(10);

        if (error) throw error;

        setBookings(data || []);
        if (data && data.length > 0) {
          setSelectedBookingId(data[0].id);
        }
      } catch (error) {
        console.error('Error fetching bookings:', error);
        setError('Failed to load bookings');
      } finally {
        setLoading(false);
      }
    };

    fetchBookings();
  }, []);

  return (
    <ProtectedRoute allowedRoles={['admin', 'staff']}>
      <AdminLayout title="Test Status History">
        <div className={styles.container}>
          <h1>Test Booking Status History</h1>
          
          {loading ? (
            <div>Loading bookings...</div>
          ) : error ? (
            <div className={styles.error}>{error}</div>
          ) : bookings.length === 0 ? (
            <div>No bookings found</div>
          ) : (
            <div className={styles.content}>
              <div className={styles.bookingSelector}>
                <h2>Select a Booking</h2>
                <div className={styles.bookingList}>
                  {bookings.map((booking) => (
                    <div 
                      key={booking.id} 
                      className={`${styles.bookingItem} ${selectedBookingId === booking.id ? styles.selected : ''}`}
                      onClick={() => setSelectedBookingId(booking.id)}
                    >
                      <div className={styles.bookingInfo}>
                        <div className={styles.bookingCustomer}>
                          {booking.customers?.name || 'Unknown Customer'}
                        </div>
                        <div className={styles.bookingDate}>
                          {new Date(booking.start_time).toLocaleDateString()}
                        </div>
                        <div className={styles.bookingStatus}>
                          {booking.status}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className={styles.statusHistoryContainer}>
                <h2>Status History</h2>
                {selectedBookingId ? (
                  <BookingStatusHistory bookingId={selectedBookingId} />
                ) : (
                  <div>Select a booking to view its status history</div>
                )}
              </div>
            </div>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}

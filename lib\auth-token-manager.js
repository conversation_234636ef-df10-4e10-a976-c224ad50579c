/**
 * Auth Token Manager
 *
 * A centralized utility for managing authentication tokens
 * with a simplified, consistent approach to reduce complexity
 * and prevent authentication issues.
 *
 * This version uses Supabase directly for authentication.
 */

import supabase from './supabase';

// Constants
const TOKEN_STORAGE_KEY = 'oss_auth_token_cache';
const TOKEN_EXPIRY_BUFFER = 5 * 60 * 1000; // 5 minutes buffer before expiry

/**
 * Get the current auth token from Supabase session or storage
 *
 * @returns {Promise<string|null>} The auth token or null if not available
 */
export const getAuthToken = async () => {
  // First try to get token from sessionStorage for performance
  const cachedToken = getTokenFromStorage();
  if (cachedToken) {
    return cachedToken;
  }

  // If no valid token in storage, get from current Supabase session
  try {
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.warn('Error getting session:', error);
      return null;
    }

    if (data?.session?.access_token) {
      // Store the token for future use
      storeToken(data.session.access_token);
      return data.session.access_token;
    }
  } catch (error) {
    console.warn('Failed to get session token:', error);
  }

  // If still no token, try to refresh the session
  try {
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      console.error('Error refreshing session:', error);
      return null;
    }

    if (data?.session?.access_token) {
      // Store the refreshed token
      storeToken(data.session.access_token);
      return data.session.access_token;
    }
  } catch (error) {
    console.error('Error refreshing auth token:', error);
  }

  return null;
};

/**
 * Store a token in sessionStorage with expiration
 *
 * @param {string} token - The auth token to store
 */
export const storeToken = (token) => {
  if (!token || typeof window === 'undefined' || !window.sessionStorage) {
    return;
  }

  try {
    const tokenData = {
      token,
      expiry: Date.now() + (60 * 60 * 1000) - TOKEN_EXPIRY_BUFFER, // 1 hour minus buffer
      refreshed: Date.now()
    };

    sessionStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify(tokenData));
  } catch (error) {
    console.warn('Failed to store auth token:', error);
  }
};

/**
 * Get a token from sessionStorage if it exists and is valid
 *
 * @returns {string|null} The auth token or null if not available/valid
 */
export const getTokenFromStorage = () => {
  if (typeof window === 'undefined' || !window.sessionStorage) {
    return null;
  }

  try {
    const cachedToken = sessionStorage.getItem(TOKEN_STORAGE_KEY);
    if (cachedToken) {
      const tokenData = JSON.parse(cachedToken);
      if (tokenData && tokenData.token && tokenData.expiry > Date.now()) {
        return tokenData.token;
      }
    }
  } catch (error) {
    console.warn('Error accessing token from storage:', error);
  }

  return null;
};

/**
 * Clear the auth token from storage
 */
export const clearToken = () => {
  if (typeof window === 'undefined' || !window.sessionStorage) {
    return;
  }

  try {
    sessionStorage.removeItem(TOKEN_STORAGE_KEY);
  } catch (error) {
    console.warn('Error clearing auth token:', error);
  }
};

/**
 * Add auth headers to a request options object
 *
 * @param {Object} options - The request options
 * @returns {Promise<Object>} The updated options with auth headers
 */
export const addAuthHeaders = async (options = {}) => {
  const token = await getAuthToken();

  const headers = {
    ...options.headers,
    'Content-Type': 'application/json',
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return {
    ...options,
    headers,
    credentials: 'include', // Always include credentials for cookies
  };
};

export default {
  getAuthToken,
  storeToken,
  getTokenFromStorage,
  clearToken,
  addAuthHeaders
};

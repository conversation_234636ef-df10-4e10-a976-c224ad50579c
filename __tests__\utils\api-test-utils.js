/**
 * Utility functions for testing API endpoints
 */

import { createMocks } from 'node-mocks-http';

// Mock Supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    count: jest.fn().mockReturnValue('exact'),
  }
}));

// Mock Supabase client
jest.mock('@/lib/supabase', () => ({
  __esModule: true,
  default: {
    getCurrentUser: jest.fn(),
    verifyAdminRole: jest.fn(),
    query: jest.fn(),
    initialize: jest.fn(),
    signIn: jest.fn(),
    signOut: jest.fn(),
    refreshSession: jest.fn(),
    persistSession: jest.fn(),
    restoreSession: jest.fn(),
  }
}));

/**
 * Create mock request and response objects
 * 
 * @param {Object} options - Options for the mock request
 * @param {string} options.method - HTTP method (GET, POST, PUT, DELETE)
 * @param {Object} options.query - Query parameters
 * @param {Object} options.body - Request body
 * @param {Object} options.headers - Request headers
 * @returns {Object} - Object containing mock request and response
 */
export function createApiMocks(options = {}) {
  const {
    method = 'GET',
    query = {},
    body = {},
    headers = {},
  } = options;

  return createMocks({
    method,
    query,
    body,
    headers,
  });
}

/**
 * Mock authenticated user
 * 
 * @param {Object} user - User object
 * @param {string} role - User role (admin, staff)
 */
export function mockAuthenticatedUser(user = { id: 'test-user-id' }, role = 'admin') {
  const supabaseClient = require('@/lib/supabase').default;
  supabaseClient.getCurrentUser.mockResolvedValue({ user, role, error: null });
  supabaseClient.verifyAdminRole.mockResolvedValue(role === 'admin');
}

/**
 * Mock unauthenticated user (authentication failure)
 */
export function mockUnauthenticatedUser() {
  const supabaseClient = require('@/lib/supabase').default;
  supabaseClient.getCurrentUser.mockRejectedValue(new Error('Authentication failed'));
}

/**
 * Mock unauthorized user (authenticated but wrong role)
 */
export function mockUnauthorizedUser() {
  const supabaseClient = require('@/lib/supabase').default;
  supabaseClient.getCurrentUser.mockResolvedValue({ 
    user: { id: 'test-user-id' }, 
    role: 'user', 
    error: null 
  });
  supabaseClient.verifyAdminRole.mockResolvedValue(false);
}

/**
 * Mock Supabase response
 * 
 * @param {Object} options - Options for the mock response
 * @param {Object|Array} options.data - Response data
 * @param {Object} options.error - Response error
 * @param {number} options.count - Count for pagination
 */
export function mockSupabaseResponse(options = {}) {
  const { data = null, error = null, count = null } = options;
  const supabase = require('@/lib/supabase').supabase;

  // Reset all mocks
  Object.keys(supabase).forEach(key => {
    if (typeof supabase[key].mockReset === 'function') {
      supabase[key].mockReset();
    }
  });

  // Set up the response
  if (data !== null) {
    if (count !== null) {
      supabase.from.mockReturnValue({
        ...supabase,
        select: jest.fn().mockReturnValue({
          ...supabase,
          count: jest.fn().mockResolvedValue({ data, count, error })
        })
      });
    } else {
      supabase.from.mockReturnValue({
        ...supabase,
        select: jest.fn().mockResolvedValue({ data, error }),
        insert: jest.fn().mockResolvedValue({ data, error }),
        update: jest.fn().mockResolvedValue({ data, error }),
        delete: jest.fn().mockResolvedValue({ data, error }),
      });
    }
  } else if (error !== null) {
    supabase.from.mockReturnValue({
      ...supabase,
      select: jest.fn().mockResolvedValue({ data: null, error }),
      insert: jest.fn().mockResolvedValue({ data: null, error }),
      update: jest.fn().mockResolvedValue({ data: null, error }),
      delete: jest.fn().mockResolvedValue({ data: null, error }),
    });
  }

  return supabase;
}

/**
 * Reset all mocks
 */
export function resetMocks() {
  jest.resetAllMocks();
}

# Implementation Guide: Supabase Authentication

This guide provides implementation details and usage examples for the Supabase authentication system in Ocean Soul Sparkles.

## Overview

The Ocean Soul Sparkles application uses direct Supabase integration for authentication and database operations. This implementation:

- Simplifies the architecture
- Improves performance
- Enhances reliability
- Reduces complexity
- Improves maintainability

## Implementation Patterns

Follow these patterns when implementing authentication in new components:

### 1. Import Pattern

Use consistent imports from `lib/supabase`:

```javascript
// Standard imports for authentication
import supabase, { getCurrentUser, getAdminClient } from '@/lib/supabase';
```

### 2. Client Usage

Use the direct Supabase client for all operations:

```javascript
// Standard client usage
const client = supabase;

// For database operations
const { data, error } = await client
  .from('table_name')
  .select('*');
```

### 3. Admin Client Usage

Use the admin client for privileged operations:

```javascript
// Admin client usage
const adminClient = getAdminClient();
if (!adminClient) {
  console.error("Supabase admin client not available.");
  return;
}

// Perform admin operations
const { data, error } = await adminClient
  .from('table_name')
  .select('*');
```

### 4. Update Authentication Methods

### 4. Authentication Methods

Use Supabase authentication methods:

```javascript
// Get current user and role
const { user, role } = await getCurrentUser();

// Get current session
const { data, error } = await supabase.auth.getSession();

// Refresh session
const { data, error } = await supabase.auth.refreshSession();
```

### 5. Token Handling

Handle authentication tokens properly:

```javascript
// Get current session and token
const { data } = await supabase.auth.getSession();
const token = data?.session?.access_token;

// Use token in API requests
const response = await fetch('/api/endpoint', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

```

### 6. Database Queries

Database queries use standard Supabase syntax:

```javascript
// Standard query pattern
const { data, error } = await supabase
  .from('customers')
  .select('*')
  .eq('id', customerId);

// Admin query with elevated privileges
const { data, error } = await adminClient
  .from('admin_table')
  .select('*')
  .eq('id', recordId);
```

### 7. Error Handling

Use consistent error handling patterns:

```javascript
// Authentication with error handling
try {
  const { data, error } = await supabase.auth.signInWithPassword({ 
    email, 
    password 
  });
  if (error) throw error;
  // Handle success
} catch (error) {
  console.error('Authentication error:', error.message);
  // Handle error
```

## Common Implementation Patterns

### API Route Authentication

```javascript
// Standard API route with authentication
import { getCurrentUser, getClient } from '@/lib/supabase';

export default async function handler(req, res) {
  try {
    const { user, role } = await getCurrentUser(req);
    if (!user || role !== 'admin') {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    const client = getClient();
    // Use client for database operations
    const { data, error } = await client
      .from('table_name')
      .select('*');
      
    if (error) throw error;
    res.json({ data });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}
import supabase, { getCurrentUser } from '@/lib/supabase';

```

### Admin Operations

```javascript
// Admin API route with elevated privileges
import { getCurrentUser, getAdminClient } from '@/lib/supabase';

export default async function handler(req, res) {
  try {
    const { user, role } = await getCurrentUser(req);
    if (!user || role !== 'admin') {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    const adminClient = getAdminClient();
    if (!adminClient) {
      return res.status(500).json({ error: 'Admin client not available' });
    }
    
    // Use adminClient for privileged operations
    const { data, error } = await adminClient
      .from('admin_table')
      .select('*');
      
    if (error) throw error;
    res.json({ data });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}
```

### Frontend Component Authentication

```javascript
// React component with authentication
import { useEffect, useState } from 'react';
import supabase from '@/lib/supabase';

export default function AdminComponent() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user ?? null);
      setLoading(false);
    };

    getSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  if (loading) return <div>Loading...</div>;
  if (!user) return <div>Please log in</div>;

  return <div>Welcome, {user.email}!</div>;
}
```

## Best Practices

1. **Always check authentication** before performing operations
2. **Use proper error handling** for authentication failures
3. **Implement proper session management** with token refresh
4. **Use admin client only when necessary** for privileged operations
5. **Handle loading states** in frontend components
6. **Implement proper logout** functionality

## Testing

Test your authentication implementation:

```javascript
// Test authentication endpoint
const response = await fetch('/api/admin/test', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const result = await response.json();
console.log('Auth test result:', result);
```
  }
}

// After
import { getAdminClient } from '@/lib/supabase';

export default async function handler(req, res) {
  try {
    const adminClient = getAdminClient();
    if (!adminClient) {
      return res.status(500).json({ error: 'Admin client not available' });
    }
    
    // Use adminClient for database operations
  } catch (error) {
    return res.status(500).json({ error: 'Server error' });
  }
}
```

### Client-Side Authentication

```javascript
import { useEffect, useState } from 'react';
import { getCurrentUser } from '@/lib/supabase';

function MyComponent() {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { user } = await getCurrentUser();
        setUser(user);
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    };
    
    fetchUser();
  }, []);
  
  // Component logic
}
```

## Testing Your Migration

After migrating a component, test it thoroughly to ensure it works correctly:

1. Test authentication flows
2. Test database operations
3. Test error handling
4. Test edge cases (e.g., token expiration, network errors)

## Common Issues and Solutions

### Issue: Authentication Fails After Migration

**Solution**: Ensure you're using the correct Supabase URL and API keys in your environment variables.

### Issue: Database Queries Return No Data

**Solution**: Check your RLS policies to ensure they allow the current user to access the data.

### Issue: Admin Operations Fail

**Solution**: Ensure the `SUPABASE_SERVICE_ROLE_KEY` environment variable is set correctly and that you're using `getAdminClient()` for admin operations.

### Issue: Token Refresh Doesn't Work

**Solution**: Ensure you're using Supabase's built-in token refresh mechanism or the custom refresh logic in `lib/auth-fetch.js`.

## Need Help?

If you encounter any issues during migration, please:

1. Check the Supabase documentation: [https://supabase.io/docs](https://supabase.io/docs)
2. Review the authentication documentation in `docs/authentication/`
3. Contact the development team for assistance

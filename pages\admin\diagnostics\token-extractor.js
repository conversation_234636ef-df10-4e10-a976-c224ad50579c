import { useEffect, useState } from 'react';
import Head from 'next/head';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Token Extractor Page
 *
 * This page extracts the authentication token from browser storage
 * and displays it for use in API testing.
 */
export default function TokenExtractorPage() {
  const { user, role, loading } = useAuth();
  const [token, setToken] = useState(null);
  const [copied, setCopied] = useState(false);

  // Extract token from browser storage
  useEffect(() => {
    if (user && typeof window !== 'undefined') {
      try {
        // Check for token in localStorage
        const ossToken = localStorage.getItem('oss_auth_token');
        if (ossToken) {
          try {
            const parsedToken = JSON.parse(ossToken);
            setToken(parsedToken.access_token || null);
            return;
          } catch (e) {
            setToken(ossToken);
            return;
          }
        }

        // Check for token in Supabase storage
        const supabaseToken = localStorage.getItem('sb-ndlgbcsbidyhxbpqzgqp-auth-token');
        if (supabaseToken) {
          try {
            const parsedToken = JSON.parse(supabaseToken);
            setToken(parsedToken.access_token || null);
            return;
          } catch (e) {
            setToken(supabaseToken);
            return;
          }
        }
      } catch (error) {
        console.error('Error extracting token:', error);
      }
    }
  }, [user]);

  // Copy token to clipboard
  const copyToClipboard = () => {
    if (token) {
      navigator.clipboard.writeText(token)
        .then(() => {
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
        })
        .catch(err => {
          console.error('Failed to copy token:', err);
        });
    }
  };

  // Run API tests with token
  const runApiTests = () => {
    if (token) {
      const testCommand = `node scripts/run-auth-tests.js --token=${token}`;
      console.log('Run this command in your terminal:');
      console.log(testCommand);

      // Copy command to clipboard
      navigator.clipboard.writeText(testCommand)
        .then(() => {
          alert('Test command copied to clipboard!');
        })
        .catch(err => {
          console.error('Failed to copy test command:', err);
        });
    }
  };

  return (
    <>
      <Head>
        <title>Token Extractor | Ocean Soul Sparkles Admin</title>
      </Head>

      <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-md p-6 w-full max-w-2xl">
          <h1 className="text-3xl font-bold mb-6 text-center">Authentication Token Extractor</h1>

          {loading ? (
            <p className="text-center">Loading authentication information...</p>
          ) : !user ? (
            <div className="text-center">
              <p className="mb-4">You need to be logged in to extract a token.</p>
              <a
                href="/admin/login"
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
              >
                Go to Login
              </a>
            </div>
          ) : (
            <div>
              <div className="mb-6">
                <p className="mb-2"><strong>User:</strong> {user.email}</p>
                <p className="mb-2"><strong>Role:</strong> {role || 'None'}</p>
              </div>

              {token ? (
                <div>
                  <div className="mb-4">
                    <h2 className="text-xl font-semibold mb-2">Authentication Token:</h2>
                    <div className="bg-gray-100 p-3 rounded-md overflow-x-auto">
                      <code className="text-sm break-all">{token}</code>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-4 mb-6">
                    <button
                      onClick={copyToClipboard}
                      className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
                    >
                      {copied ? 'Copied!' : 'Copy Token'}
                    </button>

                    <button
                      onClick={runApiTests}
                      className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                    >
                      Run API Tests
                    </button>
                  </div>

                  <div className="bg-gray-100 p-4 rounded-md">
                    <h3 className="font-semibold mb-2">To run API tests manually:</h3>
                    <ol className="list-decimal list-inside space-y-2">
                      <li>Copy the token above</li>
                      <li>Open a terminal in the project root</li>
                      <li>Run the following command:</li>
                      <div className="bg-gray-200 p-2 rounded mt-2 overflow-x-auto">
                        <code>node scripts/run-auth-tests.js --token={token.substring(0, 10)}...</code>
                      </div>
                    </ol>
                  </div>
                </div>
              ) : (
                <p className="text-center text-red-500">No authentication token found in browser storage.</p>
              )}
            </div>
          )}

          <div className="mt-6 pt-4 border-t border-gray-200">
            <p className="text-center text-gray-500 text-sm">
              This tool is for testing purposes only. The extracted token should not be shared.
            </p>
          </div>
        </div>
      </div>
    </>
  );
}

-- Phase 3 Migration: Inventory Management and Analytics
-- This script adds the necessary tables and functions for inventory management and analytics

-- Create product_categories table
CREATE TABLE IF NOT EXISTS product_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  parent_id UUID REFERENCES product_categories(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for product_categories
ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow read access to all users" 
  ON product_categories FOR SELECT 
  USING (true);

CREATE POLICY "Allow insert/update/delete for authenticated users" 
  ON product_categories FOR ALL 
  USING (auth.role() = 'authenticated');

-- Create inventory_transactions table
CREATE TABLE IF NOT EXISTS inventory_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES products(id),
  quantity INTEGER NOT NULL,
  transaction_type VARCHAR(50) NOT NULL CHECK (transaction_type IN ('restock', 'sale', 'adjustment', 'return', 'damaged', 'lost')),
  notes TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for inventory_transactions
ALTER TABLE inventory_transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow read access to authenticated users" 
  ON inventory_transactions FOR SELECT 
  USING (auth.role() = 'authenticated');

CREATE POLICY "Allow insert/update/delete for authenticated users" 
  ON inventory_transactions FOR ALL 
  USING (auth.role() = 'authenticated');

-- Create analytics table for storing aggregated data
CREATE TABLE IF NOT EXISTS analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  date DATE NOT NULL UNIQUE,
  sessions INTEGER DEFAULT 0,
  page_views INTEGER DEFAULT 0,
  unique_visitors INTEGER DEFAULT 0,
  bounce_rate NUMERIC(5,2) DEFAULT 0,
  avg_session_duration NUMERIC(10,2) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for analytics
ALTER TABLE analytics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow read access to authenticated users" 
  ON analytics FOR SELECT 
  USING (auth.role() = 'authenticated');

CREATE POLICY "Allow insert/update/delete for authenticated users" 
  ON analytics FOR ALL 
  USING (auth.role() = 'authenticated');

-- Add new columns to products table
ALTER TABLE products ADD COLUMN IF NOT EXISTS category UUID REFERENCES product_categories(id);
ALTER TABLE products ADD COLUMN IF NOT EXISTS cost_price NUMERIC(10,2);
ALTER TABLE products ADD COLUMN IF NOT EXISTS low_stock_threshold INTEGER DEFAULT 5;
ALTER TABLE products ADD COLUMN IF NOT EXISTS gallery_images TEXT[] DEFAULT '{}';
ALTER TABLE products ADD COLUMN IF NOT EXISTS meta_title VARCHAR(255);
ALTER TABLE products ADD COLUMN IF NOT EXISTS meta_description TEXT;

-- Create function to update product updated_at timestamp
CREATE OR REPLACE FUNCTION update_product_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update product updated_at timestamp
DROP TRIGGER IF EXISTS update_product_updated_at_trigger ON products;
CREATE TRIGGER update_product_updated_at_trigger
BEFORE UPDATE ON products
FOR EACH ROW
EXECUTE FUNCTION update_product_updated_at();

-- Create function to log inventory transactions
CREATE OR REPLACE FUNCTION log_inventory_transaction()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert into inventory_transactions
  INSERT INTO inventory_transactions (
    product_id,
    quantity,
    transaction_type,
    notes,
    created_by
  ) VALUES (
    NEW.id,
    NEW.stock - COALESCE(OLD.stock, 0),
    'adjustment',
    'Stock updated via product update',
    auth.uid()
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to log inventory transactions
DROP TRIGGER IF EXISTS log_inventory_transaction_trigger ON products;
CREATE TRIGGER log_inventory_transaction_trigger
AFTER UPDATE OF stock ON products
FOR EACH ROW
WHEN (NEW.stock IS DISTINCT FROM OLD.stock)
EXECUTE FUNCTION log_inventory_transaction();

-- Create view for product stock status
CREATE OR REPLACE VIEW product_stock_status AS
SELECT 
  p.id,
  p.name,
  p.sku,
  p.stock,
  p.low_stock_threshold,
  CASE 
    WHEN p.stock <= 0 THEN 'out_of_stock'
    WHEN p.stock <= p.low_stock_threshold THEN 'low_stock'
    ELSE 'in_stock'
  END AS stock_status,
  pc.name AS category_name
FROM 
  products p
LEFT JOIN 
  product_categories pc ON p.category = pc.id;

-- Create function to get sales analytics
CREATE OR REPLACE FUNCTION get_sales_analytics(
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE
)
RETURNS TABLE (
  date DATE,
  orders_count INTEGER,
  revenue NUMERIC,
  average_order_value NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    DATE(o.created_at) AS date,
    COUNT(o.id)::INTEGER AS orders_count,
    SUM(o.total_amount) AS revenue,
    CASE 
      WHEN COUNT(o.id) > 0 THEN SUM(o.total_amount) / COUNT(o.id)
      ELSE 0
    END AS average_order_value
  FROM 
    orders o
  WHERE 
    o.created_at BETWEEN start_date AND end_date
  GROUP BY 
    DATE(o.created_at)
  ORDER BY 
    date;
END;
$$ LANGUAGE plpgsql;

-- Insert sample data for product categories
INSERT INTO product_categories (name, description)
VALUES 
  ('Face Painting', 'Face painting products and supplies'),
  ('Glitter', 'Glitter products and accessories'),
  ('Accessories', 'Various accessories for events'),
  ('Merchandise', 'Branded merchandise items')
ON CONFLICT DO NOTHING;

-- Insert sample data for analytics
INSERT INTO analytics (date, sessions, page_views, unique_visitors, bounce_rate, avg_session_duration)
VALUES 
  (CURRENT_DATE - INTERVAL '7 days', 120, 350, 95, 35.5, 125.5),
  (CURRENT_DATE - INTERVAL '6 days', 135, 410, 105, 32.1, 130.2),
  (CURRENT_DATE - INTERVAL '5 days', 142, 425, 112, 30.8, 135.7),
  (CURRENT_DATE - INTERVAL '4 days', 156, 470, 120, 28.5, 142.3),
  (CURRENT_DATE - INTERVAL '3 days', 168, 510, 130, 27.2, 145.8),
  (CURRENT_DATE - INTERVAL '2 days', 175, 530, 138, 26.5, 150.2),
  (CURRENT_DATE - INTERVAL '1 day', 182, 550, 145, 25.8, 155.5),
  (CURRENT_DATE, 190, 580, 152, 24.5, 160.3)
ON CONFLICT (date) DO UPDATE
SET 
  sessions = EXCLUDED.sessions,
  page_views = EXCLUDED.page_views,
  unique_visitors = EXCLUDED.unique_visitors,
  bounce_rate = EXCLUDED.bounce_rate,
  avg_session_duration = EXCLUDED.avg_session_duration,
  updated_at = NOW();

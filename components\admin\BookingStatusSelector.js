import { useState } from 'react';
import { 
  BOOKING_STATUSES, 
  STATUS_DISPLAY_NAMES, 
  STATUS_COLORS,
  getAvailableStatusOptions,
  isValidStatusTransition
} from '@/lib/booking-status';
import styles from '@/styles/admin/BookingStatusSelector.module.css';

/**
 * Component for selecting booking status with validation
 * 
 * @param {Object} props - Component props
 * @param {string} props.currentStatus - Current booking status
 * @param {Function} props.onStatusChange - Function to call when status changes
 * @param {boolean} props.disabled - Whether the selector is disabled
 * @returns {JSX.Element}
 */
export default function BookingStatusSelector({ currentStatus, onStatusChange, disabled = false }) {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [notes, setNotes] = useState('');
  const [error, setError] = useState(null);
  
  // Get available status options based on current status
  const availableOptions = getAvailableStatusOptions(currentStatus);
  
  // Handle status selection
  const handleStatusSelect = (status) => {
    // Add null check for React patching compatibility
    if (!status || typeof status !== 'string') {
      console.error('Invalid status selection:', status);
      return;
    }
    // If selecting the current status, do nothing
    if (status === currentStatus) {
      return;
    }
    
    // Validate status transition
    if (!isValidStatusTransition(currentStatus, status)) {
      setError(`Cannot change status from ${STATUS_DISPLAY_NAMES[currentStatus]} to ${STATUS_DISPLAY_NAMES[status]}`);
      return;
    }
    
    // Clear any previous errors
    setError(null);
    
    // Set the selected status
    setSelectedStatus(status);
    
    // Show confirmation dialog
    setShowConfirmation(true);
  };
  
  // Handle confirmation
  const handleConfirm = () => {
    // Call the onStatusChange callback with the new status and notes
    onStatusChange(selectedStatus, notes);
    
    // Reset state
    setShowConfirmation(false);
    setSelectedStatus(null);
    setNotes('');
  };
  
  // Handle cancellation
  const handleCancel = () => {
    setShowConfirmation(false);
    setSelectedStatus(null);
    setNotes('');
  };
  
  return (
    <div className={styles.statusSelector}>
      {error && (
        <div className={styles.error}>
          {error}
          <button 
            className={styles.closeButton} 
            onClick={() => setError(null)}
            aria-label="Close error message"
          >
            &times;
          </button>
        </div>
      )}
      
      <div className={styles.statusButtons}>
        {availableOptions.map((status) => (
          <button
            key={status}
            className={`${styles.statusButton} ${status === currentStatus ? styles.active : ''}`}
            style={{
              backgroundColor: status === currentStatus ? STATUS_COLORS[status] : 'transparent',
              color: status === currentStatus ? '#fff' : '#333',
              borderColor: STATUS_COLORS[status]
            }}
            onClick={() => handleStatusSelect(status)}
            disabled={disabled || status === currentStatus}
          >
            {STATUS_DISPLAY_NAMES[status]}
          </button>
        ))}
      </div>
      
      {showConfirmation && (
        <div className={styles.confirmationDialog}>
          <h4 className={styles.confirmationTitle}>
            Change status to {STATUS_DISPLAY_NAMES[selectedStatus]}?
          </h4>
          
          <div className={styles.notesField}>
            <label htmlFor="status-notes">Notes (optional):</label>
            <textarea
              id="status-notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add notes about this status change..."
              rows={3}
              className={styles.notesInput}
            />
          </div>
          
          <div className={styles.confirmationActions}>
            <button 
              className={styles.cancelButton}
              onClick={handleCancel}
            >
              Cancel
            </button>
            <button 
              className={styles.confirmButton}
              onClick={handleConfirm}
              style={{ backgroundColor: STATUS_COLORS[selectedStatus] }}
            >
              Confirm
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import styles from '@/styles/admin/marketing/CampaignForm.module.css'

export default function CampaignForm({ initialCampaign = null, onSubmit, onCancel }) {
  const router = useRouter()
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [status, setStatus] = useState('draft')
  const [campaignType, setCampaignType] = useState('email')
  const [targetSegment, setTargetSegment] = useState('')
  const [segments, setSegments] = useState([])
  const [loading, setLoading] = useState(false)
  const [fetchLoading, setFetchLoading] = useState(true)
  const [error, setError] = useState(null)

  // Initialize form with initial campaign data
  useEffect(() => {
    if (initialCampaign) {
      setName(initialCampaign.name || '')
      setDescription(initialCampaign.description || '')
      setStartDate(initialCampaign.start_date ? new Date(initialCampaign.start_date).toISOString().slice(0, 16) : '')
      setEndDate(initialCampaign.end_date ? new Date(initialCampaign.end_date).toISOString().slice(0, 16) : '')
      setStatus(initialCampaign.status || 'draft')
      setCampaignType(initialCampaign.campaign_type || 'email')
      setTargetSegment(initialCampaign.target_segment || '')
    } else if (router.query.segment) {
      setTargetSegment(router.query.segment)
    }
  }, [initialCampaign, router.query.segment])

  // Fetch segments
  useEffect(() => {
    const fetchSegments = async () => {
      setFetchLoading(true)
      setError(null)

      try {
        const response = await fetch('/api/marketing/segments?limit=100')
        
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch segments')
        }

        const data = await response.json()
        setSegments(data.segments || [])
      } catch (error) {
        console.error('Error fetching segments:', error)
        setError('Failed to load segments. Please try again.')
      } finally {
        setFetchLoading(false)
      }
    }

    fetchSegments()
  }, [])

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // Validate form
      if (!name) {
        throw new Error('Campaign name is required')
      }

      if (!startDate) {
        throw new Error('Start date is required')
      }

      if (!targetSegment) {
        throw new Error('Target segment is required')
      }

      // Call onSubmit callback
      await onSubmit({
        name,
        description,
        start_date: new Date(startDate).toISOString(),
        end_date: endDate ? new Date(endDate).toISOString() : null,
        status,
        campaign_type: campaignType,
        target_segment: targetSegment
      })
    } catch (error) {
      console.error('Error saving campaign:', error)
      setError(error.message)
      setLoading(false)
    }
  }

  return (
    <div className={styles.campaignForm}>
      {error && (
        <div className={styles.error}>
          Error: {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className={styles.formSection}>
          <h3>Campaign Details</h3>
          
          <div className={styles.formGroup}>
            <label htmlFor="name">Campaign Name *</label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="e.g., Summer Promotion"
              className={styles.input}
              disabled={loading}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe the purpose of this campaign"
              className={styles.textarea}
              disabled={loading}
              rows={3}
            />
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="campaign-type">Campaign Type *</label>
              <select
                id="campaign-type"
                value={campaignType}
                onChange={(e) => setCampaignType(e.target.value)}
                className={styles.select}
                disabled={loading || (initialCampaign && initialCampaign.status !== 'draft')}
                required
              >
                <option value="email">Email Campaign</option>
                <option value="sms">SMS Campaign</option>
                <option value="push">Push Notification Campaign</option>
                <option value="multi">Multi-channel Campaign</option>
              </select>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="status">Status *</label>
              <select
                id="status"
                value={status}
                onChange={(e) => setStatus(e.target.value)}
                className={styles.select}
                disabled={loading}
                required
              >
                <option value="draft">Draft</option>
                <option value="scheduled">Scheduled</option>
                <option value="active">Active</option>
                <option value="paused">Paused</option>
                <option value="completed">Completed</option>
                <option value="canceled">Canceled</option>
              </select>
            </div>
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="start-date">Start Date *</label>
              <input
                type="datetime-local"
                id="start-date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className={styles.input}
                disabled={loading}
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="end-date">End Date (optional)</label>
              <input
                type="datetime-local"
                id="end-date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className={styles.input}
                disabled={loading}
                min={startDate}
              />
              <div className={styles.helpText}>
                Leave blank for ongoing campaigns
              </div>
            </div>
          </div>
        </div>

        <div className={styles.formSection}>
          <h3>Target Audience</h3>
          
          <div className={styles.formGroup}>
            <label htmlFor="target-segment">Target Segment *</label>
            {fetchLoading ? (
              <div className={styles.loading}>Loading segments...</div>
            ) : segments.length === 0 ? (
              <div className={styles.noSegments}>
                <p>No segments found. Create a segment first.</p>
                <button
                  type="button"
                  onClick={() => router.push('/admin/marketing/segments/new')}
                  className={styles.createSegmentButton}
                >
                  Create Segment
                </button>
              </div>
            ) : (
              <select
                id="target-segment"
                value={targetSegment}
                onChange={(e) => setTargetSegment(e.target.value)}
                className={styles.select}
                disabled={loading || (initialCampaign && initialCampaign.status !== 'draft')}
                required
              >
                <option value="">Select a segment</option>
                {segments.map((segment) => (
                  <option key={segment.id} value={segment.id}>
                    {segment.name} ({segment.customer_count || 0} customers)
                  </option>
                ))}
              </select>
            )}
          </div>
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={onCancel}
            disabled={loading}
            className={styles.cancelButton}
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading || fetchLoading || segments.length === 0}
            className={styles.submitButton}
          >
            {loading ? 'Saving...' : initialCampaign ? 'Update Campaign' : 'Create Campaign'}
          </button>
        </div>
      </form>
    </div>
  )
}

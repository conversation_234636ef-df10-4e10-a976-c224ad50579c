.messageEditor {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.error {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.formGroup {
  margin-bottom: 20px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
}

.input,
.select,
.textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.2s ease;
}

.input:focus,
.select:focus,
.textarea:focus {
  outline: none;
  border-color: #6e8efb;
}

.textarea {
  resize: vertical;
  min-height: 150px;
}

.helpText {
  margin-top: 4px;
  font-size: 0.8rem;
  color: #666;
}

.contentHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.personalizationDropdown {
  position: relative;
}

.personalizationButton {
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.personalizationButton:hover:not(:disabled) {
  background-color: rgba(110, 142, 251, 0.1);
}

.personalizationButton:disabled {
  color: #ccc;
  border-color: #ccc;
  cursor: not-allowed;
}

.personalizationMenu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 10;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 150px;
  margin-top: 4px;
}

.personalizationMenu button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 8px 12px;
  background-color: transparent;
  border: none;
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.personalizationMenu button:hover {
  background-color: #f5f5f5;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancelButton,
.deleteButton,
.saveButton,
.testButton,
.sendButton {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
}

.cancelButton:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.deleteButton {
  background-color: transparent;
  color: #f44336;
  border: 1px solid #f44336;
}

.deleteButton:hover:not(:disabled) {
  background-color: rgba(244, 67, 54, 0.1);
}

.saveButton {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
}

.saveButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.sendActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.testButton {
  background-color: transparent;
  color: #ff9800;
  border: 1px solid #ff9800;
}

.testButton:hover:not(:disabled) {
  background-color: rgba(255, 152, 0, 0.1);
}

.sendButton {
  background-color: #4caf50;
  color: white;
  border: none;
}

.sendButton:hover:not(:disabled) {
  background-color: #43a047;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cancelButton:disabled,
.deleteButton:disabled,
.saveButton:disabled,
.testButton:disabled,
.sendButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.preview {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.preview h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.1rem;
  color: #333;
}

.previewSubject {
  margin-bottom: 16px;
}

.previewContentText {
  background-color: rgba(255, 255, 255, 0.5);
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 16px;
  margin-top: 8px;
}

.previewContentText p {
  margin: 0 0 8px 0;
}

.previewContentText p:last-child {
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .contentHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .formActions,
  .sendActions {
    flex-direction: column;
  }
  
  .cancelButton,
  .deleteButton,
  .saveButton,
  .testButton,
  .sendButton {
    width: 100%;
    text-align: center;
  }
}

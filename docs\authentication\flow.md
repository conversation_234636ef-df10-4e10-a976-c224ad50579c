# Authentication Flow

This document describes the authentication flow in the Ocean Soul Sparkles application.

## Login Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │     │             │
│  User       │     │  Login      │     │  Supabase   │     │  Database   │
│             │     │  Page       │     │  Auth       │     │             │
│             │     │             │     │             │     │             │
└──────┬──────┘     └──────┬──────┘     └──────┬──────┘     └──────┬──────┘
       │                   │                   │                   │
       │  Enter            │                   │                   │
       │  Credentials      │                   │                   │
       │ ─────────────────>│                   │                   │
       │                   │                   │                   │
       │                   │  signInWithPassword                   │
       │                   │ ─────────────────>│                   │
       │                   │                   │                   │
       │                   │                   │  Verify           │
       │                   │                   │  Credentials      │
       │                   │                   │ ─────────────────>│
       │                   │                   │                   │
       │                   │                   │  Return User      │
       │                   │                   │  Data             │
       │                   │                   │ <─────────────────│
       │                   │                   │                   │
       │                   │                   │  Get User         │
       │                   │                   │  Role             │
       │                   │                   │ ─────────────────>│
       │                   │                   │                   │
       │                   │                   │  Return Role      │
       │                   │                   │ <─────────────────│
       │                   │                   │                   │
       │                   │  Return JWT       │                   │
       │                   │  Token            │                   │
       │                   │ <─────────────────│                   │
       │                   │                   │                   │
       │                   │  Store Token      │                   │
       │                   │  & Redirect       │                   │
       │                   │ ─────────────────>│                   │
       │                   │                   │                   │
       │  Redirect to      │                   │                   │
       │  Dashboard        │                   │                   │
       │ <─────────────────│                   │                   │
       │                   │                   │                   │
└──────┴──────┘     └──────┴──────┘     └──────┴──────┘     └──────┴──────┘
```

### Step-by-Step Login Process

1. **User enters credentials**
   - User navigates to `/admin/login`
   - User enters email and password
   - User clicks "Login" button

2. **Client-side validation**
   - Basic validation of email format and password length
   - Display error messages if validation fails

3. **Authentication request**
   - Call `signIn()` function from `lib/auth.js`
   - Function calls Supabase `signInWithPassword()` method
   - Credentials sent to Supabase Auth API

   ```javascript
   // lib/auth.js
   export const signIn = async (email, password) => {
     try {
       const { data, error } = await supabase.auth.signInWithPassword({
         email,
         password
       });

       if (error) throw error;

       return { data, error: null };
     } catch (error) {
       return { data: null, error };
     }
   }
   ```

4. **Server-side authentication**
   - Supabase verifies credentials against database
   - If valid, generates JWT token with user information
   - If invalid, returns error message

5. **Role verification**
   - After successful authentication, query `user_roles` table
   - Get user's role (admin, staff)
   - Add role information to authentication response

6. **Token storage**
   - JWT token stored in browser using Supabase's storage mechanism
   - Token includes user ID, email, and expiration time
   - Token is automatically refreshed before expiration

7. **Redirect to dashboard**
   - User redirected to admin dashboard or requested page
   - AuthProvider component initializes with user information
   - Protected routes become accessible

## API Authentication Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │     │             │
│  Client     │     │  API        │     │  Auth       │     │  Database   │
│  (Browser)  │     │  Route      │     │  Middleware │     │             │
│             │     │             │     │             │     │             │
└──────┬──────┘     └──────┬──────┘     └──────┬──────┘     └──────┬──────┘
       │                   │                   │                   │
       │  API Request      │                   │                   │
       │  with Token       │                   │                   │
       │ ─────────────────>│                   │                   │
       │                   │                   │                   │
       │                   │  Pass Request     │                   │
       │                   │  to Middleware    │                   │
       │                   │ ─────────────────>│                   │
       │                   │                   │                   │
       │                   │                   │  Extract          │
       │                   │                   │  Token            │
       │                   │                   │ ─────────────────>│
       │                   │                   │                   │
       │                   │                   │  Verify           │
       │                   │                   │  Token            │
       │                   │                   │ ─────────────────>│
       │                   │                   │                   │
       │                   │                   │  Check            │
       │                   │                   │  User Role        │
       │                   │                   │ ─────────────────>│
       │                   │                   │                   │
       │                   │                   │  Return           │
       │                   │                   │  Role Data        │
       │                   │                   │ <─────────────────│
       │                   │                   │                   │
       │                   │  Return           │                   │
       │                   │  Auth Result      │                   │
       │                   │ <─────────────────│                   │
       │                   │                   │                   │
       │                   │  Process          │                   │
       │                   │  Request          │                   │
       │                   │ ─────────────────>│                   │
       │                   │                   │                   │
       │  API Response     │                   │                   │
       │ <─────────────────│                   │                   │
       │                   │                   │                   │
└──────┴──────┘     └──────┴──────┘     └──────┴──────┘     └──────┴──────┘
```

### Step-by-Step API Authentication Process

1. **Client makes API request**
   - Client includes JWT token in Authorization header
   - Format: `Authorization: Bearer <token>`
   - Alternative: `X-Auth-Token: <token>` (for cross-origin requests)

2. **API route receives request**
   - Next.js API route handler receives the request
   - Passes request to authentication middleware

3. **Authentication middleware**
   - Extracts token from Authorization header
   - Verifies token signature and expiration
   - Checks user role in database
   - Adds user and role information to request object

   ```javascript
   // lib/admin-auth.js
   export const authenticateAdmin = async (req, res, next) => {
     try {
       // Extract token from Authorization header
       const authHeader = req.headers.authorization;
       if (!authHeader || !authHeader.startsWith('Bearer ')) {
         return res.status(401).json({
           error: 'Unauthorized',
           message: 'Missing or invalid Authorization header'
         });
       }

       const token = authHeader.substring(7);

       // Verify token with admin client
       const adminClient = getAdminClient();
       const { data, error } = await adminClient.auth.getUser(token);

       if (error || !data.user) {
         return res.status(401).json({
           error: 'Unauthorized',
           message: 'Invalid authentication token'
         });
       }

       // Check user role
       const { data: roleData, error: roleError } = await adminClient
         .from('user_roles')
         .select('role')
         .eq('id', data.user.id)
         .single();

       if (roleError || !roleData || roleData.role !== 'admin') {
         return res.status(403).json({
           error: 'Forbidden',
           message: 'Insufficient permissions'
         });
       }

       // Add user and role to request object
       req.user = data.user;
       req.role = roleData.role;

       // Continue to the next middleware or route handler
       next();
     } catch (error) {
       console.error('Authentication error:', error);
       return res.status(500).json({
         error: 'Internal Server Error',
         message: 'Authentication process failed'
       });
     }
   }
   ```

4. **Role-based access control**
   - Middleware checks if user has required role for the endpoint
   - If not, returns 403 Forbidden response
   - If yes, passes request to API route handler

5. **API route handler**
   - Processes the authenticated request
   - Performs requested operation
   - Returns response to client

## Token Refresh Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  Auth       │     │  Supabase   │     │  Supabase   │
│  Provider   │     │  Client     │     │  Auth API   │
│             │     │             │     │             │
└──────┬──────┘     └──────┬──────┘     └──────┬──────┘
       │                   │                   │
       │  Check Token      │                   │
       │  Expiration       │                   │
       │ ─────────────────>│                   │
       │                   │                   │
       │                   │  Token Near       │
       │                   │  Expiration       │
       │                   │ ─────────────────>│
       │                   │                   │
       │                   │  Request          │
       │                   │  New Token        │
       │                   │ ─────────────────>│
       │                   │                   │
       │                   │  Return           │
       │                   │  New Token        │
       │                   │ <─────────────────│
       │                   │                   │
       │                   │  Store            │
       │                   │  New Token        │
       │                   │ ─────────────────>│
       │                   │                   │
       │  Token            │                   │
       │  Refreshed        │                   │
       │ <─────────────────│                   │
       │                   │                   │
└──────┴──────┘     └──────┴──────┘     └──────┴──────┘
```

### Step-by-Step Token Refresh Process

1. **Token expiration monitoring**
   - Supabase client monitors token expiration
   - When token is near expiration, initiates refresh

2. **Refresh request**
   - Supabase client sends refresh request to Supabase Auth API
   - Includes refresh token in request

   ```javascript
   // lib/supabase.js
   export const refreshAuthToken = async () => {
     try {
       console.log('Starting token refresh');
       const { data, error } = await supabase.auth.refreshSession();

       if (error) {
         console.error('Token refresh failed:', error);
         return null;
       }

       if (data?.session?.access_token) {
         const token = data.session.access_token;
         console.log('Token refreshed successfully');
         return token;
       } else {
         console.error('No token in refresh response');
         return null;
       }
     } catch (error) {
       console.error('Error refreshing token:', error);
       return null;
     }
   }
   ```

3. **New token generation**
   - Supabase Auth API validates refresh token
   - Generates new JWT token with updated expiration
   - Returns new token to client

4. **Token storage**
   - Supabase client stores new token
   - Updates session information
   - Authentication continues seamlessly

5. **Session persistence**
   - User remains authenticated throughout the process
   - No interruption to user experience

## Logout Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  User       │     │  Client     │     │  Supabase   │
│             │     │             │     │  Auth       │
│             │     │             │     │             │
└──────┬──────┘     └──────┬──────┘     └──────┬──────┘
       │                   │                   │
       │  Click            │                   │
       │  Logout           │                   │
       │ ─────────────────>│                   │
       │                   │                   │
       │                   │  signOut()        │
       │                   │ ─────────────────>│
       │                   │                   │
       │                   │  Invalidate       │
       │                   │  Session          │
       │                   │ ─────────────────>│
       │                   │                   │
       │                   │  Clear            │
       │                   │  Token Storage    │
       │                   │ ─────────────────>│
       │                   │                   │
       │                   │  Redirect to      │
       │                   │  Login Page       │
       │                   │ ─────────────────>│
       │                   │                   │
       │  Redirect to      │                   │
       │  Login Page       │                   │
       │ <─────────────────│                   │
       │                   │                   │
└──────┴──────┘     └──────┴──────┘     └──────┴──────┘
```

### Step-by-Step Logout Process

1. **User initiates logout**
   - User clicks logout button
   - Client calls `signOut()` function

   ```javascript
   // lib/auth.js
   export const signOut = async () => {
     try {
       const { error } = await supabase.auth.signOut();
       return { error };
     } catch (error) {
       console.error('Sign out error:', error);
       return { error };
     }
   }
   ```

2. **Session invalidation**
   - Supabase client sends logout request to Supabase Auth API
   - Session is invalidated on the server

3. **Token removal**
   - Supabase client clears token from storage
   - AuthProvider updates state to reflect logged out status

4. **Redirect to login**
   - User is redirected to login page
   - Protected routes become inaccessible

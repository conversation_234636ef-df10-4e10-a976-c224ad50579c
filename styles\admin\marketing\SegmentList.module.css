.segmentList {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.actions {
  display: flex;
  gap: 10px;
}

.addButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.addButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.searchContainer {
  margin-bottom: 20px;
}

.searchInput {
  width: 100%;
  padding: 10px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background-color: rgba(255, 255, 255, 0.7);
  transition: all 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #6e8efb;
  box-shadow: 0 0 0 2px rgba(110, 142, 251, 0.2);
}

.error {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

.tableContainer {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.segmentTable {
  width: 100%;
  border-collapse: collapse;
}

.segmentTable th {
  text-align: left;
  padding: 12px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.segmentTable th:hover {
  background-color: #eee;
}

.segmentTable td {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  font-size: 0.9rem;
  color: #666;
}

.segmentTable tr:last-child td {
  border-bottom: none;
}

.segmentTable tr:hover td {
  background-color: rgba(110, 142, 251, 0.05);
}

.descriptionCell {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.actions {
  display: flex;
  gap: 8px;
}

.viewButton,
.editButton,
.campaignButton {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.viewButton {
  background-color: #6e8efb;
  color: white;
}

.viewButton:hover {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.editButton {
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
}

.editButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
  transform: translateY(-1px);
}

.campaignButton {
  background-color: transparent;
  color: #4caf50;
  border: 1px solid #4caf50;
}

.campaignButton:hover {
  background-color: rgba(76, 175, 80, 0.1);
  transform: translateY(-1px);
}

.noResults {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
}

.paginationButton {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.paginationButton:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.paginationButton:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.paginationInfo {
  font-size: 0.9rem;
  color: #666;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .actions {
    width: 100%;
  }
  
  .addButton {
    width: 100%;
    justify-content: center;
  }
  
  .segmentTable th:nth-child(2),
  .segmentTable td:nth-child(2) {
    display: none;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .viewButton,
  .editButton,
  .campaignButton {
    text-align: center;
  }
}

import Head from 'next/head'
import { useState, useEffect } from 'react'
import Link from 'next/link'
import styles from '@/styles/Gallery.module.css'
import Layout from '@/components/Layout'
import AnimatedSection from '@/components/AnimatedSection'

export default function Gallery() {
  const [activeCategory, setActiveCategory] = useState('all');
  const [selectedService, setSelectedService] = useState(null);

  // Gallery categories
  const categories = [
    { id: 'all', name: 'All' },
    { id: 'face', name: 'Face Art' },
    { id: 'body', name: 'Body Art' },
    { id: 'glitter', name: 'Glitter & Sparkles' },
    { id: 'uv', name: 'UV Art' },
    { id: 'kids', name: 'Kids Designs' },
    { id: 'events', name: 'Events' }
  ];

  // Gallery images data organized by category
  const galleryData = [
    // Face Art
    {
      id: 'face-art-1',
      title: 'Butterfly Face Art',
      category: 'face',
      mainImage: '/images/gallery/fav/Butterfly.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Butterfly.JPG',
          alt: 'Butterfly Face Art',
          caption: 'Butterfly Face Art'
        }
      ]
    },
    {
      id: 'face-art-2',
      title: 'Gold Leopard',
      category: 'face',
      mainImage: '/images/gallery/fav/Gold Leopard.jpg',
      relatedImages: [
        {
          src: '/images/gallery/fav/Gold Leopard.jpg',
          alt: 'Gold Leopard Face Art',
          caption: 'Gold Leopard Face Art'
        }
      ]
    },
    {
      id: 'face-art-3',
      title: 'Soft Face Design',
      category: 'face',
      mainImage: '/images/gallery/fav/Soft Face.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Soft Face.JPG',
          alt: 'Soft Face Design',
          caption: 'Soft Face Design'
        }
      ]
    },
    {
      id: 'face-art-4',
      title: 'Festival Face Paint',
      category: 'face',
      mainImage: '/images/gallery/fav/Eso Facepaint.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Eso Facepaint.JPG',
          alt: 'Festival Face Paint',
          caption: 'Festival Face Paint'
        }
      ]
    },
    {
      id: 'face-art-5',
      title: 'Splendour Face Paint',
      category: 'face',
      mainImage: '/images/gallery/fav/Splendour Facepaint.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Splendour Facepaint.JPG',
          alt: 'Splendour Face Paint',
          caption: 'Splendour Face Paint'
        }
      ]
    },
    {
      id: 'face-art-6',
      title: 'Adult Soft Clouds',
      category: 'face',
      mainImage: '/images/gallery/fav/Adult Soft Clouds.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Adult Soft Clouds.JPG',
          alt: 'Adult Soft Clouds Face Paint',
          caption: 'Adult Soft Clouds Face Paint'
        }
      ]
    },
    {
      id: 'face-art-7',
      title: 'Adult Rainbow Leopard',
      category: 'face',
      mainImage: '/images/gallery/fav/Adult Rainbow Leopard.jpg',
      relatedImages: [
        {
          src: '/images/gallery/fav/Adult Rainbow Leopard.jpg',
          alt: 'Adult Rainbow Leopard',
          caption: 'Adult Rainbow Leopard'
        }
      ]
    },
    {
      id: 'face-art-8',
      title: 'Camo Face Paint',
      category: 'face',
      mainImage: '/images/gallery/fav/Copy of Camo Bodypaint.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Copy of Camo Bodypaint.JPG',
          alt: 'Camo Face Paint',
          caption: 'Camo Face Paint'
        }
      ]
    },

    // Body Art
    {
      id: 'body-art-1',
      title: 'Adult Body Suit',
      category: 'body',
      mainImage: '/images/gallery/fav/Adult BodySuit.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Adult BodySuit.JPG',
          alt: 'Adult Body Suit',
          caption: 'Adult Body Suit'
        }
      ]
    },
    {
      id: 'body-art-2',
      title: 'Adult Neck & Shoulder',
      category: 'body',
      mainImage: '/images/gallery/fav/Adult Soft Neck & Shoulder.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Adult Soft Neck & Shoulder.JPG',
          alt: 'Adult Neck & Shoulder Art',
          caption: 'Adult Neck & Shoulder Art'
        }
      ]
    },
    {
      id: 'body-art-3',
      title: 'Gold Sparkles Face & Shoulder',
      category: 'body',
      mainImage: '/images/gallery/fav/Gold Sparkles Face & Shoulder.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Gold Sparkles Face & Shoulder.JPG',
          alt: 'Gold Sparkles Face & Shoulder',
          caption: 'Gold Sparkles Face & Shoulder'
        }
      ]
    },
    {
      id: 'body-art-4',
      title: 'Splendour Alien Full Body',
      category: 'body',
      mainImage: '/images/gallery/fav/Splendour Alien Full Body.jpeg',
      relatedImages: [
        {
          src: '/images/gallery/fav/Splendour Alien Full Body.jpeg',
          alt: 'Splendour Alien Full Body',
          caption: 'Splendour Alien Full Body'
        }
      ]
    },
    {
      id: 'body-art-5',
      title: 'Steph Bodysuit',
      category: 'body',
      mainImage: '/images/gallery/fav/Steph Bodysuit.jpeg',
      relatedImages: [
        {
          src: '/images/gallery/fav/Steph Bodysuit.jpeg',
          alt: 'Steph Bodysuit',
          caption: 'Steph Bodysuit'
        }
      ]
    },
    {
      id: 'body-art-6',
      title: 'Mallorca Arm & Face',
      category: 'body',
      mainImage: '/images/gallery/fav/Mallorca Arm & Face.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Mallorca Arm & Face.JPG',
          alt: 'Mallorca Arm & Face',
          caption: 'Mallorca Arm & Face'
        }
      ]
    },

    // Glitter & Sparkles
    {
      id: 'glitter-1',
      title: 'Glitter Beard',
      category: 'glitter',
      mainImage: '/images/gallery/fav/Glitter Beard.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Glitter Beard.JPG',
          alt: 'Glitter Beard',
          caption: 'Glitter Beard'
        }
      ]
    },
    {
      id: 'glitter-2',
      title: 'Blue Sparkles',
      category: 'glitter',
      mainImage: '/images/gallery/fav/Blue Sparkles.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Blue Sparkles.JPG',
          alt: 'Blue Sparkles',
          caption: 'Blue Sparkles'
        }
      ]
    },
    {
      id: 'glitter-3',
      title: 'Adult Coco Sparkles',
      category: 'glitter',
      mainImage: '/images/gallery/fav/Adult Coco Sparkles.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Adult Coco Sparkles.JPG',
          alt: 'Adult Coco Sparkles',
          caption: 'Adult Coco Sparkles'
        }
      ]
    },
    {
      id: 'glitter-4',
      title: 'Gems Packets',
      category: 'glitter',
      mainImage: '/images/gallery/fav/Gems Packets.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Gems Packets.JPG',
          alt: 'Gems Packets',
          caption: 'Gems Packets'
        }
      ]
    },

    // UV Art
    {
      id: 'uv-1',
      title: 'UV Alex',
      category: 'uv',
      mainImage: '/images/gallery/fav/UV Alex.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/UV Alex.JPG',
          alt: 'UV Alex',
          caption: 'UV Alex'
        }
      ]
    },
    {
      id: 'uv-2',
      title: 'UV Alex Progress Shot',
      category: 'uv',
      mainImage: '/images/gallery/fav/UV Alex Progress Shot.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/UV Alex Progress Shot.JPG',
          alt: 'UV Alex Progress Shot',
          caption: 'UV Alex Progress Shot'
        }
      ]
    },
    {
      id: 'uv-3',
      title: 'UV Alien',
      category: 'uv',
      mainImage: '/images/gallery/fav/UV Alien.jpeg',
      relatedImages: [
        {
          src: '/images/gallery/fav/UV Alien.jpeg',
          alt: 'UV Alien',
          caption: 'UV Alien'
        }
      ]
    },
    {
      id: 'uv-4',
      title: 'UV Generic Psychadelia',
      category: 'uv',
      mainImage: '/images/gallery/fav/UV Generic Psychadelia .jpg',
      relatedImages: [
        {
          src: '/images/gallery/fav/UV Generic Psychadelia .jpg',
          alt: 'UV Generic Psychadelia',
          caption: 'UV Generic Psychadelia'
        }
      ]
    },
    {
      id: 'uv-5',
      title: 'UV Jenny',
      category: 'uv',
      mainImage: '/images/gallery/fav/UV Jenny.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/UV Jenny.JPG',
          alt: 'UV Jenny',
          caption: 'UV Jenny'
        }
      ]
    },
    {
      id: 'uv-6',
      title: 'UV Leopard',
      category: 'uv',
      mainImage: '/images/gallery/fav/UV Leopard.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/UV Leopard.JPG',
          alt: 'UV Leopard',
          caption: 'UV Leopard'
        }
      ]
    },
    {
      id: 'uv-7',
      title: 'UV Toby & Vic',
      category: 'uv',
      mainImage: '/images/gallery/fav/UV Toby & Vic.jpg',
      relatedImages: [
        {
          src: '/images/gallery/fav/UV Toby & Vic.jpg',
          alt: 'UV Toby & Vic',
          caption: 'UV Toby & Vic'
        }
      ]
    },

    // Kids Designs
    {
      id: 'kids-1',
      title: 'Kids Butterfly',
      category: 'kids',
      mainImage: '/images/gallery/fav/Kids Butterfly.jpg',
      relatedImages: [
        {
          src: '/images/gallery/fav/Kids Butterfly.jpg',
          alt: 'Kids Butterfly',
          caption: 'Kids Butterfly'
        }
      ]
    },
    {
      id: 'kids-2',
      title: 'Kids Tropical',
      category: 'kids',
      mainImage: '/images/gallery/fav/Kids Tropical.jpg',
      relatedImages: [
        {
          src: '/images/gallery/fav/Kids Tropical.jpg',
          alt: 'Kids Tropical',
          caption: 'Kids Tropical'
        }
      ]
    },
    {
      id: 'kids-3',
      title: 'Toby Rainbow',
      category: 'kids',
      mainImage: '/images/gallery/fav/Toby Rainbow.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Toby Rainbow.JPG',
          alt: 'Toby Rainbow',
          caption: 'Toby Rainbow'
        }
      ]
    },

    // Events
    {
      id: 'events-1',
      title: 'Mallorca Mambos Dancers',
      category: 'events',
      mainImage: '/images/gallery/fav/Mallorca Mambos Dancers.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Mallorca Mambos Dancers.JPG',
          alt: 'Mallorca Mambos Dancers',
          caption: 'Mallorca Mambos Dancers'
        }
      ]
    },
    {
      id: 'events-2',
      title: 'Mallorca Animal Print',
      category: 'events',
      mainImage: '/images/gallery/fav/Mallorca Animal Print.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Mallorca Animal Print.JPG',
          alt: 'Mallorca Animal Print',
          caption: 'Mallorca Animal Print'
        }
      ]
    },
    {
      id: 'events-3',
      title: 'Mallorca Animal Print 2',
      category: 'events',
      mainImage: '/images/gallery/fav/Mallorca Animal Print 2.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/Mallorca Animal Print 2.JPG',
          alt: 'Mallorca Animal Print 2',
          caption: 'Mallorca Animal Print 2'
        }
      ]
    },
    {
      id: 'events-4',
      title: 'OceanSoulVibes Light Box 1',
      category: 'events',
      mainImage: '/images/gallery/fav/OceanSoulVibes Light Box 1.jpeg',
      relatedImages: [
        {
          src: '/images/gallery/fav/OceanSoulVibes Light Box 1.jpeg',
          alt: 'OceanSoulVibes Light Box 1',
          caption: 'OceanSoulVibes Light Box 1'
        }
      ]
    },
    {
      id: 'events-5',
      title: 'OceanSoulVibes Light Box 2',
      category: 'events',
      mainImage: '/images/gallery/fav/OceanSoulVibes Light Box 2.JPG',
      relatedImages: [
        {
          src: '/images/gallery/fav/OceanSoulVibes Light Box 2.JPG',
          alt: 'OceanSoulVibes Light Box 2',
          caption: 'OceanSoulVibes Light Box 2'
        }
      ]
    },
    {
      id: 'events-6',
      title: 'Strawberry Icepoles Camo Facepaint',
      category: 'events',
      mainImage: '/images/gallery/fav/Strawberry Icepoles Camo Facepaint .jpeg',
      relatedImages: [
        {
          src: '/images/gallery/fav/Strawberry Icepoles Camo Facepaint .jpeg',
          alt: 'Strawberry Icepoles Camo Facepaint',
          caption: 'Strawberry Icepoles Camo Facepaint'
        }
      ]
    }
  ];

  // Filter gallery items based on active category
  const filteredGallery = activeCategory === 'all'
    ? galleryData
    : galleryData.filter(item => item.category === activeCategory);

  // Handle clicking on a gallery item to open lightbox
  const handleGalleryItemClick = (item) => {
    setSelectedService(item);
  };

  // Handle closing the lightbox
  const closeLightbox = () => {
    setSelectedService(null);
  };

  // Handle keyboard navigation for lightbox
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!selectedService) return;

      if (e.key === 'Escape') {
        closeLightbox();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedService]);

  return (
    <Layout>
      <Head>
        <title>Gallery | OceanSoulSparkles</title>
        <meta name="description" content="Explore our magical gallery of face painting, festival makeup, airbrush art, braiding, and glitter designs. Get inspired for your next event with OceanSoulSparkles." />
      </Head>

      <main className={styles.main}>
        {/* Gallery Hero Section */}
        <section className={styles.galleryHero}>
          <AnimatedSection animation="fade-in">
            <h1 className={styles.galleryTitle}>Our Magical Gallery</h1>
            <p className={styles.gallerySubtitle}>
              Browse through our collection of creative designs and get inspired for your next sparkly adventure
            </p>
          </AnimatedSection>

          {/* Category Filters */}
          <div className={styles.categoryFilters}>
            {categories.map((category) => (
              <button
                key={category.id}
                className={`${styles.filterButton} ${activeCategory === category.id ? styles.activeFilter : ''}`}
                onClick={() => setActiveCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>
        </section>

        {/* Gallery Grid */}
        <section className={styles.galleryGrid}>
          {filteredGallery.map((item) => (
            <AnimatedSection
              key={item.id}
              animation="fade-in"
              className={styles.galleryItem}
              onClick={() => handleGalleryItemClick(item)}
            >
              <div className={styles.galleryImageContainer}>
                <img src={item.mainImage} alt={item.title} className={styles.galleryImage} />
                <div className={styles.galleryOverlay}>
                  <span className={styles.viewMore}>View Larger</span>
                </div>
              </div>
              <h3 className={styles.galleryItemTitle}>{item.title}</h3>
            </AnimatedSection>
          ))}
        </section>

        {/* Lightbox Only - No Related Images Section */}
        {selectedService && (
          <div className={styles.lightbox} onClick={closeLightbox}>
            <div className={styles.lightboxContent} onClick={(e) => e.stopPropagation()}>
              <img
                src={selectedService.mainImage}
                alt={selectedService.title}
                className={styles.lightboxImage}
              />
              <p className={styles.lightboxCaption}>{selectedService.title}</p>

              <button className={styles.closeButton} onClick={closeLightbox}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 6L6 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>
          </div>
        )}

        {/* Call to Action */}
        <section className={styles.galleryCta}>
          <AnimatedSection animation="fade-up">
            <h2>Ready to bring these magical designs to your event?</h2>
            <p>From face painting to glitter art, we can create the perfect look for your special occasion.</p>
            <div className={styles.ctaButtons}>
              <Link href="/book-online" className={styles.primaryButton}>
                Book Online
              </Link>
              <Link href="/services" className={styles.secondaryButton}>
                View Services
              </Link>
              <Link href="/contact" className={styles.secondaryButton}>
                Contact Us
              </Link>
            </div>
          </AnimatedSection>
        </section>


      </main>
    </Layout>
  )
}

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import styles from '@/styles/admin/marketing/TemplateList.module.css'
import { debounce } from 'lodash'

export default function TemplateList() {
  const router = useRouter()
  const [templates, setTemplates] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [search, setSearch] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [filters, setFilters] = useState({
    template_type: '',
    category: '',
    is_active: ''
  })
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0
  })

  // Initialize filters from URL query params
  useEffect(() => {
    if (router.query.template_type) {
      setFilters(prev => ({ ...prev, template_type: router.query.template_type }))
    }
    if (router.query.category) {
      setFilters(prev => ({ ...prev, category: router.query.category }))
    }
    if (router.query.is_active) {
      setFilters(prev => ({ ...prev, is_active: router.query.is_active }))
    }
  }, [router.query])

  // Debounce search input
  const debouncedSetSearch = useCallback(
    debounce((value) => {
      setDebouncedSearch(value)
    }, 500),
    []
  )

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearch(e.target.value)
    debouncedSetSearch(e.target.value)
  }

  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target
    setFilters(prev => ({ ...prev, [name]: value }))
  }

  // Handle sort change
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  // Render sort indicator
  const renderSortIndicator = (field) => {
    if (sortBy !== field) return null
    return sortOrder === 'asc' ? ' ↑' : ' ↓'
  }

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPagination({ ...pagination, page: newPage })
  }

  // Fetch templates
  const fetchTemplates = async () => {
    setLoading(true)
    setError(null)

    try {
      const offset = (pagination.page - 1) * pagination.limit
      const queryParams = new URLSearchParams({
        limit: pagination.limit,
        offset,
        sort_by: sortBy,
        sort_order: sortOrder
      })

      if (debouncedSearch) {
        queryParams.append('search', debouncedSearch)
      }

      if (filters.template_type) {
        queryParams.append('template_type', filters.template_type)
      }

      if (filters.category) {
        queryParams.append('category', filters.category)
      }

      if (filters.is_active) {
        queryParams.append('is_active', filters.is_active)
      }

      const response = await fetch(`/api/marketing/templates?${queryParams.toString()}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch templates')
      }

      const data = await response.json()
      setTemplates(data.templates || [])
      setPagination({ ...pagination, total: data.total || 0 })
    } catch (error) {
      console.error('Error fetching templates:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  // Fetch templates when dependencies change
  useEffect(() => {
    fetchTemplates()
  }, [debouncedSearch, filters, sortBy, sortOrder, pagination.page, pagination.limit])

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Get category label
  const getCategoryLabel = (categoryValue) => {
    if (!categoryValue) return '-'

    const categories = {
      welcome: 'Welcome',
      promotion: 'Promotion',
      event: 'Event',
      reminder: 'Reminder',
      confirmation: 'Confirmation',
      thank_you: 'Thank You',
      abandoned_cart: 'Abandoned Cart',
      feedback: 'Feedback',
      other: 'Other'
    }

    return categories[categoryValue] || categoryValue
  }

  // Calculate total pages
  const totalPages = Math.ceil(pagination.total / pagination.limit)

  return (
    <AdminLayout>
      <div className={styles.templateList}>
        <div className={styles.header}>
          <h2>Message Templates</h2>
          <div className={styles.actions}>
            <Link href="/admin/marketing/templates/new" className={styles.addButton}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
              Create Template
            </Link>
          </div>
        </div>

        <div className={styles.filters}>
          <div className={styles.searchContainer}>
            <input
              type="text"
              placeholder="Search templates..."
              value={search}
              onChange={handleSearchChange}
              className={styles.searchInput}
            />
          </div>

          <div className={styles.filterControls}>
            <select
              name="template_type"
              value={filters.template_type}
              onChange={handleFilterChange}
              className={styles.filterSelect}
            >
              <option value="">All Types</option>
              <option value="email">Email</option>
              <option value="sms">SMS</option>
              <option value="push">Push</option>
            </select>

            <select
              name="category"
              value={filters.category}
              onChange={handleFilterChange}
              className={styles.filterSelect}
            >
              <option value="">All Categories</option>
              <option value="welcome">Welcome</option>
              <option value="promotion">Promotion</option>
              <option value="event">Event</option>
              <option value="reminder">Reminder</option>
              <option value="confirmation">Confirmation</option>
              <option value="thank_you">Thank You</option>
              <option value="abandoned_cart">Abandoned Cart</option>
              <option value="feedback">Feedback</option>
              <option value="other">Other</option>
            </select>

            <select
              name="is_active"
              value={filters.is_active}
              onChange={handleFilterChange}
              className={styles.filterSelect}
            >
              <option value="">All Status</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>
        </div>

        {error && (
          <div className={styles.error}>
            Error: {error}
          </div>
        )}

        {loading ? (
          <div className={styles.loading}>Loading templates...</div>
        ) : (
          <>
            <div className={styles.templateGrid}>
              {templates.length === 0 ? (
                <div className={styles.noResults}>
                  No templates found. Create your first template to get started.
                </div>
              ) : (
                templates.map((template) => (
                  <div key={template.id} className={styles.templateCard}>
                    <div className={styles.templateHeader}>
                      <div className={styles.templateName}>{template.name}</div>
                      <div className={`${styles.templateStatus} ${template.is_active ? styles.statusActive : styles.statusInactive}`}>
                        {template.is_active ? 'Active' : 'Inactive'}
                      </div>
                    </div>
                    <div className={styles.templateType}>
                      {template.template_type === 'email' ? 'Email Template' :
                       template.template_type === 'sms' ? 'SMS Template' : 'Push Notification Template'}
                    </div>
                    {template.category && (
                      <div className={styles.templateCategory}>
                        Category: {getCategoryLabel(template.category)}
                      </div>
                    )}
                    {template.description && (
                      <div className={styles.templateDescription}>
                        {template.description}
                      </div>
                    )}
                    <div className={styles.templateMeta}>
                      Created: {formatDate(template.created_at)}
                    </div>
                    <div className={styles.templateActions}>
                      <Link href={`/admin/marketing/templates/${template.id}`} className={styles.viewButton}>
                        View
                      </Link>
                      <Link href={`/admin/marketing/templates/${template.id}/edit`} className={styles.editButton}>
                        Edit
                      </Link>
                    </div>
                  </div>
                ))
              )}
            </div>

            {totalPages > 1 && (
              <div className={styles.pagination}>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(1)}
                  disabled={pagination.page === 1}
                >
                  &laquo;
                </button>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                >
                  &lsaquo;
                </button>
                <span className={styles.paginationInfo}>
                  Page {pagination.page} of {totalPages}
                </span>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === totalPages}
                >
                  &rsaquo;
                </button>
                <button
                  className={styles.paginationButton}
                  onClick={() => handlePageChange(totalPages)}
                  disabled={pagination.page === totalPages}
                >
                  &raquo;
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </AdminLayout>
  )
}

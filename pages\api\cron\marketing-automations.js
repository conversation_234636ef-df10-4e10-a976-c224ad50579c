import supabase from '@/lib/supabase';
import { sendOneSignalEmail, sendOneSignalPush } from '@/lib/notifications'

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Verify cron job secret
  const cronSecret = req.headers['x-cron-secret']
  if (cronSecret !== process.env.CRON_SECRET) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  try {
    // Use Supabase client directly
    const client = supabase;

    // Get all active automations with schedule trigger
    const { data: automations, error: automationsError } = await client
      .from('marketing_automations')
      .select(`
        *,
        segment:segment_id (id, name)
      `)
      .eq('is_active', true)
      .eq('trigger_type', 'schedule')

    if (automationsError) {
      throw new Error(`Error fetching automations: ${automationsError.message}`)
    }

    if (!automations || automations.length === 0) {
      return res.status(200).json({ message: 'No scheduled automations to process' })
    }

    // Process each automation
    const results = await Promise.all(
      automations.map(async (automation) => {
        try {
          // Check if this automation should run now based on its schedule
          if (!shouldRunAutomation(automation.trigger_config)) {
            return {
              automation_id: automation.id,
              name: automation.name,
              status: 'skipped',
              reason: 'Not scheduled to run at this time'
            }
          }

          // Get customers from segment
          if (!automation.segment_id) {
            return {
              automation_id: automation.id,
              name: automation.name,
              status: 'failed',
              reason: 'No segment defined for this automation'
            }
          }

          // Get segment preview
          const segmentResponse = await fetch(
            `${process.env.NEXT_PUBLIC_SITE_URL}/api/marketing/segments/${automation.segment_id}?action=preview`,
            { method: 'POST' }
          )

          if (!segmentResponse.ok) {
            const errorData = await segmentResponse.json()
            throw new Error(`Error previewing segment: ${errorData.error}`)
          }

          const segmentData = await segmentResponse.json()
          let customers = segmentData.customers || []

          // Filter customers by marketing consent
          customers = customers.filter(customer => customer.marketing_consent)

          if (customers.length === 0) {
            return {
              automation_id: automation.id,
              name: automation.name,
              status: 'skipped',
              reason: 'No customers with marketing consent found in segment'
            }
          }

          // Send messages to customers
          let sentCount = 0
          let failedCount = 0

          for (const customer of customers) {
            try {
              // Replace personalization tokens in subject and content
              const personalizedSubject = personalizeText(automation.subject, customer)
              const personalizedContent = personalizeText(automation.content, customer)

              if (automation.message_type === 'email') {
                // Send email via OneSignal
                if (!customer.email) continue

                await sendOneSignalEmail({
                  email: customer.email,
                  subject: personalizedSubject,
                  message: personalizedContent,
                  htmlBody: personalizedContent,
                  data: {
                    type: 'marketing_automation',
                    automation_id: automation.id
                  }
                })
              } else if (automation.message_type === 'sms') {
                // Send SMS
                if (!customer.phone) continue

                // In a real implementation, this would use an SMS service
                // For now, we'll just log it
                console.log(`SMS would be sent to ${customer.phone}:`, personalizedContent)
              } else if (automation.message_type === 'push') {
                // Send push notification
                await sendOneSignalPush({
                  userIds: [customer.id],
                  title: personalizedSubject || 'New notification',
                  message: personalizedContent,
                  data: {
                    type: 'marketing_automation',
                    automation_id: automation.id
                  }
                })
              }

              // Log successful execution
              await client
                .from('automation_logs')
                .insert([
                  {
                    automation_id: automation.id,
                    trigger_event: 'schedule',
                    customer_id: customer.id,
                    status: 'success'
                  }
                ])

              sentCount++
            } catch (error) {
              console.error(`Error sending message to customer ${customer.id}:`, error)

              // Log failed execution
              await supabase
                .from('automation_logs')
                .insert([
                  {
                    automation_id: automation.id,
                    trigger_event: 'schedule',
                    customer_id: customer.id,
                    status: 'failed',
                    message: error.message
                  }
                ])

              failedCount++
            }
          }

          return {
            automation_id: automation.id,
            name: automation.name,
            status: 'completed',
            sent: sentCount,
            failed: failedCount,
            total: customers.length
          }
        } catch (error) {
          console.error(`Error processing automation ${automation.id}:`, error)
          return {
            automation_id: automation.id,
            name: automation.name,
            status: 'failed',
            error: error.message
          }
        }
      })
    )

    return res.status(200).json({
      success: true,
      processed: results.length,
      results
    })
  } catch (error) {
    console.error('Error processing marketing automations:', error)
    return res.status(500).json({ error: 'Failed to process marketing automations' })
  }
}

// Helper function to check if an automation should run based on its schedule
function shouldRunAutomation(triggerConfig) {
  if (!triggerConfig || !triggerConfig.frequency) {
    return false
  }

  const now = new Date()
  const currentHour = now.getHours()
  const currentDay = now.getDay() // 0 = Sunday, 1 = Monday, etc.
  const currentDate = now.getDate()
  const currentMonth = now.getMonth() + 1 // 1-12

  switch (triggerConfig.frequency) {
    case 'daily':
      // Run daily at specified hour (default: 9 AM)
      return currentHour === (triggerConfig.hour || 9)

    case 'weekly':
      // Run weekly on specified day (default: Monday) at specified hour (default: 9 AM)
      return currentDay === (triggerConfig.day_of_week || 1) && currentHour === (triggerConfig.hour || 9)

    case 'monthly':
      // Run monthly on specified date (default: 1st) at specified hour (default: 9 AM)
      return currentDate === (triggerConfig.day_of_month || 1) && currentHour === (triggerConfig.hour || 9)

    case 'birthday':
      // Run on customer birthdays (handled separately in a more complex implementation)
      // For now, we'll run this daily at 9 AM and filter customers by birthday in the query
      return currentHour === 9

    case 'custom':
      // Custom schedule with specific dates/times
      // This would require a more complex implementation
      return false

    default:
      return false
  }
}

// Helper function to replace personalization tokens in text
function personalizeText(text, customer) {
  if (!text) return ''

  return text
    .replace(/\{name\}/g, customer.name || 'Customer')
    .replace(/\{first_name\}/g, customer.name ? customer.name.split(' ')[0] : 'Customer')
    .replace(/\{email\}/g, customer.email || '')
    .replace(/\{phone\}/g, customer.phone || '')
    .replace(/\{city\}/g, customer.city || '')
    .replace(/\{state\}/g, customer.state || '')
}

import React from 'react';
import { useRouter } from 'next/router';
import OrganizationSchema from './OrganizationSchema';
import LocalBusinessSchema from './LocalBusinessSchema';
import BreadcrumbSchema from './BreadcrumbSchema';

const SchemaManager = () => {
  const router = useRouter();
  const path = router.asPath;
  
  return (
    <>
      <OrganizationSchema />
      <LocalBusinessSchema />
      <BreadcrumbSchema />
    </>
  );
};

export default SchemaManager;

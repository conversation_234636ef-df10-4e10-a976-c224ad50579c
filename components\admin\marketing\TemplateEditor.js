import { useState, useEffect } from 'react'
import styles from '@/styles/admin/marketing/TemplateEditor.module.css'

export default function TemplateEditor({
  initialTemplate = null,
  onSave,
  onCancel,
  readOnly = false
}) {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [subject, setSubject] = useState('')
  const [content, setContent] = useState('')
  const [templateType, setTemplateType] = useState('email')
  const [category, setCategory] = useState('')
  const [isActive, setIsActive] = useState(true)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [showPersonalizationMenu, setShowPersonalizationMenu] = useState(false)
  const [previewCustomer, setPreviewCustomer] = useState({
    name: '<PERSON>',
    first_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '0412 345 678',
    city: 'Sydney',
    state: 'NSW'
  })

  // Initialize form with initial template data
  useEffect(() => {
    if (initialTemplate) {
      setName(initialTemplate.name || '')
      setDescription(initialTemplate.description || '')
      setSubject(initialTemplate.subject || '')
      setContent(initialTemplate.content || '')
      setTemplateType(initialTemplate.template_type || 'email')
      setCategory(initialTemplate.category || '')
      setIsActive(initialTemplate.is_active !== undefined ? initialTemplate.is_active : true)
    }
  }, [initialTemplate])

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // Validate form
      if (!name) {
        throw new Error('Template name is required')
      }

      if (!content) {
        throw new Error('Template content is required')
      }

      if (templateType === 'email' && !subject) {
        throw new Error('Subject is required for email templates')
      }

      // Call onSave callback
      await onSave({
        name,
        description,
        subject,
        content,
        template_type: templateType,
        category,
        is_active: isActive
      })
    } catch (error) {
      console.error('Error saving template:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  // Insert personalization token at cursor position
  const insertPersonalizationToken = (token) => {
    const textarea = document.getElementById('template-content')
    if (!textarea) return

    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd
    const textBefore = content.substring(0, startPos)
    const textAfter = content.substring(endPos, content.length)
    
    setContent(textBefore + token + textAfter)
    setShowPersonalizationMenu(false)
    
    // Focus back on textarea and set cursor position after the inserted token
    setTimeout(() => {
      textarea.focus()
      textarea.selectionStart = startPos + token.length
      textarea.selectionEnd = startPos + token.length
    }, 0)
  }

  // Preview personalized content
  const previewPersonalizedContent = (text) => {
    if (!text) return ''
    
    return text
      .replace(/\{name\}/g, previewCustomer.name)
      .replace(/\{first_name\}/g, previewCustomer.first_name)
      .replace(/\{email\}/g, previewCustomer.email)
      .replace(/\{phone\}/g, previewCustomer.phone)
      .replace(/\{city\}/g, previewCustomer.city)
      .replace(/\{state\}/g, previewCustomer.state)
  }

  // Get category options
  const categoryOptions = [
    { value: 'welcome', label: 'Welcome' },
    { value: 'promotion', label: 'Promotion' },
    { value: 'event', label: 'Event' },
    { value: 'reminder', label: 'Reminder' },
    { value: 'confirmation', label: 'Confirmation' },
    { value: 'thank_you', label: 'Thank You' },
    { value: 'abandoned_cart', label: 'Abandoned Cart' },
    { value: 'feedback', label: 'Feedback' },
    { value: 'other', label: 'Other' }
  ]

  return (
    <div className={styles.templateEditor}>
      {error && (
        <div className={styles.error}>
          Error: {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className={styles.formSection}>
          <h3>Template Details</h3>
          
          <div className={styles.formGroup}>
            <label htmlFor="template-name">Template Name *</label>
            <input
              type="text"
              id="template-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="e.g., Welcome Email"
              className={styles.input}
              disabled={loading || readOnly}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="template-description">Description</label>
            <textarea
              id="template-description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe the purpose of this template"
              className={styles.textarea}
              disabled={loading || readOnly}
              rows={2}
            />
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="template-type">Template Type *</label>
              <select
                id="template-type"
                value={templateType}
                onChange={(e) => setTemplateType(e.target.value)}
                className={styles.select}
                disabled={loading || readOnly}
                required
              >
                <option value="email">Email</option>
                <option value="sms">SMS</option>
                <option value="push">Push Notification</option>
              </select>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="template-category">Category</label>
              <select
                id="template-category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className={styles.select}
                disabled={loading || readOnly}
              >
                <option value="">Select a category</option>
                {categoryOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className={styles.formGroup}>
            <div className={styles.checkboxGroup}>
              <input
                type="checkbox"
                id="template-active"
                checked={isActive}
                onChange={(e) => setIsActive(e.target.checked)}
                disabled={loading || readOnly}
                className={styles.checkbox}
              />
              <label htmlFor="template-active">Active</label>
            </div>
            <div className={styles.helpText}>
              Inactive templates cannot be applied to campaigns
            </div>
          </div>
        </div>

        <div className={styles.formSection}>
          <h3>Template Content</h3>

          {templateType === 'email' && (
            <div className={styles.formGroup}>
              <label htmlFor="template-subject">Subject *</label>
              <div className={styles.inputWithButton}>
                <input
                  type="text"
                  id="template-subject"
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                  placeholder="Enter email subject"
                  className={styles.input}
                  disabled={loading || readOnly}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPersonalizationMenu(!showPersonalizationMenu)}
                  disabled={loading || readOnly}
                  className={styles.personalizationButton}
                >
                  Add Personalization
                </button>
              </div>
            </div>
          )}

          <div className={styles.formGroup}>
            <div className={styles.contentHeader}>
              <label htmlFor="template-content">Content *</label>
              {!showPersonalizationMenu && (
                <button
                  type="button"
                  onClick={() => setShowPersonalizationMenu(true)}
                  disabled={loading || readOnly}
                  className={styles.personalizationButton}
                >
                  Add Personalization
                </button>
              )}
            </div>
            {showPersonalizationMenu && (
              <div className={styles.personalizationMenu}>
                <div className={styles.personalizationHeader}>
                  <h4>Insert Personalization Token</h4>
                  <button
                    type="button"
                    onClick={() => setShowPersonalizationMenu(false)}
                    className={styles.closeButton}
                  >
                    ×
                  </button>
                </div>
                <div className={styles.personalizationTokens}>
                  <button type="button" onClick={() => insertPersonalizationToken('{name}')}>
                    Full Name
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{first_name}')}>
                    First Name
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{email}')}>
                    Email
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{phone}')}>
                    Phone
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{city}')}>
                    City
                  </button>
                  <button type="button" onClick={() => insertPersonalizationToken('{state}')}>
                    State
                  </button>
                </div>
              </div>
            )}
            <textarea
              id="template-content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder={`Enter template content${templateType === 'email' ? ' (HTML supported)' : ''}`}
              className={styles.textarea}
              disabled={loading || readOnly}
              rows={10}
              required
            />
            <div className={styles.helpText}>
              {templateType === 'email' 
                ? 'HTML is supported. Use personalization tokens to make your emails more personal.' 
                : 'Keep SMS messages concise. Standard SMS messages are limited to 160 characters.'}
            </div>
          </div>
        </div>

        {!readOnly && (
          <div className={styles.formActions}>
            <button
              type="button"
              onClick={onCancel}
              disabled={loading}
              className={styles.cancelButton}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className={styles.saveButton}
            >
              {loading ? 'Saving...' : initialTemplate ? 'Update Template' : 'Create Template'}
            </button>
          </div>
        )}
      </form>

      {(subject || content) && (
        <div className={styles.preview}>
          <h3>Preview</h3>
          {subject && templateType === 'email' && (
            <div className={styles.previewSubject}>
              <strong>Subject:</strong> {previewPersonalizedContent(subject)}
            </div>
          )}
          {content && (
            <div className={styles.previewContent}>
              <strong>Content:</strong>
              <div 
                className={`${styles.previewContentText} ${templateType === 'email' ? styles.emailPreview : ''}`}
              >
                {templateType === 'email' ? (
                  <div dangerouslySetInnerHTML={{ __html: previewPersonalizedContent(content) }} />
                ) : (
                  previewPersonalizedContent(content).split('\n').map((line, i) => (
                    <p key={i}>{line}</p>
                  ))
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

# API Integration Testing Guide for OCEANSOULSPARKLES

This directory contains integration tests for the OCEANSOULSPARKLES API endpoints. These tests use a real Supabase test database instead of mocks, providing more reliable validation of API functionality.

## Test Structure

The tests are organized by API functionality:

- `customers.test.js`: Tests for listing and creating customers
- `customer-operations.test.js`: Tests for updating, deleting, and GDPR operations
- `customer-export.test.js`: Tests for exporting customer data

## Setup Requirements

### 1. Create a Test Database

Create a dedicated test database in Supabase for running these tests. This ensures that your tests don't affect your development or production data.

1. Create a new project in Supabase (e.g., "oceansoulsparkles-test")
2. Set up the same database schema as your development environment
3. Get the API URL, anon key, and service role key

### 2. Configure Environment Variables

Create a `.env.test` file based on the `.env.test.example` template:

```bash
cp .env.test.example .env.test
```

Then edit the `.env.test` file to add your Supabase test database credentials.

## Running the Tests

To run all API integration tests:

```bash
npm test -- __tests__/api-integration
```

To run a specific test file:

```bash
npm test -- __tests__/api-integration/customers.test.js
```

To run tests in watch mode:

```bash
npm test -- __tests__/api-integration --watch
```

## Test Methodology

These tests use the following approach:

1. **Real Database**: Tests interact with a real Supabase test database
2. **Test Users**: Creates temporary admin and staff users for testing
3. **Test Data**: Creates temporary test data that is cleaned up after tests
4. **HTTP Requests**: Makes actual HTTP requests to the API endpoints
5. **Authentication**: Uses real Supabase authentication

## Test Helpers

The tests use helper functions from `__tests__/utils/api-test-helpers.js`:

- `createTestUser()`: Creates a test user with the specified role
- `signInTestUser()`: Signs in as a test user
- `createTestCustomer()`: Creates a test customer
- `makeAuthenticatedRequest()`: Makes an authenticated API request
- `cleanupTestData()`: Cleans up test data after tests

## Best Practices

When writing new tests:

1. **Isolation**: Each test should be independent and not rely on data created by other tests
2. **Cleanup**: Always clean up test data after tests
3. **Descriptive Names**: Use descriptive test names that explain what is being tested
4. **Assertions**: Make specific assertions about the response status, headers, and data
5. **Error Cases**: Test both success and error cases

## Troubleshooting

If tests are failing, check:

1. **Database Connection**: Ensure your Supabase test database is accessible
2. **Environment Variables**: Verify that `.env.test` has the correct credentials
3. **API Implementation**: Check that the API endpoints match the expected behavior
4. **Test Data**: Ensure test data is being created and cleaned up correctly
5. **Authentication**: Verify that authentication is working correctly

## Adding New Tests

When adding new API endpoints or modifying existing ones:

1. Create a new test file if needed, following the naming pattern `__tests__/api-integration/[feature].test.js`
2. Import the test helpers
3. Structure tests with describe blocks for different aspects of the API
4. Create test users and data in the `beforeAll` hook
5. Clean up test data in the `afterAll` hook
6. Test both success and error cases

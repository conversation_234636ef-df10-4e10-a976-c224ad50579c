.dashboard {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.header h2 {
  margin: 0 0 20px 0;
  font-size: 1.5rem;
  color: #333;
}

.periodSelector {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.periodButton {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.7);
  color: #666;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.periodButton:hover {
  background-color: rgba(255, 255, 255, 0.9);
}

.periodButtonActive {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border-color: transparent;
}

.periodButtonActive:hover {
  background: linear-gradient(135deg, #5a7df9, #9666d1);
}

.dateRangeSelector {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 20px;
}

.dateInputGroup {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dateInputGroup label {
  font-size: 0.9rem;
  color: #666;
}

.dateInput {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  background-color: rgba(255, 255, 255, 0.7);
}

.applyButton {
  padding: 8px 16px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.applyButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a7df9, #9666d1);
  transform: translateY(-1px);
}

.applyButton:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.dashboardContent {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.analyticsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.analyticsCard {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.quickLinks {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.quickLinks h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
}

.linksGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.linkCard {
  display: flex;
  align-items: center;
  gap: 16px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  padding: 16px;
  text-decoration: none;
  transition: all 0.2s ease;
}

.linkCard:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.linkIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border-radius: 8px;
}

.linkText h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  color: #333;
}

.linkText p {
  margin: 0;
  font-size: 0.85rem;
  color: #666;
}

@media (max-width: 992px) {
  .analyticsGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .periodSelector {
    flex-wrap: wrap;
  }
  
  .periodButton {
    flex: 1;
    text-align: center;
  }
  
  .dateRangeSelector {
    flex-direction: column;
    align-items: stretch;
  }
  
  .dateInputGroup {
    width: 100%;
  }
  
  .dateInput {
    flex: 1;
  }
  
  .applyButton {
    width: 100%;
  }
}

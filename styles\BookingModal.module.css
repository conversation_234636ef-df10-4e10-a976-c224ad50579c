.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.modal {
  background-color: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.closeButton {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 28px;
  color: #333;
  cursor: pointer;
  z-index: 10;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(1.1);
}

.modalContent {
  padding: 0 0 20px 0;
}

.modalHeader {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #eee;
  margin-bottom: 20px;
  position: relative;
}

.rainbowText {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #ffe66d, #ff6b6b);
  background-size: 300% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: rainbow 6s ease infinite;
  letter-spacing: 1px;
}

@keyframes rainbow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.serviceInfo {
  padding: 0 30px;
  margin-bottom: 30px;
}

.serviceInfo h2 {
  font-size: 1.8rem;
  margin-bottom: 10px;
  color: #333;
}

.serviceInfo p {
  color: #666;
  font-size: 1.1rem;
}

.optionsSection {
  padding: 0 30px;
  margin-bottom: 30px;
}

.optionsSection h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: #444;
}

.selectWrapper {
  position: relative;
  width: 100%;
}

.optionSelect {
  width: 100%;
  padding: 12px 15px;
  font-size: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  appearance: none;
  background-color: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.optionSelect:focus {
  outline: none;
  border-color: #4ecdc4;
  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);
}

.selectArrow {
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #666;
  pointer-events: none;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  padding: 20px 30px 0;
  border-top: 1px solid #eee;
  margin-top: 20px;
  gap: 15px;
}

.nextButton, .backButton, .submitButton, .closeSuccessButton {
  padding: 12px 24px;
  border-radius: 30px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.nextButton {
  background-color: #4ecdc4;
  color: white;
}

.nextButton:hover {
  background-color: #3dbeb6;
  transform: translateY(-2px);
}

.nextButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
}

.backButton {
  background-color: #f8f8f8;
  color: #333;
}

.backButton:hover {
  background-color: #eee;
}

.submitButton {
  background-color: #ff6b6b;
  color: white;
}

.submitButton:hover {
  background-color: #ff5252;
  transform: translateY(-2px);
}

.submitButton:disabled {
  background-color: #ffb3b3;
  cursor: not-allowed;
  transform: none;
}

.backButton:disabled {
  background-color: #f0f0f0;
  color: #999;
  cursor: not-allowed;
  transform: none;
}

.closeSuccessButton {
  background-color: #4ecdc4;
  color: white;
  margin-top: 20px;
}

.closeSuccessButton:hover {
  background-color: #3dbeb6;
  transform: translateY(-2px);
}

/* Form Styles */
.bookingForm {
  padding: 0 30px;
}

.formGroup {
  margin-bottom: 20px;
}

.formRow {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.formRow .formGroup {
  flex: 1;
  margin-bottom: 0;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #444;
}

.formGroup input,
.formGroup textarea,
.formGroup select {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.formGroup input:focus,
.formGroup textarea:focus,
.formGroup select:focus {
  outline: none;
  border-color: #4ecdc4;
  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);
}

.inputError {
  border-color: #ff6b6b !important;
}

.errorText {
  color: #ff6b6b;
  font-size: 0.85rem;
  margin-top: 5px;
}

.errorMessage {
  color: #ff6b6b;
  font-size: 0.95rem;
  margin: 10px 30px 0;
  padding: 10px 15px;
  background-color: rgba(255, 107, 107, 0.1);
  border-radius: 8px;
  border-left: 3px solid #ff6b6b;
  text-align: center;
}

.checkboxGroup {
  display: flex;
  align-items: center;
  gap: 10px;
}

.checkboxGroup input {
  width: auto;
}

/* Confirmation Styles */
.confirmationDetails {
  padding: 0 30px;
}

.confirmationDetails h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: #333;
}

.summaryItem {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.summaryItem span:first-child {
  font-weight: 600;
  color: #444;
}

.summaryItem span:last-child {
  color: #666;
  text-align: right;
  max-width: 60%;
}

.bookingNote {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 8px;
}

.bookingNote p {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Success Message Styles */
.successMessage {
  text-align: center;
  padding: 40px 30px;
}

.successIcon {
  width: 80px;
  height: 80px;
  background-color: #4ecdc4;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  margin: 0 auto 20px;
}

.successMessage h2 {
  font-size: 2rem;
  margin-bottom: 15px;
  color: #333;
}

.successMessage p {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  max-width: 400px;
  margin: 0 auto 30px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .modal {
    width: 95%;
  }

  .formRow {
    flex-direction: column;
    gap: 20px;
  }

  .rainbowText {
    font-size: 1.3rem;
  }

  .serviceInfo h2 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .modalHeader {
    padding: 15px;
  }

  .serviceInfo, .optionsSection, .bookingForm, .confirmationDetails {
    padding: 0 20px;
  }

  .modalFooter {
    padding: 15px 20px 0;
  }

  .rainbowText {
    font-size: 1.1rem;
  }
}

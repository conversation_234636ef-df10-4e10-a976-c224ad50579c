import { useState, useEffect } from 'react'
import styles from '@/styles/admin/marketing/TriggerBuilder.module.css'

export default function TriggerBuilder({
  initialTriggerType = 'event',
  initialTriggerConfig = {},
  onChange,
  readOnly = false
}) {
  const [triggerType, setTriggerType] = useState(initialTriggerType)
  const [triggerConfig, setTriggerConfig] = useState(initialTriggerConfig)

  // Update parent component when trigger type or config changes
  useEffect(() => {
    if (onChange) {
      onChange({
        trigger_type: triggerType,
        trigger_config: triggerConfig
      })
    }
  }, [triggerType, triggerConfig, onChange])

  // Initialize with initial values
  useEffect(() => {
    setTriggerType(initialTriggerType)
    setTriggerConfig(initialTriggerConfig)
  }, [initialTriggerType, initialTriggerConfig])

  // Handle trigger type change
  const handleTriggerTypeChange = (e) => {
    const newTriggerType = e.target.value
    setTriggerType(newTriggerType)
    
    // Reset trigger config when type changes
    switch (newTriggerType) {
      case 'event':
        setTriggerConfig({ event_type: 'new_booking' })
        break
      case 'schedule':
        setTriggerConfig({ frequency: 'daily', hour: 9 })
        break
      case 'segment_entry':
        setTriggerConfig({ delay_hours: 24 })
        break
      default:
        setTriggerConfig({})
    }
  }

  // Handle trigger config change
  const handleConfigChange = (e) => {
    const { name, value } = e.target
    
    // Handle numeric values
    if (name === 'hour' || name === 'day_of_week' || name === 'day_of_month' || name === 'delay_hours') {
      setTriggerConfig({
        ...triggerConfig,
        [name]: parseInt(value, 10)
      })
    } else {
      setTriggerConfig({
        ...triggerConfig,
        [name]: value
      })
    }
  }

  // Render event trigger configuration
  const renderEventTriggerConfig = () => {
    return (
      <div className={styles.configSection}>
        <div className={styles.formGroup}>
          <label htmlFor="event-type">Event Type</label>
          <select
            id="event-type"
            name="event_type"
            value={triggerConfig.event_type || 'new_booking'}
            onChange={handleConfigChange}
            className={styles.select}
            disabled={readOnly}
          >
            <option value="new_booking">New Booking</option>
            <option value="booking_confirmed">Booking Confirmed</option>
            <option value="booking_completed">Booking Completed</option>
            <option value="new_customer">New Customer</option>
            <option value="product_purchased">Product Purchased</option>
            <option value="abandoned_cart">Abandoned Cart</option>
          </select>
        </div>
        
        {triggerConfig.event_type === 'product_purchased' && (
          <div className={styles.formGroup}>
            <label htmlFor="product-category">Product Category (optional)</label>
            <select
              id="product-category"
              name="product_category"
              value={triggerConfig.product_category || ''}
              onChange={handleConfigChange}
              className={styles.select}
              disabled={readOnly}
            >
              <option value="">Any Category</option>
              <option value="glitter">Glitter</option>
              <option value="face_paint">Face Paint</option>
              <option value="uv_products">UV Products</option>
              <option value="accessories">Accessories</option>
            </select>
          </div>
        )}
        
        <div className={styles.formGroup}>
          <label htmlFor="delay-hours">Delay (hours)</label>
          <input
            type="number"
            id="delay-hours"
            name="delay_hours"
            value={triggerConfig.delay_hours || 0}
            onChange={handleConfigChange}
            min="0"
            max="168"
            className={styles.input}
            disabled={readOnly}
          />
          <div className={styles.helpText}>
            Number of hours to wait after the event before sending the message. Use 0 for immediate sending.
          </div>
        </div>
      </div>
    )
  }

  // Render schedule trigger configuration
  const renderScheduleTriggerConfig = () => {
    return (
      <div className={styles.configSection}>
        <div className={styles.formGroup}>
          <label htmlFor="frequency">Frequency</label>
          <select
            id="frequency"
            name="frequency"
            value={triggerConfig.frequency || 'daily'}
            onChange={handleConfigChange}
            className={styles.select}
            disabled={readOnly}
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="birthday">Customer Birthday</option>
          </select>
        </div>
        
        {triggerConfig.frequency === 'weekly' && (
          <div className={styles.formGroup}>
            <label htmlFor="day-of-week">Day of Week</label>
            <select
              id="day-of-week"
              name="day_of_week"
              value={triggerConfig.day_of_week || 1}
              onChange={handleConfigChange}
              className={styles.select}
              disabled={readOnly}
            >
              <option value={0}>Sunday</option>
              <option value={1}>Monday</option>
              <option value={2}>Tuesday</option>
              <option value={3}>Wednesday</option>
              <option value={4}>Thursday</option>
              <option value={5}>Friday</option>
              <option value={6}>Saturday</option>
            </select>
          </div>
        )}
        
        {triggerConfig.frequency === 'monthly' && (
          <div className={styles.formGroup}>
            <label htmlFor="day-of-month">Day of Month</label>
            <select
              id="day-of-month"
              name="day_of_month"
              value={triggerConfig.day_of_month || 1}
              onChange={handleConfigChange}
              className={styles.select}
              disabled={readOnly}
            >
              {Array.from({ length: 31 }, (_, i) => (
                <option key={i + 1} value={i + 1}>
                  {i + 1}
                </option>
              ))}
            </select>
          </div>
        )}
        
        {triggerConfig.frequency !== 'birthday' && (
          <div className={styles.formGroup}>
            <label htmlFor="hour">Hour of Day</label>
            <select
              id="hour"
              name="hour"
              value={triggerConfig.hour || 9}
              onChange={handleConfigChange}
              className={styles.select}
              disabled={readOnly}
            >
              {Array.from({ length: 24 }, (_, i) => (
                <option key={i} value={i}>
                  {i.toString().padStart(2, '0')}:00
                </option>
              ))}
            </select>
          </div>
        )}
        
        {triggerConfig.frequency === 'birthday' && (
          <div className={styles.formGroup}>
            <label htmlFor="days-before">Days Before Birthday</label>
            <select
              id="days-before"
              name="days_before"
              value={triggerConfig.days_before || 0}
              onChange={handleConfigChange}
              className={styles.select}
              disabled={readOnly}
            >
              <option value={0}>On Birthday</option>
              <option value={1}>1 Day Before</option>
              <option value={2}>2 Days Before</option>
              <option value={3}>3 Days Before</option>
              <option value={7}>1 Week Before</option>
              <option value={14}>2 Weeks Before</option>
            </select>
          </div>
        )}
      </div>
    )
  }

  // Render segment entry trigger configuration
  const renderSegmentEntryTriggerConfig = () => {
    return (
      <div className={styles.configSection}>
        <div className={styles.formGroup}>
          <label htmlFor="delay-hours">Delay After Entry (hours)</label>
          <input
            type="number"
            id="delay-hours"
            name="delay_hours"
            value={triggerConfig.delay_hours || 24}
            onChange={handleConfigChange}
            min="0"
            max="168"
            className={styles.input}
            disabled={readOnly}
          />
          <div className={styles.helpText}>
            Number of hours to wait after a customer enters the segment before sending the message. Use 0 for immediate sending.
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.triggerBuilder}>
      <div className={styles.triggerTypeSection}>
        <div className={styles.formGroup}>
          <label htmlFor="trigger-type">Trigger Type</label>
          <select
            id="trigger-type"
            value={triggerType}
            onChange={handleTriggerTypeChange}
            className={styles.select}
            disabled={readOnly}
          >
            <option value="event">Event-Based</option>
            <option value="schedule">Scheduled</option>
            <option value="segment_entry">Segment Entry</option>
          </select>
          <div className={styles.helpText}>
            {triggerType === 'event' && 'Send messages when specific events occur, such as new bookings or purchases.'}
            {triggerType === 'schedule' && 'Send messages on a regular schedule, such as daily, weekly, or monthly.'}
            {triggerType === 'segment_entry' && 'Send messages when customers enter a specific segment.'}
          </div>
        </div>
      </div>

      {triggerType === 'event' && renderEventTriggerConfig()}
      {triggerType === 'schedule' && renderScheduleTriggerConfig()}
      {triggerType === 'segment_entry' && renderSegmentEntryTriggerConfig()}
    </div>
  )
}

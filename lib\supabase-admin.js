/**
 * Supabase Admin Client
 * 
 * This module provides a consistent Supabase admin client for server-side operations
 * that require bypassing RLS policies.
 */

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Default headers for all requests
const DEFAULT_HEADERS = {
  'X-Client-Info': 'ocean-soul-sparkles-admin@1.0.0',
  'X-Protocol-Version': '1.0'
};

/**
 * Custom fetch implementation with timeout
 * Prevents requests from hanging indefinitely
 */
// Preserve existing fetch pattern with enhanced error handling
const fetchWithTimeout = async (resource, options = {}) => {
  const controller = new AbortController();
  const timeout = options.timeout || 15000; // Increased to 15s for complex ops
  let retryCount = 0;

  const id = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(resource, {
      ...options,
      signal: controller.signal
    });

    return response;
  } finally {
    clearTimeout(id);
  }
};

/**
 * Supabase admin client with service role key
 * This bypasses RLS policies and should only be used server-side
 */
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: true, // Enable token refresh
    persistSession: true, // Maintain session consistency
    detectSessionInUrl: false // Disable URL session detection
  },
  global: {
    headers: DEFAULT_HEADERS,
    fetch: fetchWithTimeout
  }
});

// Default export for convenience
export default supabaseAdmin;

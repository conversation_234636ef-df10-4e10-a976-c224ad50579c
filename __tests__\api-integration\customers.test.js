/**
 * Integration tests for Customer API endpoints
 * 
 * These tests use a real Supabase test database instead of mocks.
 * They require proper environment variables in .env.test
 */

import {
  createTestUser,
  createTestCustomer,
  signInTestUser,
  cleanupTestData,
  makeAuthenticatedRequest
} from '../utils/api-test-helpers';

// Test data to clean up
const testData = {
  customerIds: [],
  userIds: []
};

// Base URL for API requests
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

describe('Customer API Integration Tests', () => {
  let adminUser;
  let adminSession;
  let staffUser;
  let staffSession;
  
  // Set up test users before all tests
  beforeAll(async () => {
    // Create admin user
    adminUser = await createTestUser('admin');
    testData.userIds.push(adminUser.id);
    
    // Create staff user
    staffUser = await createTestUser('staff');
    testData.userIds.push(staffUser.id);
    
    // Sign in as admin
    const adminAuth = await signInTestUser({
      email: adminUser.email,
      password: adminUser.password
    });
    adminSession = adminAuth.session;
    
    // Sign in as staff
    const staffAuth = await signInTestUser({
      email: staffUser.email,
      password: staffUser.password
    });
    staffSession = staffAuth.session;
  }, 30000); // Increase timeout for user creation
  
  // Clean up test data after all tests
  afterAll(async () => {
    await cleanupTestData(testData);
  }, 10000);
  
  describe('GET /api/customers', () => {
    beforeAll(async () => {
      // Create test customers
      for (let i = 0; i < 3; i++) {
        const customer = await createTestCustomer({
          name: `List Test Customer ${i}`,
          city: i % 2 === 0 ? 'Sydney' : 'Melbourne'
        });
        testData.customerIds.push(customer.id);
      }
    }, 10000);
    
    it('should return a list of customers for admin users', async () => {
      // Make request as admin
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers`,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(response.data.customers).toBeDefined();
      expect(response.data.total).toBeDefined();
      expect(Array.isArray(response.data.customers)).toBe(true);
      expect(response.data.customers.length).toBeGreaterThan(0);
    });
    
    it('should return a list of customers for staff users', async () => {
      // Make request as staff
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers`,
        session: staffSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(response.data.customers).toBeDefined();
      expect(Array.isArray(response.data.customers)).toBe(true);
    });
    
    it('should filter customers by search term', async () => {
      // Make request with search parameter
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers?search=List Test`,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(response.data.customers).toBeDefined();
      expect(Array.isArray(response.data.customers)).toBe(true);
      expect(response.data.customers.length).toBeGreaterThan(0);
      
      // Verify all returned customers match the search term
      response.data.customers.forEach(customer => {
        expect(customer.name.includes('List Test') || 
               customer.email.includes('List Test') || 
               (customer.phone && customer.phone.includes('List Test')))
          .toBe(true);
      });
    });
    
    it('should filter customers by city', async () => {
      // Make request with city filter
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers?city=Sydney`,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(response.data.customers).toBeDefined();
      expect(Array.isArray(response.data.customers)).toBe(true);
      
      // Verify all returned customers are from Sydney
      response.data.customers
        .filter(customer => customer.name.includes('List Test'))
        .forEach(customer => {
          expect(customer.city).toBe('Sydney');
        });
    });
    
    it('should sort customers by name', async () => {
      // Make request with sorting parameters
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers?sort_by=name&sort_order=asc`,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(response.data.customers).toBeDefined();
      expect(Array.isArray(response.data.customers)).toBe(true);
      
      // Verify customers are sorted by name
      const sortedCustomers = [...response.data.customers].sort((a, b) => 
        a.name.localeCompare(b.name)
      );
      
      // Check if the first few customers match the expected sort order
      expect(response.data.customers[0].name).toBe(sortedCustomers[0].name);
      expect(response.data.customers[1].name).toBe(sortedCustomers[1].name);
    });
    
    it('should paginate customers', async () => {
      // Make request with pagination parameters
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers?limit=2&offset=0`,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(response.data.customers).toBeDefined();
      expect(Array.isArray(response.data.customers)).toBe(true);
      expect(response.data.customers.length).toBeLessThanOrEqual(2);
      
      // Get next page
      const nextPageResponse = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers?limit=2&offset=2`,
        session: adminSession
      });
      
      // Check that pages contain different customers
      const firstPageIds = response.data.customers.map(c => c.id);
      const secondPageIds = nextPageResponse.data.customers.map(c => c.id);
      
      // No customer should appear on both pages
      const overlap = firstPageIds.filter(id => secondPageIds.includes(id));
      expect(overlap.length).toBe(0);
    });
  });
  
  describe('POST /api/customers', () => {
    it('should create a new customer', async () => {
      // Customer data
      const newCustomer = {
        name: 'New Test Customer',
        email: `new-test-${Date.now()}@example.com`,
        phone: '0499887766',
        city: 'Brisbane',
        state: 'QLD'
      };
      
      // Make request to create customer
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers`,
        method: 'POST',
        body: newCustomer,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(201);
      expect(response.data.id).toBeDefined();
      expect(response.data.name).toBe(newCustomer.name);
      expect(response.data.email).toBe(newCustomer.email);
      
      // Add to cleanup list
      testData.customerIds.push(response.data.id);
    });
    
    it('should validate required fields', async () => {
      // Missing required fields
      const invalidCustomer = {
        phone: '0499887766'
      };
      
      // Make request with invalid data
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers`,
        method: 'POST',
        body: invalidCustomer,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(400);
      expect(response.data.error).toBeDefined();
    });
    
    it('should prevent duplicate email addresses', async () => {
      // Create first customer
      const customer = await createTestCustomer();
      testData.customerIds.push(customer.id);
      
      // Try to create another customer with the same email
      const duplicateCustomer = {
        name: 'Duplicate Email Customer',
        email: customer.email,
        phone: '0499887766'
      };
      
      // Make request with duplicate email
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers`,
        method: 'POST',
        body: duplicateCustomer,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(409);
      expect(response.data.error).toBeDefined();
    });
  });
  
  describe('GET /api/customers/:id', () => {
    let testCustomer;
    
    beforeAll(async () => {
      // Create test customer
      testCustomer = await createTestCustomer();
      testData.customerIds.push(testCustomer.id);
    });
    
    it('should return a customer by ID', async () => {
      // Make request to get customer
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/${testCustomer.id}`,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(200);
      expect(response.data.customer).toBeDefined();
      expect(response.data.customer.id).toBe(testCustomer.id);
      expect(response.data.customer.name).toBe(testCustomer.name);
      expect(response.data.customer.email).toBe(testCustomer.email);
      expect(response.data.bookings).toBeDefined();
      expect(Array.isArray(response.data.bookings)).toBe(true);
    });
    
    it('should return 404 for non-existent customer', async () => {
      // Make request with invalid ID
      const response = await makeAuthenticatedRequest({
        url: `${API_BASE_URL}/customers/00000000-0000-0000-0000-000000000000`,
        session: adminSession
      });
      
      // Check response
      expect(response.status).toBe(404);
      expect(response.data.error).toBeDefined();
    });
  });
});

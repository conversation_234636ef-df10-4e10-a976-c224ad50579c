.productCard {
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.productCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.cardContent {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Card front */
.cardFront {
  width: 100%;
  height: 100%;
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 220px;
  overflow: hidden;
}

.productImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.productCard:hover .productImage {
  transform: scale(1.05);
}

.newBadge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--primary-color);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 1;
}

.cardInfo {
  padding: 1.5rem;
}

.productTitle {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.productPrice {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.productDescription {
  font-size: 0.95rem;
  color: var(--light-text-color);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.viewDetails {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.productCard:hover .viewDetails {
  gap: 0.8rem;
}

/* Card expanded */
.cardExpanded {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  transform: translateY(20px);
  overflow-y: auto;
  max-height: 100%;
}

.expanded .cardExpanded {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.expandedTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.expandedPrice {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.expandedDescription {
  font-size: 0.95rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.featuresSection, .detailsSection {
  margin-bottom: 1.5rem;
}

.featuresSection h4, .detailsSection h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  color: var(--text-color);
}

.featuresList, .detailsList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.featureItem svg {
  color: var(--primary-color);
  flex-shrink: 0;
}

.detailItem {
  margin-bottom: 0.5rem;
  color: var(--light-text-color);
  font-size: 0.9rem;
}

.actionButtons {
  display: flex;
  gap: 1rem;
  margin-top: auto;
  margin-bottom: 1.5rem;
}

.viewButton, .addToCartButton {
  flex: 1;
  padding: 0.8rem 1rem;
  border-radius: 30px;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.viewButton {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  text-decoration: none;
}

.viewButton:hover {
  background-color: var(--primary-color);
  color: white;
}

.addToCartButton {
  background-color: var(--primary-color);
  border: 2px solid var(--primary-color);
  color: white;
}

.addToCartButton:hover {
  background-color: var(--secondary-color, #5562ff);
  border-color: var(--secondary-color, #5562ff);
}

.closeExpanded {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: var(--light-text-color);
  font-weight: 500;
  cursor: pointer;
}

/* Responsive styles */
@media (max-width: 768px) {
  .actionButtons {
    flex-direction: column;
  }
  
  .cardExpanded {
    padding: 1.5rem;
  }
  
  .expandedTitle {
    font-size: 1.3rem;
  }
  
  .expandedPrice {
    font-size: 1.2rem;
  }
}

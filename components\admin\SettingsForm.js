import { useState, useEffect } from 'react';
import styles from '@/styles/admin/SettingsForm.module.css';

const SettingsForm = ({ settings, onSave, loading }) => {
  const [formData, setFormData] = useState({
    site_name: '',
    site_description: '',
    contact_email: '',
    contact_phone: '',
    business_hours: '',
    booking_lead_time: '24',
    booking_max_days_ahead: '60',
    enable_online_bookings: 'true',
    enable_online_payments: 'true',
    notification_email: '',
    google_analytics_id: '',
    facebook_pixel_id: '',
    theme_primary_color: '#3788d8',
    theme_secondary_color: '#2c3e50',
    theme_accent_color: '#e74c3c',
    logo_url: '',
    favicon_url: '',
    terms_url: '',
    privacy_url: '',
    social_facebook: '',
    social_instagram: '',
    social_twitter: '',
    social_linkedin: '',
    social_youtube: '',
    custom_css: '',
    custom_js: '',
  });

  // Initialize form with settings
  useEffect(() => {
    if (settings) {
      setFormData(prevData => ({
        ...prevData,
        ...settings
      }));
    }
  }, [settings]);

  // Handle input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    // Handle checkboxes
    if (type === 'checkbox') {
      setFormData({
        ...formData,
        [name]: checked.toString()
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form className={styles.settingsForm} onSubmit={handleSubmit}>
      <div className={styles.formSection}>
        <h2>General Settings</h2>
        
        <div className={styles.formGroup}>
          <label htmlFor="site_name">Site Name</label>
          <input
            type="text"
            id="site_name"
            name="site_name"
            value={formData.site_name || ''}
            onChange={handleChange}
            placeholder="Your Business Name"
          />
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="site_description">Site Description</label>
          <textarea
            id="site_description"
            name="site_description"
            value={formData.site_description || ''}
            onChange={handleChange}
            placeholder="Brief description of your business"
            rows={3}
          />
        </div>
        
        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label htmlFor="contact_email">Contact Email</label>
            <input
              type="email"
              id="contact_email"
              name="contact_email"
              value={formData.contact_email || ''}
              onChange={handleChange}
              placeholder="<EMAIL>"
            />
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="contact_phone">Contact Phone</label>
            <input
              type="tel"
              id="contact_phone"
              name="contact_phone"
              value={formData.contact_phone || ''}
              onChange={handleChange}
              placeholder="+****************"
            />
          </div>
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="business_hours">Business Hours</label>
          <textarea
            id="business_hours"
            name="business_hours"
            value={formData.business_hours || ''}
            onChange={handleChange}
            placeholder="Mon-Fri: 9am-5pm, Sat: 10am-3pm, Sun: Closed"
            rows={2}
          />
        </div>
      </div>
      
      <div className={styles.formSection}>
        <h2>Booking Settings</h2>
        
        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label htmlFor="booking_lead_time">Booking Lead Time (hours)</label>
            <input
              type="number"
              id="booking_lead_time"
              name="booking_lead_time"
              value={formData.booking_lead_time || '24'}
              onChange={handleChange}
              min="0"
              max="168"
            />
            <small>Minimum hours in advance a booking can be made</small>
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="booking_max_days_ahead">Max Booking Days Ahead</label>
            <input
              type="number"
              id="booking_max_days_ahead"
              name="booking_max_days_ahead"
              value={formData.booking_max_days_ahead || '60'}
              onChange={handleChange}
              min="1"
              max="365"
            />
            <small>Maximum days in advance a booking can be made</small>
          </div>
        </div>
        
        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="enable_online_bookings"
                checked={formData.enable_online_bookings === 'true'}
                onChange={handleChange}
              />
              Enable Online Bookings
            </label>
          </div>
          
          <div className={styles.formGroup}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="enable_online_payments"
                checked={formData.enable_online_payments === 'true'}
                onChange={handleChange}
              />
              Enable Online Payments
            </label>
          </div>
        </div>
        
        <div className={styles.formGroup}>
          <label htmlFor="notification_email">Notification Email</label>
          <input
            type="email"
            id="notification_email"
            name="notification_email"
            value={formData.notification_email || ''}
            onChange={handleChange}
            placeholder="<EMAIL>"
          />
          <small>Email address to receive booking notifications</small>
        </div>
      </div>
      
      <div className={styles.formSection}>
        <h2>Theme Settings</h2>
        
        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label htmlFor="theme_primary_color">Primary Color</label>
            <div className={styles.colorPickerWrapper}>
              <input
                type="color"
                id="theme_primary_color"
                name="theme_primary_color"
                value={formData.theme_primary_color || '#3788d8'}
                onChange={handleChange}
              />
              <input
                type="text"
                value={formData.theme_primary_color || '#3788d8'}
                onChange={handleChange}
                name="theme_primary_color"
                placeholder="#3788d8"
              />
            </div>
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="theme_secondary_color">Secondary Color</label>
            <div className={styles.colorPickerWrapper}>
              <input
                type="color"
                id="theme_secondary_color"
                name="theme_secondary_color"
                value={formData.theme_secondary_color || '#2c3e50'}
                onChange={handleChange}
              />
              <input
                type="text"
                value={formData.theme_secondary_color || '#2c3e50'}
                onChange={handleChange}
                name="theme_secondary_color"
                placeholder="#2c3e50"
              />
            </div>
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="theme_accent_color">Accent Color</label>
            <div className={styles.colorPickerWrapper}>
              <input
                type="color"
                id="theme_accent_color"
                name="theme_accent_color"
                value={formData.theme_accent_color || '#e74c3c'}
                onChange={handleChange}
              />
              <input
                type="text"
                value={formData.theme_accent_color || '#e74c3c'}
                onChange={handleChange}
                name="theme_accent_color"
                placeholder="#e74c3c"
              />
            </div>
          </div>
        </div>
      </div>
      
      <div className={styles.formActions}>
        <button 
          type="submit" 
          className={styles.saveButton}
          disabled={loading}
        >
          {loading ? 'Saving...' : 'Save Settings'}
        </button>
      </div>
    </form>
  );
};

export default SettingsForm;

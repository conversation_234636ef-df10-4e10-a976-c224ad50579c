.productForm {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.errorMessage {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 14px;
  border-left: 4px solid #d32f2f;
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

@media (min-width: 768px) {
  .formGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.formSection {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.formSection h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.formGroup {
  margin-bottom: 16px;
}

.formGroup label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.formGroup small {
  display: block;
  margin-top: 4px;
  color: #777;
  font-size: 12px;
}

.formControl {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.formControl:focus {
  border-color: #4a90e2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.formRow {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

@media (min-width: 576px) {
  .formRow {
    grid-template-columns: repeat(2, 1fr);
  }
}

.checkboxGroup {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.checkbox {
  margin-right: 8px;
}

.imageUploadContainer {
  margin-top: 8px;
}

.uploadPlaceholder {
  border: 2px dashed #ddd;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.uploadPlaceholder:hover {
  border-color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.05);
}

.fileInput {
  display: none;
}

.uploadButton {
  display: inline-block;
  background-color: #f5f5f5;
  color: #333;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.uploadButton:hover {
  background-color: #e0e0e0;
}

.imagePreview {
  position: relative;
  margin-top: 8px;
}

.previewImage {
  width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: 4px;
  border: 1px solid #eee;
}

.removeImageButton {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(255, 255, 255, 0.8);
  color: #d32f2f;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.removeImageButton:hover {
  background-color: rgba(255, 255, 255, 1);
  color: #b71c1c;
}

.galleryUploadContainer {
  margin-top: 8px;
}

.galleryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
}

.galleryItem {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  aspect-ratio: 1;
}

.galleryImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.removeGalleryButton {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  color: #d32f2f;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.removeGalleryButton:hover {
  background-color: rgba(255, 255, 255, 1);
  color: #b71c1c;
}

.galleryUploadPlaceholder {
  border: 2px dashed #ddd;
  border-radius: 4px;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.galleryUploadPlaceholder:hover {
  border-color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.05);
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.cancelButton {
  padding: 10px 20px;
  background-color: #f5f5f5;
  color: #333;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.cancelButton:hover {
  background-color: #e0e0e0;
}

.saveButton {
  padding: 10px 20px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.saveButton:hover {
  background-color: #3a7bc8;
}

.saveButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

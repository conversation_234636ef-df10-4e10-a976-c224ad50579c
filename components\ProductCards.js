import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import styles from '@/styles/ProductCards.module.css';
import AnimatedSection from './AnimatedSection';
import QuickViewModal from './QuickViewModal';

/**
 * ProductCards component with interactive animations and filtering
 *
 * @param {Object} props - Component props
 * @param {Array} props.products - Array of product objects
 * @param {Array} props.categories - Array of category objects
 * @param {string} props.title - Section title
 * @param {string} props.subtitle - Section subtitle
 * @returns {JSX.Element}
 */
const ProductCards = ({
  products = [],
  categories = [],
  title = 'Our Products',
  subtitle = 'Browse our collection of eco-friendly products',
  ...props
}) => {
  const router = useRouter();
  const [activeCategory, setActiveCategory] = useState('all');
  const [hoveredProduct, setHoveredProduct] = useState(null);
  const [cart, setCart] = useState([]);
  const [isCartVisible, setIsCartVisible] = useState(false);
  const [cartAnimation, setCartAnimation] = useState({ active: false, productId: null, top: 0, left: 0 });
  const [quickViewProduct, setQuickViewProduct] = useState(null);
  const [policyAcknowledged, setPolicyAcknowledged] = useState(false);

  // Default categories if none provided
  const defaultCategories = [
    { id: 'all', name: 'All Products' },
    { id: 'glitter', name: 'Eco-Friendly Glitter' },
    { id: 'kits', name: 'Face Painting Kits' },
    { id: 'accessories', name: 'Accessories' }
  ];

  // Default products if none provided
  const defaultProducts = [
    {
      id: 1,
      name: 'Biodegradable Chunky Glitter - Ocean Blue',
      category: 'glitter',
      price: 12.95,
      description: 'Add some sparkle with our eco-friendly, biodegradable chunky glitter in a beautiful ocean blue shade.',
      image: '/images/products/biodegradable-glitter.jpg',
      badge: 'Best Seller'
    },
    {
      id: 2,
      name: 'Biodegradable Fine Glitter - Rose Gold',
      category: 'glitter',
      price: 12.95,
      description: 'Our fine biodegradable glitter in stunning rose gold, perfect for adding subtle shimmer to any look.',
      image: '/images/products/product-1.jpg',
      badge: 'New'
    },
    {
      id: 3,
      name: 'Biodegradable Chunky Glitter - Rainbow Mix',
      category: 'glitter',
      price: 14.95,
      description: 'A vibrant mix of rainbow colors in our eco-friendly chunky glitter formula.',
      image: '/images/products/product-2.jpg'
    },
    {
      id: 4,
      name: 'Biodegradable Fine Glitter - Silver Sparkle',
      category: 'glitter',
      price: 12.95,
      description: 'Classic silver sparkle in our fine, biodegradable formula that is gentle on the environment.',
      image: '/images/products/product-3.jpg'
    },
    {
      id: 5,
      name: 'Beginner Face Painting Kit',
      category: 'kits',
      price: 49.95,
      description: 'Everything you need to get started with face painting, including 8 colors, 2 brushes, and a sponge.',
      image: '/images/services/face-paint.jpg',
      badge: 'Popular'
    },
    {
      id: 6,
      name: 'Professional Face Painting Kit',
      category: 'kits',
      price: 89.95,
      description: 'Our professional kit includes 16 colors, 5 brushes, 3 sponges, and a carrying case.',
      image: '/images/services/airbrush-painting.jpeg'
    },
    {
      id: 7,
      name: 'Glitter Application Brush Set',
      category: 'accessories',
      price: 24.95,
      description: 'Set of 3 specialized brushes for perfect glitter application every time.',
      image: '/images/products/product-4.jpg'
    },
    {
      id: 8,
      name: 'Eco-Friendly Glitter Gel Base',
      category: 'accessories',
      price: 18.95,
      description: 'Our aloe-based gel creates the perfect base for glitter application, keeping it in place all day.',
      image: '/images/gallery/gallery-5.jpg',
      badge: 'Eco-Friendly'
    }
  ];

  const displayProducts = products.length > 0 ? products : defaultProducts;
  const displayCategories = categories.length > 0 ? categories : defaultCategories;

  // Filter products based on active category
  const filteredProducts = activeCategory === 'all'
    ? displayProducts
    : displayProducts.filter(product => product.category === activeCategory);

  // Handle adding to cart
  const handleAddToCart = (product, e) => {
    // Create animation from button to cart icon
    let buttonRect;

    if (e) {
      // If called from button click
      buttonRect = e.currentTarget.getBoundingClientRect();
    } else {
      // If called from QuickView modal
      const cartButton = document.querySelector(`.${styles.cartButton}`);
      buttonRect = cartButton.getBoundingClientRect();
    }

    setCartAnimation({
      active: true,
      productId: product.id,
      top: buttonRect.top,
      left: buttonRect.left
    });

    // Add product to cart
    setCart(prev => {
      const existingProduct = prev.find(item => item.id === product.id);

      if (existingProduct) {
        return prev.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [...prev, { ...product, quantity: 1 }];
      }
    });

    // Reset animation after it completes
    setTimeout(() => {
      setCartAnimation({ active: false, productId: null, top: 0, left: 0 });
    }, 1000);
  };

  // Handle quick view
  const handleQuickView = (product, e) => {
    e.stopPropagation(); // Prevent event bubbling
    setQuickViewProduct(product);
  };

  // Calculate cart total
  const cartTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);

  // Load cart from localStorage on mount
  useEffect(() => {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      try {
        const parsedCart = JSON.parse(savedCart);
        setCart(parsedCart);
      } catch (error) {
        console.error('Error parsing cart from localStorage:', error);
      }
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('cart', JSON.stringify(cart));
  }, [cart]);

  // Clean up animation on unmount
  useEffect(() => {
    return () => {
      if (cartAnimation.active) {
        setCartAnimation({ active: false, productId: null, top: 0, left: 0 });
      }
    };
  }, [cartAnimation.active]);

  return (
    <section className={styles.productsSection} id="products" {...props}>
      <AnimatedSection animation="fade-up">
        <h2 className={styles.sectionTitle}>{title}</h2>
        <p className={styles.sectionSubtitle}>{subtitle}</p>
      </AnimatedSection>

      <div className={styles.productHeader}>
        <div className={styles.categoryNav}>
          {displayCategories.map((category) => (
            <button
              key={category.id}
              className={`${styles.categoryButton} ${activeCategory === category.id ? styles.activeCategory : ''}`}
              onClick={() => setActiveCategory(category.id)}
            >
              {category.name}
              {activeCategory === category.id && <span className={styles.activeIndicator}></span>}
            </button>
          ))}
        </div>

        <div className={styles.cartContainer}>
          <button
            className={styles.cartButton}
            onClick={() => setIsCartVisible(!isCartVisible)}
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 22C9.55228 22 10 21.5523 10 21C10 20.4477 9.55228 20 9 20C8.44772 20 8 20.4477 8 21C8 21.5523 8.44772 22 9 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M20 22C20.5523 22 21 21.5523 21 21C21 20.4477 20.5523 20 20 20C19.4477 20 19 20.4477 19 21C19 21.5523 19.4477 22 20 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M1 1H5L7.68 14.39C7.77144 14.8504 8.02191 15.264 8.38755 15.5583C8.75318 15.8526 9.2107 16.009 9.68 16H19.4C19.8693 16.009 20.3268 15.8526 20.6925 15.5583C21.0581 15.264 21.3086 14.8504 21.4 14.39L23 6H6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            {cart.length > 0 && (
              <span className={styles.cartCount}>{cart.reduce((count, item) => count + item.quantity, 0)}</span>
            )}
          </button>

          {isCartVisible && (
            <div className={styles.cartDropdown}>
              <h3>Your Cart</h3>
              {cart.length === 0 ? (
                <p className={styles.emptyCart}>Your cart is empty</p>
              ) : (
                <>
                  <div className={styles.cartItems}>
                    {cart.map(item => (
                      <div key={item.id} className={styles.cartItem}>
                        <div className={styles.cartItemImage}>
                          <img src={item.image} alt={item.name} />
                        </div>
                        <div className={styles.cartItemDetails}>
                          <h4>{item.name}</h4>
                          <div className={styles.cartItemPrice}>
                            <span>${item.price.toFixed(2)}</span>
                            <span>x {item.quantity}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className={styles.cartTotal}>
                    <span>Total:</span>
                    <span>${cartTotal.toFixed(2)}</span>
                  </div>

                  <div className={styles.policyLinks}>
                    <a href="/policies#shipping-info" target="_blank" rel="noopener noreferrer">Shipping Information</a>
                    <a href="/policies#return-policy" target="_blank" rel="noopener noreferrer">Return & Refund Policy</a>
                  </div>

                  <div className={styles.policyCheckbox}>
                    <label>
                      <input
                        type="checkbox"
                        checked={policyAcknowledged}
                        onChange={() => setPolicyAcknowledged(!policyAcknowledged)}
                      />
                      <span>I acknowledge that I have read and agree to the shipping and return policies</span>
                    </label>
                  </div>

                  <button
                    className={`${styles.checkoutButton} ${!policyAcknowledged ? styles.disabledButton : ''}`}
                    disabled={!policyAcknowledged}
                    onClick={() => {
                      if (policyAcknowledged) {
                        router.push('/checkout');
                      }
                    }}
                  >
                    Checkout
                  </button>
                </>
              )}
            </div>
          )}
        </div>
      </div>

      <div className={styles.productsGrid}>
        {filteredProducts.map((product, index) => (
          <AnimatedSection
            key={product.id}
            animation="fade-in"
            delay={index * 100}
            className={styles.productCardWrapper}
            onMouseEnter={() => setHoveredProduct(product.id)}
            onMouseLeave={() => setHoveredProduct(null)}
          >
            <div className={styles.productCard}>
              <div className={styles.productImageContainer}>
                <img src={product.image} alt={product.name} className={styles.productImage} />
                {product.badge && (
                  <span className={styles.productBadge}>{product.badge}</span>
                )}
                <div className={styles.productOverlay}>
                  <button
                    className={styles.quickViewButton}
                    onClick={(e) => handleQuickView(product, e)}
                  >
                    Quick View
                  </button>
                </div>
              </div>
              <div className={styles.productContent}>
                <h3 className={styles.productName}>{product.name}</h3>
                <p className={styles.productDescription}>{product.description}</p>
                <div className={styles.productFooter}>
                  <span className={styles.productPrice}>${product.price.toFixed(2)}</span>
                  <button
                    className={styles.addToCartButton}
                    onClick={(e) => handleAddToCart(product, e)}
                  >
                    <span className={styles.buttonText}>Add to Cart</span>
                    <span className={styles.buttonIcon}>
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 5V19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </AnimatedSection>
        ))}
      </div>

      {/* Flying cart animation */}
      {cartAnimation.active && (
        <div
          className={styles.flyingProduct}
          style={{
            top: cartAnimation.top,
            left: cartAnimation.left
          }}
        ></div>
      )}

      {/* Quick View Modal */}
      {quickViewProduct && (
        <QuickViewModal
          product={quickViewProduct}
          onClose={() => setQuickViewProduct(null)}
          onAddToCart={handleAddToCart}
        />
      )}
    </section>
  );
};

export default ProductCards;
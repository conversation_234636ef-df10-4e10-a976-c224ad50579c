/**
 * Authentication Test Script
 * 
 * This script tests the authentication flow by:
 * 1. Signing in with provided credentials
 * 2. Verifying the user role
 * 3. Testing token validation
 * 
 * Usage:
 * 1. Run with: node scripts/test-auth.js
 * 2. Follow the prompts to enter your admin credentials
 */

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase credentials in .env.local file');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function testAuthentication() {
  try {
    // Prompt for credentials
    rl.question('Enter your admin email: ', async (email) => {
      if (!email) {
        console.error('Error: Email is required');
        rl.close();
        return;
      }

      rl.question('Enter your password: ', async (password) => {
        if (!password) {
          console.error('Error: Password is required');
          rl.close();
          return;
        }

        console.log('\n=== AUTHENTICATION TEST ===');
        console.log(`Testing authentication for: ${email}`);
        
        // Step 1: Sign in
        console.log('\n1. Testing sign in...');
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password
        });
        
        if (error) {
          console.error('❌ Sign in failed:', error.message);
          rl.close();
          return;
        }
        
        console.log('✅ Sign in successful!');
        console.log(`User ID: ${data.user.id}`);
        console.log(`Email: ${data.user.email}`);
        
        // Step 2: Verify session
        console.log('\n2. Verifying session...');
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError || !sessionData.session) {
          console.error('❌ Session verification failed:', sessionError?.message || 'No session found');
          rl.close();
          return;
        }
        
        console.log('✅ Session verified!');
        console.log(`Access token expires: ${new Date(sessionData.session.expires_at * 1000).toLocaleString()}`);
        
        // Step 3: Check user role
        console.log('\n3. Checking user role...');
        const { data: roleData, error: roleError } = await supabase
          .from('user_roles')
          .select('role')
          .eq('id', data.user.id)
          .single();
        
        if (roleError) {
          console.error('❌ Role check failed:', roleError.message);
          rl.close();
          return;
        }
        
        if (!roleData) {
          console.error('❌ No role found for this user');
          console.log('This user needs to be assigned a role in the user_roles table');
          rl.close();
          return;
        }
        
        console.log(`✅ User role: ${roleData.role}`);
        
        if (roleData.role !== 'admin' && roleData.role !== 'staff') {
          console.warn(`⚠️ Warning: User has role "${roleData.role}" but needs "admin" or "staff" role for admin access`);
        } else {
          console.log('✅ User has appropriate role for admin access');
        }
        
        // Step 4: Test token validation
        console.log('\n4. Testing token validation...');
        const token = sessionData.session.access_token;
        
        // Verify token by getting user with it
        const { data: userData, error: userError } = await supabase.auth.getUser(token);
        
        if (userError || !userData.user) {
          console.error('❌ Token validation failed:', userError?.message || 'User not found with token');
          rl.close();
          return;
        }
        
        console.log('✅ Token validation successful!');
        
        // Summary
        console.log('\n=== AUTHENTICATION TEST SUMMARY ===');
        console.log('✅ Sign in: Successful');
        console.log('✅ Session: Valid');
        console.log(`✅ User role: ${roleData.role}`);
        console.log('✅ Token validation: Successful');
        
        if (roleData.role === 'admin' || roleData.role === 'staff') {
          console.log('\n🎉 Authentication test passed! Your admin credentials are working correctly.');
          console.log('If you are still experiencing authentication issues in the admin panel, the problem may be with:');
          console.log('1. Browser storage (try clearing cookies and local storage)');
          console.log('2. API request headers (check if tokens are being sent correctly)');
          console.log('3. Row Level Security policies (verify they are configured correctly)');
        } else {
          console.log('\n⚠️ Authentication partially working, but user needs admin or staff role.');
          console.log('Run the verify-admin-user.js script to fix the role assignment.');
        }
        
        rl.close();
      });
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    rl.close();
  }
}

// Start the test
testAuthentication();

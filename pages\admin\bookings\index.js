import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import BookingCalendar from '@/components/admin/BookingCalendar';
import BookingDetails from '@/components/admin/BookingDetails';
import BookingForm from '@/components/admin/BookingForm';
import Modal from '@/components/admin/Modal';
import supabase from '@/lib/supabase';
import styles from '@/styles/admin/BookingsPage.module.css';

export default function BookingsPage() {
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(null); // 'create', 'edit', 'view'
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [bookingStats, setBookingStats] = useState({
    total: 0,
    confirmed: 0,
    pending: 0,
    canceled: 0
  });
  const [loading, setLoading] = useState(true);
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch booking statistics
  useEffect(() => {
    const fetchBookingStats = async () => {
      try {
        setLoading(true);
        // Use Supabase client directly
        const client = supabase;

        // Get total bookings
        const { count: total, error: totalError } = await client
          .from('bookings')
          .select('*', { count: 'exact', head: true });

        if (totalError) throw totalError;

        // Get confirmed bookings
        const { count: confirmed, error: confirmedError } = await client
          .from('bookings')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'confirmed');

        if (confirmedError) throw confirmedError;

        // Get pending bookings
        const { count: pending, error: pendingError } = await client
          .from('bookings')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'pending');

        if (pendingError) throw pendingError;

        // Get canceled bookings
        const { count: canceled, error: canceledError } = await client
          .from('bookings')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'canceled');

        if (canceledError) throw canceledError;

        setBookingStats({
          total,
          confirmed,
          pending,
          canceled
        });

        setLoading(false);
      } catch (error) {
        console.error('Error fetching booking stats:', error);
        setLoading(false);
      }
    };

    fetchBookingStats();
  }, [refreshKey]);

  // Handle booking selection
  const handleSelectBooking = (booking) => {
    setSelectedBooking(booking);
    setModalType('view');
    setShowModal(true);
  };

  // Handle slot selection for creating a new booking
  const handleSelectSlot = (slotInfo) => {
    setSelectedSlot(slotInfo);
    setModalType('create');
    setShowModal(true);
  };

  // Handle adding a new booking
  const handleAddBooking = () => {
    setSelectedBooking(null);
    setSelectedSlot(null);
    setModalType('create');
    setShowModal(true);
  };

  // Handle booking saved (created or updated)
  const handleBookingSaved = () => {
    // Refresh the calendar and stats
    setRefreshKey(prev => prev + 1);
    setShowModal(false);
    setSelectedBooking(null);
    setSelectedSlot(null);
  };

  // Handle modal close
  const handleCloseModal = () => {
    setShowModal(false);
  };

  return (
    <ProtectedRoute>
      <AdminLayout title="Booking Management">
        <div className={styles.bookingsPage}>
          <div className={styles.statsContainer}>
            <div className={styles.statCard}>
              <div className={styles.statIcon}>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
              </div>
              <div className={styles.statContent}>
                <h3>Total Bookings</h3>
                <p className={styles.statValue}>{bookingStats.total}</p>
              </div>
            </div>

            <div className={styles.statCard}>
              <div className={`${styles.statIcon} ${styles.confirmed}`}>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
              </div>
              <div className={styles.statContent}>
                <h3>Confirmed</h3>
                <p className={styles.statValue}>{bookingStats.confirmed}</p>
              </div>
            </div>

            <div className={styles.statCard}>
              <div className={`${styles.statIcon} ${styles.pending}`}>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
              </div>
              <div className={styles.statContent}>
                <h3>Pending</h3>
                <p className={styles.statValue}>{bookingStats.pending}</p>
              </div>
            </div>

            <div className={styles.statCard}>
              <div className={`${styles.statIcon} ${styles.canceled}`}>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="15" y1="9" x2="9" y2="15"></line>
                  <line x1="9" y1="9" x2="15" y2="15"></line>
                </svg>
              </div>
              <div className={styles.statContent}>
                <h3>Canceled</h3>
                <p className={styles.statValue}>{bookingStats.canceled}</p>
              </div>
            </div>
          </div>

          <div className={styles.actionBar}>
            <h2 className={styles.sectionTitle}>Booking Calendar</h2>
            <button
              className={styles.addButton}
              onClick={handleAddBooking}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
              Add Booking
            </button>
          </div>

          <div className={styles.contentContainer}>
            <div className={styles.calendarContainer}>
              <BookingCalendar
                key={refreshKey}
                onSelectBooking={handleSelectBooking}
                onSelectSlot={handleSelectSlot}
                refreshKey={refreshKey}
              />
            </div>
          </div>

          {/* Modal for booking form */}
          {showModal && (
            <Modal onClose={handleCloseModal}>
              {modalType === 'create' && (
                <BookingForm
                  booking={null}
                  initialSlot={selectedSlot}
                  onSave={handleBookingSaved}
                  onCancel={handleCloseModal}
                />
              )}
              {modalType === 'edit' && (
                <BookingForm
                  booking={selectedBooking}
                  onSave={handleBookingSaved}
                  onCancel={handleCloseModal}
                />
              )}
              {modalType === 'view' && (
                <BookingDetails
                  booking={selectedBooking}
                  onClose={handleCloseModal}
                  onEdit={() => setModalType('edit')}
                  onUpdate={handleBookingSaved}
                />
              )}
            </Modal>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}

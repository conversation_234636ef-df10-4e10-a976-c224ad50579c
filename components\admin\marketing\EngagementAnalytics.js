import { useState, useEffect } from 'react'
import { Line, Pie } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'
import styles from '@/styles/admin/marketing/Analytics.module.css'

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

export default function EngagementAnalytics({ period = 'month' }) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [analytics, setAnalytics] = useState(null)

  // Fetch customer engagement analytics
  useEffect(() => {
    const fetchAnalytics = async () => {
      setLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/analytics/engagement?period=${period}`)
        
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch engagement analytics')
        }

        const data = await response.json()
        setAnalytics(data)
      } catch (error) {
        console.error('Error fetching engagement analytics:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchAnalytics()
  }, [period])

  // Prepare consent distribution chart data
  const prepareConsentData = () => {
    if (!analytics || !analytics.summary) return null

    const withConsent = analytics.summary.customers_with_consent
    const withoutConsent = analytics.summary.total_customers - withConsent

    return {
      labels: ['With Consent', 'Without Consent'],
      datasets: [
        {
          data: [withConsent, withoutConsent],
          backgroundColor: [
            'rgba(75, 192, 192, 0.6)',
            'rgba(255, 99, 132, 0.6)'
          ],
          borderColor: [
            'rgba(75, 192, 192, 1)',
            'rgba(255, 99, 132, 1)'
          ],
          borderWidth: 1
        }
      ]
    }
  }

  // Prepare new customers time series chart data
  const prepareTimeSeriesData = () => {
    if (!analytics || !analytics.time_series || analytics.time_series.length === 0) return null

    return {
      labels: analytics.time_series.map(item => item.date),
      datasets: [
        {
          label: 'New Customers',
          data: analytics.time_series.map(item => item.new_customers),
          borderColor: 'rgba(54, 162, 235, 1)',
          backgroundColor: 'rgba(54, 162, 235, 0.1)',
          borderWidth: 2,
          tension: 0.4,
          fill: true
        },
        {
          label: 'With Marketing Consent',
          data: analytics.time_series.map(item => item.with_consent),
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.1)',
          borderWidth: 2,
          tension: 0.4,
          fill: true
        }
      ]
    }
  }

  // Chart options
  const pieOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          color: '#666',
          font: {
            size: 12
          }
        }
      },
      title: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || ''
            const value = context.raw || 0
            const total = context.dataset.data.reduce((a, b) => a + b, 0)
            const percentage = Math.round((value / total) * 100)
            return `${label}: ${value} (${percentage}%)`
          }
        }
      }
    }
  }

  const lineOptions = {
    responsive: true,
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          color: '#666'
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        }
      },
      x: {
        ticks: {
          color: '#666'
        },
        grid: {
          display: false
        }
      }
    },
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          color: '#666',
          font: {
            size: 12
          }
        }
      },
      title: {
        display: false
      }
    }
  }

  if (loading) {
    return <div className={styles.loading}>Loading engagement analytics...</div>
  }

  if (error) {
    return <div className={styles.error}>Error: {error}</div>
  }

  if (!analytics) {
    return <div className={styles.noData}>No engagement analytics data available</div>
  }

  const consentData = prepareConsentData()
  const timeSeriesData = prepareTimeSeriesData()

  return (
    <div className={styles.analyticsSection}>
      <h3>Customer Engagement</h3>
      
      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.summary.total_customers}</div>
          <div className={styles.metricLabel}>Total Customers</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.summary.new_customers}</div>
          <div className={styles.metricLabel}>New Customers</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.summary.active_customers}</div>
          <div className={styles.metricLabel}>Active Customers</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.summary.consent_rate}%</div>
          <div className={styles.metricLabel}>Consent Rate</div>
        </div>
      </div>
      
      <div className={styles.chartsGrid}>
        {consentData && (
          <div className={styles.chartCard}>
            <h4>Marketing Consent Distribution</h4>
            <div className={styles.chartContainer}>
              <Pie data={consentData} options={pieOptions} />
            </div>
          </div>
        )}
        
        {timeSeriesData && (
          <div className={styles.chartCard}>
            <h4>New Customer Growth</h4>
            <div className={styles.chartContainer}>
              <Line data={timeSeriesData} options={lineOptions} />
            </div>
          </div>
        )}
      </div>
      
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <h4>Customer Growth</h4>
          <div className={styles.statValue}>{analytics.summary.customer_growth_rate}%</div>
          <div className={styles.statLabel}>Growth Rate</div>
        </div>
        <div className={styles.statCard}>
          <h4>Repeat Customers</h4>
          <div className={styles.statValue}>{analytics.summary.repeat_customers}</div>
          <div className={styles.statLabel}>
            {analytics.summary.active_customers > 0 
              ? `(${Math.round((analytics.summary.repeat_customers / analytics.summary.active_customers) * 100)}% of active)`
              : ''}
          </div>
        </div>
        <div className={styles.statCard}>
          <h4>New Customer Consent</h4>
          <div className={styles.statValue}>{analytics.summary.new_customer_consent_rate}%</div>
          <div className={styles.statLabel}>Consent Rate</div>
        </div>
      </div>
    </div>
  )
}

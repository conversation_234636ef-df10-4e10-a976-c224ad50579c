.segmentDetail {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.headerLeft h2 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  color: #333;
}

.meta {
  display: flex;
  gap: 16px;
}

.metaItem {
  font-size: 0.9rem;
  color: #666;
}

.headerActions {
  display: flex;
  gap: 10px;
}

.editButton,
.campaignButton,
.deleteButton,
.backButton {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.editButton {
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
}

.editButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
  transform: translateY(-1px);
}

.campaignButton {
  background-color: transparent;
  color: #4caf50;
  border: 1px solid #4caf50;
}

.campaignButton:hover {
  background-color: rgba(76, 175, 80, 0.1);
  transform: translateY(-1px);
}

.deleteButton {
  background-color: transparent;
  color: #f44336;
  border: 1px solid #f44336;
  cursor: pointer;
}

.deleteButton:hover {
  background-color: rgba(244, 67, 54, 0.1);
  transform: translateY(-1px);
}

.backButton {
  background-color: #6e8efb;
  color: white;
  border: none;
  cursor: pointer;
  margin-top: 16px;
}

.backButton:hover {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.loading,
.error,
.notFound {
  text-align: center;
  padding: 40px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.error {
  color: #f44336;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.notFound {
  color: #666;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.segmentInfo {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.infoSection {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.infoSection h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.infoLabel {
  font-size: 0.9rem;
  color: #666;
}

.infoValue {
  font-size: 1rem;
  color: #333;
}

.noResults {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.customerTable {
  width: 100%;
  overflow-x: auto;
}

.customerTable table {
  width: 100%;
  border-collapse: collapse;
}

.customerTable th {
  text-align: left;
  padding: 12px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  font-size: 0.9rem;
  color: #333;
}

.customerTable td {
  padding: 12px;
  border-bottom: 1px solid #eee;
  font-size: 0.9rem;
  color: #666;
}

.customerTable tr:last-child td {
  border-bottom: none;
}

.customerTable tr:hover td {
  background-color: rgba(110, 142, 251, 0.05);
}

.consentGranted {
  color: #4caf50;
  font-weight: 500;
}

.consentDenied {
  color: #f44336;
  font-weight: 500;
}

.viewCustomerButton {
  display: inline-block;
  padding: 4px 8px;
  background-color: #6e8efb;
  color: white;
  border-radius: 4px;
  font-size: 0.8rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.viewCustomerButton:hover {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.moreCustomers {
  text-align: center;
  padding: 12px;
  color: #6e8efb;
  font-size: 0.9rem;
  border-top: 1px solid #eee;
}

.deleteModal {
  padding: 20px;
}

.deleteModal h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
}

.deleteModal p {
  margin-bottom: 20px;
  color: #666;
}

.deleteError {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.deleteActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancelDeleteButton,
.confirmDeleteButton {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelDeleteButton {
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
}

.cancelDeleteButton:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.confirmDeleteButton {
  background-color: #f44336;
  color: white;
  border: none;
}

.confirmDeleteButton:hover:not(:disabled) {
  background-color: #e53935;
}

.cancelDeleteButton:disabled,
.confirmDeleteButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 16px;
  }
  
  .headerActions {
    width: 100%;
    flex-wrap: wrap;
  }
  
  .editButton,
  .campaignButton,
  .deleteButton {
    flex: 1;
    text-align: center;
  }
  
  .infoGrid {
    grid-template-columns: 1fr;
  }
  
  .customerTable th:nth-child(3),
  .customerTable td:nth-child(3),
  .customerTable th:nth-child(4),
  .customerTable td:nth-child(4) {
    display: none;
  }
}

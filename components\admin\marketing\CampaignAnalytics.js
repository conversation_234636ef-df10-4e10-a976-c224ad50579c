import { useState, useEffect } from 'react'
import { <PERSON>, Doughnut } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js'
import styles from '@/styles/admin/marketing/Analytics.module.css'

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
)

export default function CampaignAnalytics({ period = 'month' }) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [analytics, setAnalytics] = useState(null)

  // Fetch campaign analytics
  useEffect(() => {
    const fetchAnalytics = async () => {
      setLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/analytics/campaigns?period=${period}`)
        
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch campaign analytics')
        }

        const data = await response.json()
        setAnalytics(data)
      } catch (error) {
        console.error('Error fetching campaign analytics:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchAnalytics()
  }, [period])

  // Prepare campaign type chart data
  const prepareCampaignTypeData = () => {
    if (!analytics || !analytics.campaign_types) return null

    const labels = Object.keys(analytics.campaign_types)
    const data = Object.values(analytics.campaign_types)

    return {
      labels,
      datasets: [
        {
          data,
          backgroundColor: [
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 99, 132, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)'
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)'
          ],
          borderWidth: 1
        }
      ]
    }
  }

  // Prepare campaign performance chart data
  const prepareCampaignPerformanceData = () => {
    if (!analytics || !analytics.top_campaigns || analytics.top_campaigns.length === 0) return null

    // Get top 5 campaigns
    const topCampaigns = analytics.top_campaigns.slice(0, 5)
    
    return {
      labels: topCampaigns.map(campaign => campaign.name),
      datasets: [
        {
          label: 'Open Rate (%)',
          data: topCampaigns.map(campaign => campaign.metrics.open_rate),
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        },
        {
          label: 'Click Rate (%)',
          data: topCampaigns.map(campaign => campaign.metrics.click_rate),
          backgroundColor: 'rgba(255, 99, 132, 0.6)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        },
        {
          label: 'Click-Through Rate (%)',
          data: topCampaigns.map(campaign => campaign.metrics.click_through_rate),
          backgroundColor: 'rgba(75, 192, 192, 0.6)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }
      ]
    }
  }

  // Chart options
  const doughnutOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          color: '#666',
          font: {
            size: 12
          }
        }
      },
      title: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || ''
            const value = context.raw || 0
            const total = context.dataset.data.reduce((a, b) => a + b, 0)
            const percentage = Math.round((value / total) * 100)
            return `${label}: ${value} (${percentage}%)`
          }
        }
      }
    }
  }

  const barOptions = {
    responsive: true,
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          color: '#666'
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        }
      },
      x: {
        ticks: {
          color: '#666'
        },
        grid: {
          display: false
        }
      }
    },
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          color: '#666',
          font: {
            size: 12
          }
        }
      },
      title: {
        display: false
      }
    }
  }

  if (loading) {
    return <div className={styles.loading}>Loading campaign analytics...</div>
  }

  if (error) {
    return <div className={styles.error}>Error: {error}</div>
  }

  if (!analytics) {
    return <div className={styles.noData}>No campaign analytics data available</div>
  }

  const campaignTypeData = prepareCampaignTypeData()
  const campaignPerformanceData = prepareCampaignPerformanceData()

  return (
    <div className={styles.analyticsSection}>
      <h3>Campaign Analytics</h3>
      
      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.summary.total_campaigns}</div>
          <div className={styles.metricLabel}>Total Campaigns</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.summary.active_campaigns}</div>
          <div className={styles.metricLabel}>Active Campaigns</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.summary.sent_messages}</div>
          <div className={styles.metricLabel}>Messages Sent</div>
        </div>
        <div className={styles.metricCard}>
          <div className={styles.metricValue}>{analytics.summary.open_rate}%</div>
          <div className={styles.metricLabel}>Open Rate</div>
        </div>
      </div>
      
      <div className={styles.chartsGrid}>
        {campaignTypeData && (
          <div className={styles.chartCard}>
            <h4>Campaign Types</h4>
            <div className={styles.chartContainer}>
              <Doughnut data={campaignTypeData} options={doughnutOptions} />
            </div>
          </div>
        )}
        
        {campaignPerformanceData && (
          <div className={styles.chartCard}>
            <h4>Top Campaign Performance</h4>
            <div className={styles.chartContainer}>
              <Bar data={campaignPerformanceData} options={barOptions} />
            </div>
          </div>
        )}
      </div>
      
      {analytics.top_campaigns && analytics.top_campaigns.length > 0 && (
        <div className={styles.tableCard}>
          <h4>Top Performing Campaigns</h4>
          <div className={styles.tableContainer}>
            <table className={styles.dataTable}>
              <thead>
                <tr>
                  <th>Campaign</th>
                  <th>Type</th>
                  <th>Sent</th>
                  <th>Open Rate</th>
                  <th>Click Rate</th>
                </tr>
              </thead>
              <tbody>
                {analytics.top_campaigns.slice(0, 5).map((campaign) => (
                  <tr key={campaign.id}>
                    <td>{campaign.name}</td>
                    <td>{campaign.campaign_type}</td>
                    <td>{campaign.metrics.sent}</td>
                    <td>{campaign.metrics.open_rate}%</td>
                    <td>{campaign.metrics.click_rate}%</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}

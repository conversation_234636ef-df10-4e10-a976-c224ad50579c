# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

download_images.js
download_key_images.js
create_image_dirs.sh
create_image_dirs.bat
image_downloader.html
update_image_references.js
IMAGE_INSTRUCTIONS.md

/**
 * OneSignal Standalone Initialization Script
 * This script initializes OneSignal independently from the React application
 * to prevent hydration issues and initialization race conditions.
 */

// Wait for DOM to be fully loaded before initializing OneSignal
document.addEventListener('DOMContentLoaded', function() {
  // Only initialize in production environment
  if (window.location.hostname !== 'localhost' && 
      !window.location.hostname.includes('127.0.0.1') &&
      !window.location.hostname.includes('.local')) {
    
    // Set a flag to indicate initialization has started
    window.__ONESIGNAL_INIT_STARTED__ = true;
    
    // Initialize with error handling
    try {
      // Create a global error handler for OneSignal
      window.__oneSignalInitError = function(error) {
        console.error('[OneSignal Init] Error:', error);
        // Prevent errors from bubbling up and causing UI issues
        return true;
      };
      
      // Wait for OneSignal to be available
      const waitForOneSignal = setInterval(function() {
        if (window.OneSignal) {
          clearInterval(waitForOneSignal);
          
          try {
            // Initialize OneSignal
            window.OneSignal.init({
              appId: window.__ONESIGNAL_APP_ID__ || "************************************",
              safari_web_id: window.__ONESIGNAL_SAFARI_WEB_ID__ || "web.onesignal.auto.************************************",
              notifyButton: {
                enable: true,
                size: 'medium',
                theme: 'default',
                position: 'bottom-right',
                offset: {
                  bottom: '20px',
                  right: '20px',
                },
                showCredit: false,
                text: {
                  'tip.state.unsubscribed': 'Subscribe to notifications',
                  'tip.state.subscribed': 'You are subscribed to notifications',
                  'tip.state.blocked': 'You have blocked notifications',
                  'message.prenotify': 'Click to subscribe to notifications',
                  'message.action.subscribed': 'Thanks for subscribing!',
                  'message.action.resubscribed': 'You are subscribed to notifications',
                  'message.action.unsubscribed': 'You will no longer receive notifications',
                  'dialog.main.title': 'Manage Notifications',
                  'dialog.main.button.subscribe': 'SUBSCRIBE',
                  'dialog.main.button.unsubscribe': 'UNSUBSCRIBE',
                  'dialog.blocked.title': 'Unblock Notifications',
                  'dialog.blocked.message': 'Follow these instructions to allow notifications:',
                },
                colors: {
                  'circle.background': '#6a0dad',
                  'circle.foreground': 'white',
                  'badge.background': '#6a0dad',
                  'badge.foreground': 'white',
                  'badge.bordercolor': 'white',
                  'pulse.color': 'white',
                  'dialog.button.background.hovering': '#6a0dad',
                  'dialog.button.background.active': '#6a0dad',
                  'dialog.button.background': '#6a0dad',
                  'dialog.button.foreground': 'white',
                },
              },
              welcomeNotification: {
                title: 'OceanSoulSparkles',
                message: 'Thanks for subscribing to notifications!',
              },
              // Add error handling for OneSignal
              promptOptions: {
                slidedown: {
                  prompts: [
                    {
                      type: "push",
                      autoPrompt: false,
                    }
                  ]
                }
              }
            }).then(function() {
              console.log('[OneSignal Init] Initialization successful');
              window.__ONESIGNAL_INITIALIZED__ = true;
              
              // Dispatch a custom event that React components can listen for
              const event = new CustomEvent('onesignal:initialized');
              document.dispatchEvent(event);
            }).catch(function(error) {
              console.error('[OneSignal Init] Initialization promise rejected:', error);
              window.__oneSignalInitError(error);
            });
          } catch (error) {
            console.error('[OneSignal Init] Error during init call:', error);
            window.__oneSignalInitError(error);
          }
        }
      }, 200);
      
      // Add a timeout to prevent hanging indefinitely
      setTimeout(function() {
        clearInterval(waitForOneSignal);
        if (!window.__ONESIGNAL_INITIALIZED__) {
          console.warn('[OneSignal Init] Initialization timed out after 10 seconds');
          
          // Create a mock OneSignal object to prevent errors
          if (!window.OneSignal) {
            window.OneSignal = {
              getNotificationPermission: function() { return Promise.resolve('default'); },
              isPushNotificationsEnabled: function() { return Promise.resolve(false); },
              showNativePrompt: function() { return Promise.resolve(); },
              showHttpPrompt: function() { return Promise.resolve(); },
              showCategorySlidedown: function() { return Promise.resolve(); },
              getUserId: function() { return Promise.resolve('mock-user-id'); },
              setExternalUserId: function() { return Promise.resolve(); },
              removeExternalUserId: function() { return Promise.resolve(); },
              setEmail: function() { return Promise.resolve(); },
              sendTag: function() { return Promise.resolve(); },
              getTags: function() { return Promise.resolve({}); },
              on: function() {},
              once: function() {},
              off: function() {}
            };
            window.__ONESIGNAL_MOCKED__ = true;
          }
        }
      }, 10000);
    } catch (error) {
      console.error('[OneSignal Init] Critical error:', error);
      window.__oneSignalInitError(error);
    }
  } else {
    console.log('[OneSignal Init] Skipped in development/local environment');
  }
});

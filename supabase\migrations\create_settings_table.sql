-- Create settings table for Ocean Soul Sparkles
-- This script creates the settings table and populates it with default values

-- Create the settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- Create policy for admin access
DROP POLICY IF EXISTS "Admin users can manage settings" ON public.settings;
CREATE POLICY "Admin users can manage settings" 
  ON public.settings 
  USING (
    (SELECT role FROM public.user_roles WHERE id = auth.uid()) = 'admin'
  );

-- Create policy for staff read-only access
DROP POLICY IF EXISTS "Staff users can read settings" ON public.settings;
CREATE POLICY "Staff users can read settings" 
  ON public.settings 
  FOR SELECT
  USING (
    (SELECT role FROM public.user_roles WHERE id = auth.uid()) IN ('staff', 'admin')
  );

-- Insert default settings if they don't exist
INSERT INTO public.settings (key, value)
VALUES
  ('site_name', 'Ocean Soul Sparkles'),
  ('site_description', 'Face painting and body art services'),
  ('contact_email', '<EMAIL>'),
  ('contact_phone', ''),
  ('business_hours', 'Mon-Fri: 9am-5pm, Sat: 10am-3pm, Sun: Closed'),
  ('booking_lead_time', '24'),
  ('booking_max_days_ahead', '60'),
  ('enable_online_bookings', 'true'),
  ('enable_online_payments', 'true'),
  ('theme_primary_color', '#3788d8'),
  ('theme_secondary_color', '#2c3e50'),
  ('theme_accent_color', '#e74c3c')
ON CONFLICT (key) DO NOTHING;

-- Create a function to create the settings table
-- This is used by the API when the table doesn't exist
CREATE OR REPLACE FUNCTION public.create_settings_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Create the settings table if it doesn't exist
  CREATE TABLE IF NOT EXISTS public.settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
  );
  
  -- Enable Row Level Security
  ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;
  
  -- Create policy for admin access
  DROP POLICY IF EXISTS "Admin users can manage settings" ON public.settings;
  CREATE POLICY "Admin users can manage settings" 
    ON public.settings 
    USING (
      (SELECT role FROM public.user_roles WHERE id = auth.uid()) = 'admin'
    );
  
  -- Create policy for staff read-only access
  DROP POLICY IF EXISTS "Staff users can read settings" ON public.settings;
  CREATE POLICY "Staff users can read settings" 
    ON public.settings 
    FOR SELECT
    USING (
      (SELECT role FROM public.user_roles WHERE id = auth.uid()) IN ('staff', 'admin')
    );
  
  -- Insert default settings if they don't exist
  INSERT INTO public.settings (key, value)
  VALUES
    ('site_name', 'Ocean Soul Sparkles'),
    ('site_description', 'Face painting and body art services'),
    ('contact_email', '<EMAIL>'),
    ('contact_phone', ''),
    ('business_hours', 'Mon-Fri: 9am-5pm, Sat: 10am-3pm, Sun: Closed'),
    ('booking_lead_time', '24'),
    ('booking_max_days_ahead', '60'),
    ('enable_online_bookings', 'true'),
    ('enable_online_payments', 'true'),
    ('theme_primary_color', '#3788d8'),
    ('theme_secondary_color', '#2c3e50'),
    ('theme_accent_color', '#e74c3c')
  ON CONFLICT (key) DO NOTHING;
END;
$$;

-- Create a function to execute SQL (used as a fallback in the API)
CREATE OR REPLACE FUNCTION public.execute_sql(sql text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE sql;
END;
$$;

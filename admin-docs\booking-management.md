# Booking Management System

This document provides detailed implementation information for the OceanSoulSparkles admin panel booking management system.

## Overview

The booking management system allows administrators to view, create, update, and delete bookings through a user-friendly calendar interface. It includes features for managing appointments, handling customer information, and sending automated notifications.

## Features

- Interactive calendar view with day, week, month, and agenda views
- Color-coded bookings by service type
- Booking creation and editing interface with modal dialogs
- Slot selection for creating new bookings
- Customer information integration with ability to add new customers
- Automated notifications via OneSignal (email and push)
- Booking status management (confirmed, pending, canceled)
- Responsive design for all device sizes

## Database Schema

Create the following tables in Supabase:

```sql
-- Bookings table
CREATE TABLE public.bookings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id),
  service_id UUID REFERENCES public.services(id),
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('confirmed', 'pending', 'canceled')),
  location TEXT,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Services table (if not already created)
CREATE TABLE public.services (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  duration INTEGER NOT NULL, -- in minutes
  price DECIMAL(10, 2) NOT NULL,
  color TEXT NOT NULL, -- for calendar display
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.services ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Staff can view all bookings" ON public.bookings
  FOR SELECT USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can insert bookings" ON public.bookings
  FOR INSERT WITH CHECK (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can update bookings" ON public.bookings
  FOR UPDATE USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Admins can delete bookings" ON public.bookings
  FOR DELETE USING (
    get_user_role(auth.uid()) = 'admin'
  );

-- Similar policies for services table
```

## Implementation Details

The booking management system has been implemented with the following components:

### 1. Core Components

The booking management system consists of the following core components:

1. **BookingCalendar Component** (`components/admin/BookingCalendar.js`)
   - Uses react-big-calendar for displaying bookings
   - Supports day, week, month, and agenda views
   - Allows slot selection for creating new bookings
   - Refreshes data automatically when bookings are added or updated
   - Color-codes bookings based on service type and status

2. **BookingForm Component** (`components/admin/BookingForm.js`)
   - Handles both creating new bookings and editing existing ones
   - Supports selecting existing customers or adding new ones
   - Automatically calculates end time based on service duration
   - Validates form inputs before submission
   - Integrates with OneSignal for sending notifications

3. **BookingDetails Component** (`components/admin/BookingDetails.js`)
   - Displays detailed information about a booking
   - Shows customer information, service details, and booking status
   - Provides options to edit or cancel bookings
   - Sends notifications when booking status changes

4. **Modal Component** (`components/admin/Modal.js`)
   - Provides a reusable modal dialog for forms and details
   - Supports different sizes (small, medium, large)
   - Handles keyboard events (Escape to close)
   - Prevents body scrolling when open

5. **Bookings Page** (`pages/admin/bookings/index.js`)
   - Integrates all booking components
   - Displays booking statistics (total, confirmed, pending, canceled)
   - Provides a calendar interface for viewing and managing bookings
   - Uses modals for creating, editing, and viewing bookings

### 2. Data Flow

The booking management system uses the following data flow:

1. **Fetching Bookings**
   - The BookingCalendar component fetches bookings directly from Supabase
   - Bookings are joined with services and customers tables to get related information
   - Data is transformed into the format required by react-big-calendar
   - Bookings are refreshed when the calendar view changes or when a booking is added/updated

2. **Creating Bookings**
   - User selects a time slot in the calendar or clicks the "Add Booking" button
   - The BookingForm component is displayed in a modal
   - User fills in booking details (customer, service, time, status, etc.)
   - On submission, the booking is saved to Supabase
   - A notification is sent to the customer via OneSignal
   - The calendar is refreshed to show the new booking

3. **Updating Bookings**
   - User clicks on a booking in the calendar
   - The BookingDetails component is displayed in a modal
   - User can view booking details and choose to edit or cancel the booking
   - If editing, the BookingForm component is displayed with pre-filled data
   - On submission, the booking is updated in Supabase
   - A notification is sent to the customer if the status changes
   - The calendar is refreshed to show the updated booking

4. **Canceling Bookings**
   - User clicks on a booking in the calendar
   - The BookingDetails component is displayed in a modal
   - User clicks the "Cancel Booking" button
   - A confirmation dialog is shown
   - On confirmation, the booking status is updated to "canceled" in Supabase
   - A notification is sent to the customer
   - The calendar is refreshed to show the canceled booking

### 3. Notification System

The booking management system integrates with OneSignal to send notifications to customers:

1. **Booking Creation Notifications**
   - When a new booking is created, a notification is sent to the customer
   - The notification includes booking details (service, date, time, location)
   - Both email and push notifications are supported

2. **Booking Update Notifications**
   - When a booking is updated, a notification is sent if the status changes
   - The notification includes the new status and any other changed details
   - Both email and push notifications are supported

3. **Booking Cancellation Notifications**
   - When a booking is canceled, a notification is sent to the customer
   - The notification includes the cancellation details
   - Both email and push notifications are supported

The notification system uses the `lib/notifications.js` module, which provides functions for sending different types of notifications. The implementation handles both development and production environments, with notifications being logged but not actually sent in development mode.

### 4. Styling and UI

The booking management system uses a consistent design aesthetic with the rest of the admin panel:

1. **Calendar Styling**
   - Custom styling for the react-big-calendar component
   - Color-coded bookings based on service type and status
   - Responsive design for different screen sizes

2. **Modal Dialogs**
   - Consistent modal design for forms and details
   - Backdrop blur effect for better focus
   - Smooth animations for opening and closing

3. **Form Styling**
   - Consistent form design with the rest of the admin panel
   - Validation feedback for required fields
   - Clear error and success messages

4. **Responsive Design**
   - Adapts to different screen sizes
   - Mobile-friendly calendar and forms
   - Touch-friendly controls for mobile devices

## Next Steps

1. Implement conflict detection to prevent double-bookings
2. Add filtering and search capabilities for bookings
3. Implement booking reminders (24 hours before appointment)
4. Add reporting and analytics features
5. Integrate with payment processing system


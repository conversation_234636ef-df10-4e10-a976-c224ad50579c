/**
 * <PERSON><PERSON><PERSON> to apply database migrations to Supabase
 * 
 * Usage: node scripts/apply-migration.js <migration-file>
 * Example: node scripts/apply-migration.js db/migrations/phase2_migration.sql
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service role key not found in environment variables.');
  console.error('Make sure you have a .env file with NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Get migration file path from command line arguments
const migrationFile = process.argv[2];

if (!migrationFile) {
  console.error('Error: No migration file specified.');
  console.error('Usage: node scripts/apply-migration.js <migration-file>');
  console.error('Example: node scripts/apply-migration.js db/migrations/phase2_migration.sql');
  process.exit(1);
}

// Resolve the migration file path
const migrationFilePath = path.resolve(process.cwd(), migrationFile);

// Check if the migration file exists
if (!fs.existsSync(migrationFilePath)) {
  console.error(`Error: Migration file not found: ${migrationFilePath}`);
  process.exit(1);
}

// Read the migration file
const migrationSql = fs.readFileSync(migrationFilePath, 'utf8');

// Split the migration into individual statements
const statements = migrationSql
  .split(';')
  .map(statement => statement.trim())
  .filter(statement => statement.length > 0);

// Apply the migration
async function applyMigration() {
  console.log(`Applying migration: ${migrationFile}`);
  console.log(`Found ${statements.length} SQL statements to execute.`);
  
  // Create a migration record
  const migrationName = path.basename(migrationFile, '.sql');
  const { error: recordError } = await supabase
    .from('migrations')
    .upsert([
      {
        name: migrationName,
        applied_at: new Date().toISOString(),
        status: 'in_progress'
      }
    ]);
    
  if (recordError) {
    console.error('Error creating migration record:', recordError);
    // Continue anyway
  }
  
  // Execute each statement
  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i];
    console.log(`Executing statement ${i + 1}/${statements.length}...`);
    
    try {
      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      
      if (error) {
        console.error(`Error executing statement ${i + 1}:`, error);
        
        // Update migration record with error
        await supabase
          .from('migrations')
          .update({
            status: 'failed',
            error_message: `Error in statement ${i + 1}: ${error.message}`
          })
          .eq('name', migrationName);
          
        process.exit(1);
      }
    } catch (error) {
      console.error(`Error executing statement ${i + 1}:`, error);
      
      // Update migration record with error
      await supabase
        .from('migrations')
        .update({
          status: 'failed',
          error_message: `Error in statement ${i + 1}: ${error.message}`
        })
        .eq('name', migrationName);
        
      process.exit(1);
    }
  }
  
  // Update migration record as completed
  const { error: updateError } = await supabase
    .from('migrations')
    .update({
      status: 'completed',
      error_message: null
    })
    .eq('name', migrationName);
    
  if (updateError) {
    console.error('Error updating migration record:', updateError);
    // Continue anyway
  }
  
  console.log('Migration completed successfully!');
}

// Check if migrations table exists, create if not
async function ensureMigrationsTable() {
  try {
    // Check if migrations table exists
    const { data, error } = await supabase
      .from('migrations')
      .select('name')
      .limit(1);
      
    if (error && error.code === '42P01') {
      // Table doesn't exist, create it
      console.log('Creating migrations table...');
      
      const createTableSql = `
        CREATE TABLE public.migrations (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL UNIQUE,
          applied_at TIMESTAMPTZ NOT NULL,
          status TEXT NOT NULL,
          error_message TEXT,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
      `;
      
      const { error: createError } = await supabase.rpc('exec_sql', { sql: createTableSql });
      
      if (createError) {
        console.error('Error creating migrations table:', createError);
        process.exit(1);
      }
      
      console.log('Migrations table created successfully.');
    } else if (error) {
      console.error('Error checking migrations table:', error);
      process.exit(1);
    }
    
    return true;
  } catch (error) {
    console.error('Error ensuring migrations table:', error);
    process.exit(1);
  }
}

// Main function
async function main() {
  try {
    await ensureMigrationsTable();
    await applyMigration();
  } catch (error) {
    console.error('Unhandled error:', error);
    process.exit(1);
  }
}

main();

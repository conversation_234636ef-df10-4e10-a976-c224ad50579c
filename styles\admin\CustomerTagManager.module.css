.tagManager {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.title {
  font-size: 1.1rem;
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.subtitle {
  font-size: 0.95rem;
  margin-top: 0;
  margin-bottom: 12px;
  color: #495057;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 0.9rem;
}

.success {
  background-color: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 0.9rem;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-size: 0.9rem;
}

.emptyState {
  text-align: center;
  padding: 16px;
  color: #6c757d;
  font-style: italic;
  background-color: #fff;
  border-radius: 4px;
  border: 1px dashed #ced4da;
  margin-bottom: 16px;
}

.currentTags {
  margin-bottom: 20px;
}

.tagList {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
  color: white;
  transition: opacity 0.2s;
}

.tag:hover {
  opacity: 0.9;
}

.tagName {
  margin-right: 6px;
}

.removeButton {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  line-height: 1;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.removeButton:hover {
  opacity: 1;
}

.removeButton:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.addTagSection {
  margin-top: 16px;
}

.availableTags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.addTagButton {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
  background-color: transparent;
  border: 1px solid;
  cursor: pointer;
  transition: all 0.2s;
}

.addTagButton:hover {
  opacity: 0.8;
}

.addTagButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.plusIcon {
  margin-right: 4px;
  font-weight: bold;
}

import { useState } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import MarketingStats from '@/components/admin/marketing/MarketingStats'
import CampaignAnalytics from '@/components/admin/marketing/CampaignAnalytics'
import EngagementAnalytics from '@/components/admin/marketing/EngagementAnalytics'
import styles from '@/styles/admin/marketing/Dashboard.module.css'

export default function MarketingDashboard() {
  const [period, setPeriod] = useState('month')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [customDateRange, setCustomDateRange] = useState(false)

  // Handle period change
  const handlePeriodChange = (newPeriod) => {
    setPeriod(newPeriod)
    setCustomDateRange(false)
  }

  // Handle custom date range
  const handleCustomDateRange = () => {
    if (startDate && endDate) {
      setCustomDateRange(true)
    }
  }

  return (
    <AdminLayout>
      <div className={styles.dashboard}>
        <div className={styles.header}>
          <h2>Marketing Dashboard</h2>

          <div className={styles.periodSelector}>
            <button
              className={`${styles.periodButton} ${!customDateRange && period === 'week' ? styles.periodButtonActive : ''}`}
              onClick={() => handlePeriodChange('week')}
            >
              Last Week
            </button>
            <button
              className={`${styles.periodButton} ${!customDateRange && period === 'month' ? styles.periodButtonActive : ''}`}
              onClick={() => handlePeriodChange('month')}
            >
              Last Month
            </button>
            <button
              className={`${styles.periodButton} ${!customDateRange && period === 'quarter' ? styles.periodButtonActive : ''}`}
              onClick={() => handlePeriodChange('quarter')}
            >
              Last Quarter
            </button>
            <button
              className={`${styles.periodButton} ${!customDateRange && period === 'year' ? styles.periodButtonActive : ''}`}
              onClick={() => handlePeriodChange('year')}
            >
              Last Year
            </button>
          </div>

          <div className={styles.dateRangeSelector}>
            <div className={styles.dateInputGroup}>
              <label htmlFor="start-date">From:</label>
              <input
                type="date"
                id="start-date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className={styles.dateInput}
              />
            </div>
            <div className={styles.dateInputGroup}>
              <label htmlFor="end-date">To:</label>
              <input
                type="date"
                id="end-date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className={styles.dateInput}
              />
            </div>
            <button
              className={styles.applyButton}
              onClick={handleCustomDateRange}
              disabled={!startDate || !endDate}
            >
              Apply
            </button>
          </div>
        </div>

        <div className={styles.dashboardContent}>
          <MarketingStats />

          <div className={styles.analyticsGrid}>
            <div className={styles.analyticsCard}>
              <CampaignAnalytics
                period={period}
                customDateRange={customDateRange}
                startDate={startDate}
                endDate={endDate}
              />
            </div>

            <div className={styles.analyticsCard}>
              <EngagementAnalytics
                period={period}
                customDateRange={customDateRange}
                startDate={startDate}
                endDate={endDate}
              />
            </div>
          </div>

          <div className={styles.quickLinks}>
            <h3>Quick Links</h3>
            <div className={styles.linksGrid}>
              <a href="/admin/marketing/campaigns/new" className={styles.linkCard}>
                <div className={styles.linkIcon}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M12 5v14M5 12h14"></path>
                  </svg>
                </div>
                <div className={styles.linkText}>
                  <h4>Create Campaign</h4>
                  <p>Create a new marketing campaign</p>
                </div>
              </a>

              <a href="/admin/marketing/automations/new" className={styles.linkCard}>
                <div className={styles.linkIcon}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg>
                </div>
                <div className={styles.linkText}>
                  <h4>Create Automation</h4>
                  <p>Set up automated marketing messages</p>
                </div>
              </a>

              <a href="/admin/marketing/templates/new" className={styles.linkCard}>
                <div className={styles.linkIcon}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                </div>
                <div className={styles.linkText}>
                  <h4>Create Template</h4>
                  <p>Design reusable message templates</p>
                </div>
              </a>

              <a href="/admin/marketing/segments/new" className={styles.linkCard}>
                <div className={styles.linkIcon}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                  </svg>
                </div>
                <div className={styles.linkText}>
                  <h4>Create Segment</h4>
                  <p>Define customer segments for targeting</p>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}

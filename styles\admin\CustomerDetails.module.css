.customerDetails {
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eaeaea;
}

.header h2 {
  font-size: 1.8rem;
  color: #333;
  margin: 0;
}

.actions {
  display: flex;
  gap: 12px;
}

.editButton {
  padding: 8px 16px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.editButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.deleteButton {
  padding: 8px 16px;
  background-color: transparent;
  color: #d32f2f;
  border: 1px solid #d32f2f;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.deleteButton:hover:not(:disabled) {
  background-color: rgba(211, 47, 47, 0.1);
}

.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.customerInfo {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.infoSection {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.infoSection h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 8px;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.infoLabel {
  font-size: 0.9rem;
  color: #666;
}

.infoValue {
  font-size: 1rem;
  color: #333;
}

.infoValue a {
  color: #6e8efb;
  text-decoration: none;
}

.infoValue a:hover {
  text-decoration: underline;
}

.notes {
  white-space: pre-line;
  color: #333;
  line-height: 1.5;
}

.preferencesList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preferenceItem {
  display: flex;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.preferenceKey {
  font-weight: 500;
  color: #555;
}

.preferenceValue {
  color: #333;
}

.marketingConsent {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.consentStatus {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
}

.consentGranted {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.consentDenied {
  background-color: rgba(211, 47, 47, 0.1);
  color: #d32f2f;
}

.consentDescription {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.notificationButton {
  display: inline-block;
  margin-top: 12px;
  padding: 8px 16px;
  background-color: #6e8efb;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notificationButton:hover:not(:disabled) {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.notificationButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.gdprButton {
  padding: 8px 16px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  color: #555;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.gdprButton:hover:not(:disabled) {
  background-color: #e5e5e5;
}

.gdprDescription {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.bookingHistory {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.modalContent {
  padding: 24px;
}

.modalContent h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.4rem;
  color: #333;
}

.modalContent p {
  margin-bottom: 24px;
  color: #555;
  line-height: 1.5;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.formGroup {
  margin-bottom: 16px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.input:focus {
  border-color: #6e8efb;
  outline: none;
}

.select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.select:focus {
  border-color: #6e8efb;
  outline: none;
}

.textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  resize: vertical;
  min-height: 100px;
}

.textarea:focus {
  border-color: #6e8efb;
  outline: none;
}

.sendButton {
  padding: 8px 16px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sendButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.sendButton:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #d32f2f;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #d32f2f;
}

.success {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #4caf50;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.1rem;
  color: #666;
}

@media (max-width: 992px) {
  .content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .infoGrid {
    grid-template-columns: 1fr;
  }

  .modalActions {
    flex-direction: column-reverse;
  }

  .modalActions button {
    width: 100%;
  }
}

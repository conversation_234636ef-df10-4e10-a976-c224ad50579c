import { isValidStatusTransition } from '@/lib/booking-status';
import { getAdminClient } from '@/lib/supabase';

/**
 * API endpoint for updating booking status
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 */
export default async function handler(req, res) {
  // Generate a request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 15);

  console.log(`[${requestId}] Processing ${req.method} request to /api/admin/bookings/status`);

  // Only allow PUT method
  if (req.method !== 'PUT') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get booking ID, new status, and notes from request body
    const { id, status, notes } = req.body;

    // Initialize Supabase admin client
    const supabase = getAdminClient();

    // Validate required fields
    if (!id || !status) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Get the booking to check current status
    const { data: booking, error: fetchError } = await supabase
      .from('bookings')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error(`[${requestId}] Error fetching booking:`, fetchError);
      return res.status(404).json({ error: 'Booking not found' });
    }

    // Validate status transition
    if (!isValidStatusTransition(booking.status, status)) {
      return res.status(400).json({
        error: `Invalid status transition from ${booking.status} to ${status}`
      });
    }

    // Update the booking status
    const { data: updatedBooking, error: updateError } = await supabase
      .from('bookings')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select();

    if (updateError) {
      console.error(`[${requestId}] Error updating booking status:`, updateError);
      return res.status(500).json({ error: 'Error updating booking status' });
    }

    // Record status history
    const { error: historyError } = await supabase
      .from('booking_status_history')
      .insert([{
        booking_id: id,
        previous_status: booking.status,
        new_status: status,
        notes: notes || null,
        changed_by: req.user?.id || process.env.SUPABASE_SERVICE_USER_ID
      }], {
        returning: 'minimal',
        defaultToNull: false
      });

    if (historyError) {
      console.error(`[${requestId}] Error recording status history:`, historyError);
      // Don't fail the request, just log the error
    }

    // Return the updated booking
    return res.status(200).json({ booking: updatedBooking[0] });
  } catch (error) {
    console.error(`[${requestId}] Unhandled error:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

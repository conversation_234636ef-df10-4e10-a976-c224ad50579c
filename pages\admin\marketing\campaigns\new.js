import { useState } from 'react'
import { useRouter } from 'next/router'
import AdminLayout from '@/components/admin/AdminLayout'
import CampaignForm from '@/components/admin/marketing/CampaignForm'
import styles from '@/styles/admin/marketing/CampaignCreate.module.css'

export default function NewCampaign() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [successMessage, setSuccessMessage] = useState(null)

  // Handle form submission
  const handleSubmit = async (campaignData) => {
    setLoading(true)
    setError(null)
    setSuccessMessage(null)

    try {
      // Create campaign
      const response = await fetch('/api/marketing/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(campaignData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create campaign')
      }

      const data = await response.json()
      setSuccessMessage('Campaign created successfully')

      // Redirect to campaign detail page after a short delay
      setTimeout(() => {
        router.push(`/admin/marketing/campaigns/${data.id}`)
      }, 1500)
    } catch (error) {
      console.error('Error creating campaign:', error)
      setError(error.message)
      setLoading(false)
    }
  }

  return (
    <AdminLayout>
      <div className={styles.campaignCreate}>
        <div className={styles.header}>
          <h2>Create Marketing Campaign</h2>
          <button
            className={styles.cancelButton}
            onClick={() => router.push('/admin/marketing/campaigns')}
            disabled={loading}
          >
            Cancel
          </button>
        </div>

        {error && (
          <div className={styles.error}>
            Error: {error}
          </div>
        )}

        {successMessage && (
          <div className={styles.success}>
            {successMessage}
          </div>
        )}

        <CampaignForm
          onSubmit={handleSubmit}
          onCancel={() => router.push('/admin/marketing/campaigns')}
        />
      </div>
    </AdminLayout>
  )
}

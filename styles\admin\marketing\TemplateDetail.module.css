.templateDetail {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.headerLeft h2 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  color: #333;
}

.meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  text-transform: capitalize;
}

.statusActive {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.statusInactive {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}

.type,
.category {
  font-size: 0.9rem;
  color: #666;
}

.headerActions {
  display: flex;
  gap: 10px;
}

.applyButton,
.editButton,
.deleteButton,
.backButton {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.applyButton {
  background-color: #4caf50;
  color: white;
  border: none;
  cursor: pointer;
}

.applyButton:hover:not(:disabled) {
  background-color: #43a047;
  transform: translateY(-1px);
}

.applyButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.editButton {
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
}

.editButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
  transform: translateY(-1px);
}

.deleteButton {
  background-color: transparent;
  color: #f44336;
  border: 1px solid #f44336;
  cursor: pointer;
}

.deleteButton:hover:not(:disabled) {
  background-color: rgba(244, 67, 54, 0.1);
  transform: translateY(-1px);
}

.deleteButton:disabled {
  color: #ccc;
  border-color: #ccc;
  cursor: not-allowed;
}

.backButton {
  background-color: #6e8efb;
  color: white;
  border: none;
  cursor: pointer;
  margin-top: 16px;
}

.backButton:hover {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.loading,
.error,
.notFound {
  text-align: center;
  padding: 40px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.error {
  color: #f44336;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.notFound {
  color: #666;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.templateInfo {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.infoSection {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.infoSection h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.infoLabel {
  font-size: 0.9rem;
  color: #666;
}

.infoValue {
  font-size: 1rem;
  color: #333;
}

.contentItem,
.previewItem {
  margin-bottom: 16px;
}

.contentItem:last-child,
.previewItem:last-child {
  margin-bottom: 0;
}

.contentLabel,
.previewLabel {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 8px;
}

.contentValue,
.previewValue {
  display: block;
  font-size: 1rem;
  color: #333;
}

.contentPre {
  background-color: rgba(255, 255, 255, 0.5);
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 16px;
  overflow: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  max-height: 300px;
}

.previewValue {
  background-color: rgba(255, 255, 255, 0.5);
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 16px;
}

.previewValue p {
  margin: 0 0 8px 0;
}

.previewValue p:last-child {
  margin-bottom: 0;
}

.emailPreview {
  font-family: Arial, sans-serif;
}

.deleteModal,
.applyModal {
  padding: 20px;
}

.deleteModal h3,
.applyModal h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
}

.deleteModal p,
.applyModal p {
  margin-bottom: 20px;
  color: #666;
}

.deleteError,
.applyError {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.applySuccess {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.deleteActions,
.applyActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancelDeleteButton,
.confirmDeleteButton,
.cancelApplyButton,
.confirmApplyButton {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelDeleteButton,
.cancelApplyButton {
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
}

.cancelDeleteButton:hover:not(:disabled),
.cancelApplyButton:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.confirmDeleteButton {
  background-color: #f44336;
  color: white;
  border: none;
}

.confirmDeleteButton:hover:not(:disabled) {
  background-color: #e53935;
}

.confirmApplyButton {
  background-color: #4caf50;
  color: white;
  border: none;
}

.confirmApplyButton:hover:not(:disabled) {
  background-color: #43a047;
}

.cancelDeleteButton:disabled,
.confirmDeleteButton:disabled,
.cancelApplyButton:disabled,
.confirmApplyButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.campaignsLoading {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.noCampaigns {
  text-align: center;
  padding: 20px;
}

.noCampaigns p {
  margin-bottom: 16px;
}

.createCampaignButton {
  display: inline-block;
  background-color: #6e8efb;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.createCampaignButton:hover {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.formGroup {
  margin-bottom: 20px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
}

.select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.2s ease;
}

.select:focus {
  outline: none;
  border-color: #6e8efb;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 16px;
  }
  
  .headerActions {
    width: 100%;
    flex-wrap: wrap;
  }
  
  .applyButton,
  .editButton,
  .deleteButton {
    flex: 1;
    text-align: center;
  }
  
  .infoGrid {
    grid-template-columns: 1fr;
  }
  
  .deleteActions,
  .applyActions {
    flex-direction: column;
  }
  
  .cancelDeleteButton,
  .confirmDeleteButton,
  .cancelApplyButton,
  .confirmApplyButton {
    width: 100%;
  }
}

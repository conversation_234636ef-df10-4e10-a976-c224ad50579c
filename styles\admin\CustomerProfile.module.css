.customerProfile {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 30px;
}

.loading {
  padding: 40px;
  text-align: center;
  color: #6c757d;
  font-size: 1rem;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 0.9rem;
}

.success {
  background-color: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 0.9rem;
}

.profileHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.customerInfo {
  flex: 1;
}

.customerName {
  font-size: 1.5rem;
  margin: 0 0 8px;
  color: #333;
}

.customerMeta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  color: #6c757d;
  font-size: 0.95rem;
}

.customerEmail,
.customerPhone {
  display: flex;
  align-items: center;
  gap: 6px;
}

.customerEmail::before {
  content: '✉️';
  font-size: 0.9rem;
}

.customerPhone::before {
  content: '📱';
  font-size: 0.9rem;
}

.customerActions {
  display: flex;
  gap: 10px;
}

.editButton,
.newBookingButton {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.editButton {
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #ced4da;
}

.editButton:hover {
  background-color: #e9ecef;
}

.newBookingButton {
  background-color: #6a0dad;
  color: white;
  border: none;
}

.newBookingButton:hover {
  background-color: #5a0b9d;
}

.customerStats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.statCard {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  transition: transform 0.2s;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.statValue {
  font-size: 1.5rem;
  font-weight: 600;
  color: #6a0dad;
  margin-bottom: 8px;
}

.statLabel {
  font-size: 0.85rem;
  color: #6c757d;
}

.tabsContainer {
  margin-top: 20px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 20px;
}

.tabButton {
  padding: 10px 16px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  font-size: 0.95rem;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s;
}

.tabButton:hover {
  color: #6a0dad;
}

.tabButton.active {
  color: #6a0dad;
  border-bottom-color: #6a0dad;
  font-weight: 500;
}

.tabContent {
  padding: 10px 0;
}

.sectionTitle {
  font-size: 1.1rem;
  color: #495057;
  margin-top: 0;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
}

.infoSection {
  margin-bottom: 24px;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.infoItem {
  margin-bottom: 12px;
}

.infoLabel {
  font-size: 0.85rem;
  color: #6c757d;
  margin-bottom: 4px;
}

.infoValue {
  font-size: 0.95rem;
  color: #212529;
}

.vipToggle {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
  background-color: #e9ecef;
  color: #6c757d;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.vipToggle.vipActive {
  background-color: #ffd700;
  color: #212529;
}

.vipToggle:hover {
  opacity: 0.9;
}

.emptyState {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.preferencesList {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.preferenceItem {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
}

.preferenceKey {
  font-size: 0.85rem;
  color: #6c757d;
  margin-bottom: 4px;
}

.preferenceValue {
  font-size: 0.95rem;
  color: #212529;
}

.notesText {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 16px;
  white-space: pre-wrap;
  font-size: 0.95rem;
  color: #212529;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .profileHeader {
    flex-direction: column;
    gap: 16px;
  }
  
  .customerActions {
    width: 100%;
  }
  
  .customerStats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .infoGrid {
    grid-template-columns: 1fr;
  }
  
  .preferencesList {
    grid-template-columns: 1fr;
  }
}

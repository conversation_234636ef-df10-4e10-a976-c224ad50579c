import React from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

const BreadcrumbSchema = () => {
  const router = useRouter();
  const path = router.asPath;
  
  // Skip breadcrumbs for homepage
  if (path === '/') {
    return null;
  }
  
  // Generate breadcrumb items based on the current path
  const pathSegments = path.split('/').filter(segment => segment);
  const breadcrumbItems = [
    { label: 'Home', path: '/' }
  ];
  
  let currentPath = '';
  pathSegments.forEach(segment => {
    currentPath += `/${segment}`;
    
    // Format the label (capitalize first letter, replace hyphens with spaces)
    const label = segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
    
    breadcrumbItems.push({ label, path: currentPath });
  });
  
  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbItems.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.label,
      "item": `https://www.oceansoulsparkles.com.au${item.path}`
    }))
  };
  
  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbData) }}
      />
    </Head>
  );
};

export default BreadcrumbSchema;

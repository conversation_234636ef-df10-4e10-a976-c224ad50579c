.campaignDetail {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.headerLeft h2 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  color: #333;
}

.meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  text-transform: capitalize;
}

.statusActive {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.statusScheduled {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.statusCompleted {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

.statusDraft {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}

.statusPaused {
  background-color: rgba(121, 85, 72, 0.1);
  color: #795548;
}

.statusCanceled {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.statusSent {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.type,
.dates {
  font-size: 0.9rem;
  color: #666;
}

.headerActions {
  display: flex;
  gap: 10px;
}

.editButton,
.deleteButton,
.backButton {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.editButton {
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
}

.editButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
  transform: translateY(-1px);
}

.deleteButton {
  background-color: transparent;
  color: #f44336;
  border: 1px solid #f44336;
  cursor: pointer;
}

.deleteButton:hover {
  background-color: rgba(244, 67, 54, 0.1);
  transform: translateY(-1px);
}

.backButton {
  background-color: #6e8efb;
  color: white;
  border: none;
  cursor: pointer;
  margin-top: 16px;
}

.backButton:hover {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.loading,
.error,
.notFound {
  text-align: center;
  padding: 40px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.error {
  color: #f44336;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.notFound {
  color: #666;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.campaignInfo {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.infoSection {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.infoSection h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.sectionHeader h3 {
  margin: 0;
}

.addMessageButton {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.addMessageButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.addMessageButton:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.infoLabel {
  font-size: 0.9rem;
  color: #666;
}

.infoValue {
  font-size: 1rem;
  color: #333;
}

.segmentLink {
  color: #6e8efb;
  text-decoration: none;
  transition: color 0.2s ease;
}

.segmentLink:hover {
  color: #5a7df9;
  text-decoration: underline;
}

.noMessages,
.noMetrics {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.messageList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.messageCard {
  background-color: rgba(255, 255, 255, 0.5);
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 16px;
  transition: all 0.2s ease;
}

.messageCard:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.messageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.messageSubject {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.messageStatus {
  font-size: 0.8rem;
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: capitalize;
}

.messageType {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 8px;
}

.messageContent {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 12px;
  white-space: pre-line;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 80px;
}

.messageMeta {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 12px;
}

.messageActions {
  display: flex;
  justify-content: flex-end;
}

.viewMessageButton {
  background-color: #6e8efb;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.viewMessageButton:hover {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.metricCard {
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 6px;
  padding: 16px;
  text-align: center;
  transition: all 0.2s ease;
}

.metricCard:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.metricValue {
  font-size: 1.8rem;
  font-weight: 600;
  color: #6e8efb;
  margin-bottom: 8px;
}

.metricPercent {
  font-size: 1rem;
  font-weight: normal;
  color: #666;
  margin-left: 4px;
}

.metricLabel {
  font-size: 0.9rem;
  color: #666;
}

.deleteModal {
  padding: 20px;
}

.deleteModal h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
}

.deleteModal p {
  margin-bottom: 20px;
  color: #666;
}

.deleteError {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.deleteActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancelDeleteButton,
.confirmDeleteButton {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelDeleteButton {
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
}

.cancelDeleteButton:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.confirmDeleteButton {
  background-color: #f44336;
  color: white;
  border: none;
}

.confirmDeleteButton:hover:not(:disabled) {
  background-color: #e53935;
}

.cancelDeleteButton:disabled,
.confirmDeleteButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.messageModal {
  padding: 20px;
}

.messageModal h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  color: #333;
}

.messageError {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.messageSuccess {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 16px;
  }
  
  .headerActions {
    width: 100%;
    flex-wrap: wrap;
  }
  
  .editButton,
  .deleteButton {
    flex: 1;
    text-align: center;
  }
  
  .infoGrid {
    grid-template-columns: 1fr;
  }
  
  .messageList {
    grid-template-columns: 1fr;
  }
  
  .metricsGrid {
    grid-template-columns: 1fr 1fr;
  }
}
